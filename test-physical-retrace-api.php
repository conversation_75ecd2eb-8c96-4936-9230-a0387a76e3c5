<?php

/**
 * Script de test pour l'API Physical Retrace
 * À exécuter dans le navigateur ou via PHP CLI
 */

// Configuration
$baseUrl = 'http://localhost/signsecure/public/api/v1';
$token = 'YOUR_SANCTUM_TOKEN_HERE'; // Remplacez par un vrai token

// Test 1: Créer un retrace directement
function testCreateRetrace($baseUrl, $token) {
    $url = $baseUrl . '/retraces';
    $data = [
        'date' => '2024-01-15',
        'reference' => 'TEST-REF-001',
        'subject' => 'Test de création de retrace via API',
        'notes' => 'Ceci est un test de l\'API Physical Retrace'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "=== Test 1: Créer un retrace ===\n";
    echo "URL: $url\n";
    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    return json_decode($response, true);
}

// Test 2: Demander un code OTP
function testRequestOtp($baseUrl, $token) {
    $url = $baseUrl . '/retraces/request-otp';
    $data = [
        'date' => '2024-01-15',
        'reference' => 'TEST-REF-002',
        'subject' => 'Test de demande OTP via API',
        'notes' => 'Ceci est un test de la demande OTP'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "=== Test 2: Demander un code OTP ===\n";
    echo "URL: $url\n";
    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    return json_decode($response, true);
}

// Test 3: Lister les retraces
function testListRetraces($baseUrl, $token) {
    $url = $baseUrl . '/retraces';

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPGET, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "=== Test 3: Lister les retraces ===\n";
    echo "URL: $url\n";
    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    return json_decode($response, true);
}

// Test 4: Tester sans authentification (doit échouer)
function testWithoutAuth($baseUrl) {
    $url = $baseUrl . '/retraces';

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPGET, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "=== Test 4: Sans authentification (doit échouer) ===\n";
    echo "URL: $url\n";
    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";
}

// Exécution des tests
echo "🧪 Tests de l'API Physical Retrace\n";
echo "=====================================\n\n";

if ($token === 'YOUR_SANCTUM_TOKEN_HERE') {
    echo "⚠️  ATTENTION: Remplacez 'YOUR_SANCTUM_TOKEN_HERE' par un vrai token Sanctum\n";
    echo "Pour obtenir un token, connectez-vous via l'API d'authentification\n\n";
    
    // Test sans authentification
    testWithoutAuth($baseUrl);
} else {
    // Tests avec authentification
    testCreateRetrace($baseUrl, $token);
    testRequestOtp($baseUrl, $token);
    testListRetraces($baseUrl, $token);
}

echo "✅ Tests terminés !\n";
echo "\n📝 Notes:\n";
echo "- Assurez-vous que votre serveur Laravel est démarré\n";
echo "- Vérifiez que la base de données est accessible\n";
echo "- Assurez-vous que les migrations ont été exécutées\n";
echo "- Vérifiez que les modèles et relations sont correctement définis\n";
