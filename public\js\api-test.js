/**
 * Fichier de test pour vérifier la communication API
 * À utiliser dans la console du navigateur pour tester les endpoints
 */

// Test de la classe API
window.testAPI = {
    // Test de connexion
    async testLogin() {
        try {
            console.log('🔐 Test de connexion...');
            const response = await signSecureAPI.login({ email: '<EMAIL>', password: 'password' });
            const token = response?.data?.token;
            if (token) {
                signSecureAPI.setToken(token);
                try { await signSecureAPI.createWebSession(); } catch(e) { console.warn('web-session fail', e); }
            }
            console.log('✅ Connexion réussie:', response);
            return response;
        } catch (error) {
            console.error('❌ Erreur de connexion:', error);
            return null;
        }
    },

    // Test d'inscription
    async testRegister() {
        try {
            console.log('📝 Test d\'inscription...');
            const response = await signSecureAPI.register({
                name: 'Test User',
                email: 'test' + Date.now() + '@example.com',
                password: 'password123',
                password_confirmation: 'password123'
            });
            console.log('✅ Inscription réussie:', response);
            return response;
        } catch (error) {
            console.error('❌ Erreur d\'inscription:', error);
            return null;
        }
    },

    // Test de récupération des informations utilisateur
    async testGetUser() {
        try {
            console.log('👤 Test de récupération utilisateur...');
            const response = await signSecureAPI.getUser();
            console.log('✅ Utilisateur récupéré:', response);
            return response;
        } catch (error) {
            console.error('❌ Erreur de récupération utilisateur:', error);
            return null;
        }
    },

    // Test de récupération des documents
    async testGetDocuments() {
        try {
            console.log('📄 Test de récupération des documents...');
            const response = await signSecureAPI.getDocuments();
            console.log('✅ Documents récupérés:', response);
            return response;
        } catch (error) {
            console.error('❌ Erreur de récupération des documents:', error);
            return null;
        }
    },

    // Test de génération OTP
    async testGenerateOTP(documentId = 1) {
        try {
            console.log('🔑 Test de génération OTP...');
            const response = await signSecureAPI.generateOTP(documentId, 'email');
            console.log('✅ OTP généré:', response);
            return response;
        } catch (error) {
            console.error('❌ Erreur de génération OTP:', error);
            return null;
        }
    },

    // Test de vérification OTP
    async testVerifyOTP(documentId = 1, code = '123456') {
        try {
            console.log('✅ Test de vérification OTP...');
            const response = await signSecureAPI.verifyOTP(documentId, code);
            console.log('✅ OTP vérifié:', response);
            return response;
        } catch (error) {
            console.error('❌ Erreur de vérification OTP:', error);
            return null;
        }
    },

    // Test de capture de signature
    async testCaptureSignature() {
        try {
            console.log('✍️ Test de capture de signature...');
            // Créer une signature test en base64
            const testSignature = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
            const response = await signSecureAPI.captureSignature(testSignature);
            console.log('✅ Signature capturée:', response);
            return response;
        } catch (error) {
            console.error('❌ Erreur de capture de signature:', error);
            return null;
        }
    },

    // Test de vérification de document
    async testVerifyDocument(trackingCode = 'TEST123456789') {
        try {
            console.log('🔍 Test de vérification de document...');
            const response = await signSecureAPI.verifyDocument(trackingCode);
            console.log('✅ Document vérifié:', response);
            return response;
        } catch (error) {
            console.error('❌ Erreur de vérification de document:', error);
            return null;
        }
    },

    // Test complet de l'API
    async runAllTests() {
        console.log('🚀 Démarrage des tests API...');
        console.log('=====================================');

        // Test sans authentification
        await this.testRegister();
        await this.testLogin();

        // Test avec authentification
        if (signSecureAPI.token) {
            await this.testGetUser();
            await this.testGetDocuments();
            await this.testGenerateOTP();
            await this.testVerifyOTP();
            await this.testCaptureSignature();
            await this.testVerifyDocument();
        } else {
            console.log('⚠️ Token d\'authentification manquant, certains tests sont ignorés');
        }

        console.log('=====================================');
        console.log('🏁 Tests terminés !');
    },

    // Vérifier l'état de l'API
    checkAPIStatus() {
        console.log('🔍 État de l\'API:');
        console.log('- Base URL:', signSecureAPI.baseURL);
        console.log('- Token:', signSecureAPI.token ? '✅ Présent' : '❌ Absent');
        console.log('- Token dans localStorage:', localStorage.getItem('auth_token') ? '✅ Présent' : '❌ Absent');
        
        // Test de connectivité
        fetch(signSecureAPI.baseURL + '/verification/stats')
            .then(response => {
                console.log('- Connectivité API:', response.ok ? '✅ OK' : '❌ Erreur ' + response.status);
            })
            .catch(error => {
                console.log('- Connectivité API:', '❌ Erreur de connexion');
            });
    }
};

// Instructions d'utilisation
console.log(`
🚀 API Test Helper chargé !

Utilisez ces commandes dans la console :

🔍 Vérifier l'état de l'API :
   testAPI.checkAPIStatus()

🧪 Lancer tous les tests :
   testAPI.runAllTests()

📝 Tests individuels :
   testAPI.testRegister()
   testAPI.testLogin()
   testAPI.testGetUser()
   testAPI.testGetDocuments()
   testAPI.testGenerateOTP()
   testAPI.testVerifyOTP()
   testAPI.testCaptureSignature()
   testAPI.testVerifyDocument()

💡 Conseil : Commencez par testAPI.checkAPIStatus() pour vérifier la configuration
`);
