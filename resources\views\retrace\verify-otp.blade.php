@extends('layouts.app')

@section('content')
<div class="container mx-auto max-w-md">
    <!-- Header avec icône -->
    <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full mb-4">
            <i class="fas fa-shield-alt text-white text-2xl"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Vérification OTP</h1>
        <p class="text-gray-600 text-lg">Un code de sécurité a été envoyé par la méthode choisie (email ou SMS).</p>
    </div>

    <!-- Formulaire dans une carte -->
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
        <div class="text-center mb-6">
            <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                <i class="fas fa-key text-blue-600 text-lg"></i>
            </div>
            <p class="text-gray-700">Saisissez le code reçu (email ou SMS).</p>
        </div>

        <form method="POST" action="{{ route('retrace.verify-otp.post') }}" class="space-y-6">
            @csrf
            
            <div class="space-y-2">
                <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">
                    <i class="fas fa-key text-blue-500 mr-2"></i>Code de vérification
                </label>
                <input type="text" name="code" placeholder="Ex: 123456" maxlength="6"
                       class="w-full px-4 py-4 text-center text-2xl font-mono border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 tracking-widest" 
                       required />
                @error('code')
                <div class="text-red-600 text-sm mt-2 text-center">
                    <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                </div>
                @enderror
            </div>

            <!-- Bouton de soumission -->
            <div class="pt-4">
                <button type="submit" 
                        class="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white font-semibold py-4 px-6 rounded-xl hover:from-green-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <i class="fas fa-check mr-2"></i>
                    Vérifier et créer le retrace
                </button>
            </div>

            <!-- Info sur le processus -->
            <div class="mt-6 p-4 bg-green-50 rounded-xl border border-green-200">
                <div class="flex items-start">
                    <i class="fas fa-lightbulb text-green-500 mt-1 mr-3"></i>
                    <div class="text-sm text-green-700">
                        <p class="font-medium mb-1">Prochaine étape</p>
                        <p>Une fois le code validé, un PDF sera généré avec toutes les informations saisies et un code de suivi unique.</p>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection


