@extends('layouts.app')

@section('title', 'Téléverser un document - SignSecure')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">Téléverser un document</h1>
                    <p class="text-muted mb-0">Sélectionnez un fichier PDF à signer électroniquement</p>
                </div>
                <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Retour aux documents
                </a>
            </div>
            
            <!-- Upload Form -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-body p-5">
                            <form method="POST" action="{{ route('documents.store') }}" enctype="multipart/form-data" id="uploadForm">
                                @csrf
                                
                                <!-- File Upload Area -->
                                <div class="mb-4">
                                    <label class="form-label fw-semibold mb-3">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                        Document PDF à signer
                                    </label>
                                    
                                    <div class="upload-area border-2 border-dashed rounded-3 p-5 text-center position-relative" id="uploadArea">
                                        <input 
                                            type="file" 
                                            name="document" 
                                            id="documentFile" 
                                            class="d-none" 
                                            accept=".pdf"
                                            required
                                        >
                                        
                                        <div id="uploadPrompt">
                                            <div class="mb-3">
                                                <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 3rem;"></i>
                                            </div>
                                            <h5 class="mb-2">Glissez-déposez votre fichier PDF ici</h5>
                                            <p class="text-muted mb-3">ou</p>
                                           <button type="button" class="btn btn-primary" id="browseButton">
                                            <i class="fas fa-folder-open me-2"></i>
                                            Parcourir les fichiers
                                        </button>


                                            <div class="mt-3">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Formats acceptés : PDF uniquement • Taille max : 10 MB
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div id="filePreview" class="d-none">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="me-3">
                                                    <i class="fas fa-file-pdf text-danger" style="font-size: 2rem;"></i>
                                                </div>
                                                <div class="flex-grow-1 text-start">
                                                    <h6 class="mb-1" id="fileName"></h6>
                                                    <small class="text-muted" id="fileSize"></small>
                                                </div>
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeFile()">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            
                                            <!-- Upload Progress -->
                                            <div class="mt-3 d-none" id="uploadProgress">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                                         role="progressbar" 
                                                         style="width: 0%" 
                                                         id="progressBar">
                                                    </div>
                                                </div>
                                                <small class="text-muted mt-1 d-block">Téléversement en cours...</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    @error('document')
                                        <div class="text-danger mt-2">
                                            <small><i class="fas fa-exclamation-circle me-1"></i>{{ $message }}</small>
                                        </div>
                                    @enderror
                                </div>
                                
                                <!-- File Information -->
                                <div class="row mb-4" id="fileInfo" style="display: none;">
                                    <div class="col-md-6">
                                        <div class="bg-light rounded-3 p-3">
                                            <h6 class="mb-2">
                                                <i class="fas fa-info-circle text-info me-2"></i>
                                                Informations du fichier
                                            </h6>
                                            <div class="small">
                                                <div class="d-flex justify-content-between mb-1">
                                                    <span>Nom :</span>
                                                    <span id="infoFileName" class="fw-semibold"></span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-1">
                                                    <span>Taille :</span>
                                                    <span id="infoFileSize"></span>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span>Type :</span>
                                                    <span class="text-danger fw-semibold">PDF</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="bg-light rounded-3 p-3">
                                            <h6 class="mb-2">
                                                <i class="fas fa-shield-alt text-success me-2"></i>
                                                Sécurité
                                            </h6>
                                            <div class="small">
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-check text-success me-2"></i>
                                                    <span>Chiffrement SSL/TLS</span>
                                                </div>
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-check text-success me-2"></i>
                                                    <span>Stockage sécurisé</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check text-success me-2"></i>
                                                    <span>Accès restreint</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- OTP Method Selection -->
                                <div class="mb-4" id="otpMethod" style="display: none;">
                                    <label class="form-label fw-semibold mb-3">
                                        <i class="fas fa-shield-alt text-success me-2"></i>
                                        Méthode de vérification OTP
                                    </label>
                                    
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="form-check card h-100">
                                                <div class="card-body d-flex align-items-center">
                                                    <input 
                                                        class="form-check-input me-3" 
                                                        type="radio" 
                                                        name="otp_method" 
                                                        id="otpEmail" 
                                                        value="email" 
                                                        checked
                                                    >
                                                    <div>
                                                        <label class="form-check-label fw-semibold" for="otpEmail">
                                                            <i class="fas fa-envelope text-primary me-2"></i>
                                                            Par e-mail
                                                        </label>
                                                        <div class="small text-muted">
                                                            Code envoyé à {{ Auth::user()->email }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        @if(Auth::user()->phone)
                                        <div class="col-md-6">
                                            <div class="form-check card h-100">
                                                <div class="card-body d-flex align-items-center">
                                                    <input 
                                                        class="form-check-input me-3" 
                                                        type="radio" 
                                                        name="otp_method" 
                                                        id="otpSms" 
                                                        value="sms"
                                                    >
                                                    <div>
                                                        <label class="form-check-label fw-semibold" for="otpSms">
                                                            <i class="fas fa-sms text-success me-2"></i>
                                                            Par SMS
                                                        </label>
                                                        <div class="small text-muted">
                                                            Code envoyé au {{ Auth::user()->phone }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @else
                                        <div class="col-md-6">
                                            <div class="card h-100 bg-light">
                                                <div class="card-body d-flex align-items-center">
                                                    <div class="text-muted">
                                                        <i class="fas fa-sms me-2"></i>
                                                        <span class="fw-semibold">Par SMS</span>
                                                        <div class="small">
                                                            <a href="{{ route('profile.edit') }}" class="text-decoration-none">
                                                                Ajoutez un numéro de téléphone
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                        <i class="fas fa-upload me-2"></i>
                                        Téléverser et continuer
                                    </button>
                                </div>
                                
                                <!-- Help Text -->
                                <div class="mt-4 p-3 bg-light rounded-3">
                                    <h6 class="mb-2">
                                        <i class="fas fa-question-circle text-info me-2"></i>
                                        Étapes suivantes
                                    </h6>
                                    <ol class="small mb-0 ps-3">
                                        <li>Téléversement sécurisé de votre document PDF</li>
                                        <li>Réception d'un code de vérification OTP</li>
                                        <li>Saisie du code pour accéder à la prévisualisation</li>
                                        <li>Positionnement et application de votre signature</li>
                                        <li>Téléchargement du document signé</li>
                                    </ol>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script src="{{ asset('js/document-upload.js') }}"></script>
@endpush

@push('styles')
<style>
.upload-area {
    border-color: #e9ecef !important;
    background-color: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--ss-primary) !important;
    background-color: #f0f0ff;
}

.upload-area.border-primary {
    border-color: var(--ss-primary) !important;
    background-color: #f0f0ff !important;
}

.upload-area.border-success {
    border-color: var(--ss-success) !important;
    background-color: #f0fff4 !important;
}

.form-check.card {
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
}

.form-check.card:hover {
    border-color: var(--ss-primary);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

.form-check-input:checked + .card,
.form-check-input:checked ~ .card {
    border-color: var(--ss-primary);
    background-color: #f0f0ff;
}

.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}
@media (max-width: 768px) {
    .upload-area {
        padding: 2rem !important;
    }
    
    .card-body {
        padding: 2rem !important;
    }
}
</style>
@endpush
@endsection


