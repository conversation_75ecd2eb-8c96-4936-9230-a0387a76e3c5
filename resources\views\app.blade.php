<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-bs-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="SignSecure - Plateforme sécurisée de signature électronique de documents">
    
    <title>@yield('title', 'SignSecure - Signature Électronique Sécurisée')</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('images/favicon.ico') }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Feather Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/feather-icons@4.29.0/dist/feather.min.css">
    
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- Custom CSS -->
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    
    @stack('styles')
    
    <style>
        :root {
            --ss-primary: #6366f1;
            --ss-primary-dark: #4f46e5;
            --ss-secondary: #64748b;
            --ss-success: #10b981;
            --ss-danger: #ef4444;
            --ss-warning: #f59e0b;
            --ss-info: #3b82f6;
            --ss-light: #f8fafc;
            --ss-dark: #1e293b;
            --ss-border: #e2e8f0;
            --ss-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --ss-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--ss-light);
            color: var(--ss-dark);
            line-height: 1.6;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--ss-primary) !important;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--ss-primary) 0%, var(--ss-primary-dark) 100%);
            min-height: 100vh;
            box-shadow: var(--ss-shadow-lg);
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
        }
        
        .main-content {
            background-color: white;
            border-radius: 1rem;
            box-shadow: var(--ss-shadow);
            margin: 1.5rem;
            padding: 2rem;
            min-height: calc(100vh - 3rem);
        }
        
        .card {
            border: none;
            box-shadow: var(--ss-shadow);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: var(--ss-shadow-lg);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--ss-primary) 0%, var(--ss-primary-dark) 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--ss-shadow-lg);
        }
        
        .form-control {
            border: 2px solid var(--ss-border);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--ss-primary);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }
        
        .alert {
            border: none;
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
        }
        
        .progress {
            height: 0.5rem;
            border-radius: 0.25rem;
            background-color: var(--ss-border);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, var(--ss-primary) 0%, var(--ss-success) 100%);
        }
        
        .signature-pad {
            border: 2px dashed var(--ss-border);
            border-radius: 0.75rem;
            background-color: #fafafa;
            cursor: crosshair;
            transition: all 0.3s ease;
        }
        
        .signature-pad:hover {
            border-color: var(--ss-primary);
            background-color: #f0f0ff;
        }
        
        .pdf-viewer {
            border: 1px solid var(--ss-border);
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: var(--ss-shadow);
        }
        
        /* Assure que les modales passent au-dessus du backdrop */
        .modal { z-index: 1060; }
        .modal-backdrop { z-index: 1050; }
        
        .otp-input {
            width: 3rem;
            height: 3rem;
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            border: 2px solid var(--ss-border);
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }
        
        .otp-input:focus {
            border-color: var(--ss-primary);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }
        
        .status-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-uploaded {
            background-color: #dbeafe;
            color: #1e40af;
        }
        
        .status-signed {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-processing {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .tracking-code {
            font-family: 'Courier New', monospace;
            background-color: var(--ss-light);
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: 1px solid var(--ss-border);
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -100%;
                width: 280px;
                z-index: 1050;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
            
            .main-content {
                margin: 1rem;
                padding: 1rem;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }
    </style>
</head>
<body>
    <div id="app">
        @auth
            <div class="container-fluid">
                <div class="row">
                    <!-- Sidebar -->
                    <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebarMenu">
                        <div class="position-sticky pt-3">
                            <!-- Logo -->
                            <div class="text-center mb-4">
                                <h3 class="text-white fw-bold">
                                    <i class="fas fa-signature me-2"></i>
                                    SignSecure
                                </h3>
                                <p class="text-white-50 small mb-0">Signature Électronique</p>
                            </div>
                            
                            <!-- User Info -->
                            <div class="text-center mb-4 pb-3 border-bottom border-white border-opacity-25">
                                @php
                                    $userName = Auth::user()->name ?? '';
                                    $parts = preg_split('/\s+/', trim($userName));
                                    $initials = '';
                                    foreach ($parts as $idx => $part) {
                                        if ($idx >= 2) break;
                                        $initials .= mb_strtoupper(mb_substr($part, 0, 1));
                                    }
                                    if ($initials === '') {
                                        $email = Auth::user()->email ?? 'U';
                                        $initials = mb_strtoupper(mb_substr($email, 0, 1));
                                    }
                                @endphp
                                <div class="rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px; background-color: rgba(255,255,255,0.15); border: 1px solid rgba(255,255,255,0.35);">
                                    <span class="text-white fw-bold" style="font-size: 1.2rem; letter-spacing: 1px;">{{ $initials }}</span>
                                </div>
                                <h6 class="text-white mt-2 mb-0">{{ Auth::user()->name }}</h6>
                                <small class="text-white-50">{{ Auth::user()->email }}</small>
                            </div>
                            
                            <!-- Navigation -->
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                                        <i class="fas fa-tachometer-alt"></i>
                                        Tableau de bord
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('documents.*') ? 'active' : '' }}" href="{{ route('documents.index') }}">
                                        <i class="fas fa-file-pdf"></i>
                                        Mes documents
                                    </a>
                                </li>
                                
                               
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('profile.show') }}">
                                        <i class="fas fa-user-cog"></i>
                                        Profil
                                    </a>
                                </li>
                                <li class="nav-item mt-3">
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="nav-link border-0 bg-transparent w-100 text-start">
                                            <i class="fas fa-sign-out-alt"></i>
                                            Déconnexion
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </nav>
                    
                    <!-- Main content -->
                    <main class="col-md-9 ms-sm-auto col-lg-10 px-0">
                        <!-- Mobile menu button -->
                        <div class="d-md-none p-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                        
                        <div class="main-content fade-in">
                            @yield('content')
                        </div>
                    </main>
                </div>
            </div>
        @else
            <!-- Guest layout -->
            <div class="min-vh-100 d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, var(--ss-primary) 0%, var(--ss-primary-dark) 100%);">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-6 col-lg-5">
                            <div class="card shadow-lg border-0 rounded-4">
                                <div class="card-body p-5">
                                    <div class="text-center mb-4">
                                        <h2 class="fw-bold text-primary">
                                            <i class="fas fa-signature me-2"></i>
                                            SignSecure
                                        </h2>
                                        <p class="text-muted">Plateforme sécurisée de signature électronique</p>
                                    </div>
                                    
                                    @yield('content')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endauth
    </div>
    
    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <!-- Toasts will be inserted here -->
    </div>
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background-color: rgba(0,0,0,0.5); z-index: 9999;">
        <div class="d-flex align-items-center justify-content-center h-100">
            <div class="text-center text-white">
                <div class="spinner-border mb-3" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p>Traitement en cours...</p>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <!-- Bootstrap JS (bundle incl. Popper) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script type="module" src="{{ asset('js/app.js') }}"></script>
    
    <script>
        // Configuration globale
        window.SignSecure = {
            csrfToken: '{{ csrf_token() }}',
            baseUrl: '{{ url('/') }}',
           user: @auth {!! Auth::user()->toJson() !!} @else null @endauth

        };
        
        // Configuration Toastr
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
        
        // Affichage des messages Flash
        @if(session('success'))
            toastr.success('{{ session('success') }}');
        @endif
        
        @if(session('error'))
            toastr.error('{{ session('error') }}');
        @endif
        
        @if(session('warning'))
            toastr.warning('{{ session('warning') }}');
        @endif
        
        @if(session('info'))
            toastr.info('{{ session('info') }}');
        @endif
        
        // Gestion des erreurs de validation
        @if($errors->any())
            @foreach($errors->all() as $error)
                toastr.error('{{ $error }}');
            @endforeach
        @endif
        
        // Fonctions utilitaires
        function showLoading() {
            document.getElementById('loadingOverlay').classList.remove('d-none');
        }
        
        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('d-none');
        }
        
        function confirmAction(message, callback) {
            Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#6366f1',
                cancelButtonColor: '#ef4444',
                confirmButtonText: 'Oui, continuer',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed && callback) {
                    callback();
                }
            });
        }
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Mobile sidebar toggle
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.querySelector('[data-bs-toggle="collapse"]');
            const sidebar = document.getElementById('sidebarMenu');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
                
                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(e) {
                    if (window.innerWidth < 768 && 
                        !sidebar.contains(e.target) && 
                        !sidebarToggle.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                });
            }
        });
    </script>
    
    @stack('scripts')
</body>
</html>

