<?php

namespace App\Services;

use App\Models\AuditLog;

class AuditLogger
{
    public static function log(string $action, ?string $description = null): void
    {
        try {
            AuditLog::create([
                'user_id' => auth()->check() ? auth()->id() : null,
                'action' => $action,
                'description' => $description,
                'ip_address' => request()->ip(),
                'user_agent' => request()->header('User-Agent'),
            ]);
        } catch (\Throwable $e) {
            // Best-effort logging; ne pas bloquer le flux
        }
    }
}


