<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PhysicalRetrace;
use App\Services\PDFService;
use App\Services\OTPService;
use App\Http\Requests\API\PhysicalRetraceCreateRequest;
use App\Http\Requests\API\PhysicalRetraceOtpRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class PhysicalRetraceController extends Controller
{
    public function __construct(
        private PDFService $pdfService, 
        private OTPService $otpService
    ) {
        $this->middleware('auth:sanctum');
    }

    /**
     * Créer une demande de retrace physique
     */
    public function store(PhysicalRetraceCreateRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = $request->user();

        // Utiliser le service de génération de codes de suivi
        $trackingCode = \App\Services\TrackingCodeService::generateRetraceCode();

        $retrace = PhysicalRetrace::create([
            'user_id' => $user->id,
            'tracking_code' => $trackingCode,
            'date' => $validated['date'],
            'reference' => $validated['reference'],
            'subject' => $validated['subject'],
            'notes' => $validated['notes'] ?? null,
        ]);

        // Générer un PDF blanc contenant uniquement le code de suivi
        $pdfPath = $this->pdfService->generateBlankWithTrackingCodeOnly(
            $retrace->tracking_code
        );

        $retrace->update(['pdf_path' => $pdfPath]);

        return response()->json([
            'success' => true,
            'message' => 'Retrace créé avec succès',
            'data' => [
                'id' => $retrace->id,
                'tracking_code' => $retrace->tracking_code,
                'date' => $retrace->date,
                'reference' => $retrace->reference,
                'subject' => $retrace->subject,
                'notes' => $retrace->notes,
                'created_at' => $retrace->created_at->toIso8601String(),
                'pdf_path' => $retrace->pdf_path,
            ]
        ], 201);
    }

    /**
     * Demander un code OTP pour la création d'un retrace
     */
    public function requestOtp(PhysicalRetraceCreateRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = $request->user();

        // Envoyer OTP pour retrace selon préférence (sms/email)
        try {
            $type = $request->input('otp_method', 'email');
            $this->otpService->generateAndSendForRetrace($user, $type);
            
            return response()->json([
                'success' => true,
                'message' => "Code OTP envoyé par {$type}.",
                'data' => [
                    'form_data' => [
                        'date' => $validated['date'],
                        'reference' => $validated['reference'],
                        'subject' => $validated['subject'],
                        'notes' => $validated['notes'] ?? null,
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'envoi du code OTP',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Vérifier un code OTP et créer le retrace
     */
    public function verifyOtpAndCreate(PhysicalRetraceOtpRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = $request->user();
        $valid = $this->otpService->verifyForRetrace($user, $validated['code']);

        if (!$valid) {
            return response()->json([
                'success' => false,
                'message' => 'Code OTP invalide ou expiré',
                'errors' => ['code' => ['Code OTP invalide ou expiré']]
            ], 422);
        }

        // Utiliser le service de génération de codes de suivi
        $trackingCode = \App\Services\TrackingCodeService::generateRetraceCode();

        $retrace = PhysicalRetrace::create([
            'user_id' => $user->id,
            'tracking_code' => $trackingCode,
            'date' => $validated['date'],
            'reference' => $validated['reference'],
            'subject' => $validated['subject'],
            'notes' => $validated['notes'] ?? null,
        ]);

        // Générer un PDF blanc contenant uniquement le code de suivi
        $pdfPath = $this->pdfService->generateBlankWithTrackingCodeOnly(
            $retrace->tracking_code
        );

        $retrace->update(['pdf_path' => $pdfPath]);

        return response()->json([
            'success' => true,
            'message' => 'Retrace créé avec succès après vérification OTP',
            'data' => [
                'id' => $retrace->id,
                'tracking_code' => $retrace->tracking_code,
                'date' => $retrace->date,
                'reference' => $retrace->reference,
                'subject' => $retrace->subject,
                'notes' => $retrace->notes,
                'created_at' => $retrace->created_at->toIso8601String(),
                'pdf_path' => $retrace->pdf_path,
            ]
        ], 201);
    }

    /**
     * Afficher un retrace spécifique
     */
    public function show(PhysicalRetrace $retrace): JsonResponse
    {
        $this->authorize('view', $retrace);

        return response()->json([
            'success' => true,
            'message' => 'Retrace récupéré avec succès',
            'data' => [
                'id' => $retrace->id,
                'tracking_code' => $retrace->tracking_code,
                'date' => $retrace->date,
                'reference' => $retrace->reference,
                'subject' => $retrace->subject,
                'notes' => $retrace->notes,
                'created_at' => $retrace->created_at->toIso8601String(),
                'updated_at' => $retrace->updated_at->toIso8601String(),
                'pdf_path' => $retrace->pdf_path,
            ]
        ]);
    }

    /**
     * Lister tous les retraces de l'utilisateur
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $retraces = $user->physicalRetraces()
            ->latest()
            ->paginate($request->input('per_page', 15));

        $data = $retraces->getCollection()->map(function ($retrace) {
            return [
                'id' => $retrace->id,
                'tracking_code' => $retrace->tracking_code,
                'date' => $retrace->date,
                'reference' => $retrace->reference,
                'subject' => $retrace->subject,
                'created_at' => $retrace->created_at->toIso8601String(),
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'Retraces récupérés avec succès',
            'data' => $data,
            'pagination' => [
                'current_page' => $retraces->currentPage(),
                'last_page' => $retraces->lastPage(),
                'per_page' => $retraces->perPage(),
                'total' => $retraces->total(),
            ]
        ]);
    }

    /**
     * Télécharger le PDF d'un retrace
     */
    public function download(PhysicalRetrace $retrace): JsonResponse
    {
        $this->authorize('view', $retrace);

        if (!$retrace->pdf_path) {
            return response()->json([
                'success' => false,
                'message' => 'PDF non disponible pour ce retrace'
            ], 404);
        }

        // Retourner le chemin du fichier pour téléchargement
        return response()->json([
            'success' => true,
            'message' => 'PDF disponible pour téléchargement',
            'data' => [
                'download_url' => route('api.retraces.download', $retrace),
                'filename' => 'retrace_' . $retrace->tracking_code . '.pdf',
                'tracking_code' => $retrace->tracking_code,
            ]
        ]);
    }

    /**
     * Supprimer un retrace
     */
    public function destroy(PhysicalRetrace $retrace): JsonResponse
    {
        $this->authorize('delete', $retrace);

        // Supprimer le fichier PDF associé s'il existe
        if ($retrace->pdf_path) {
            Storage::disk('private')->delete($retrace->pdf_path);
        }

        $retrace->delete();

        return response()->json([
            'success' => true,
            'message' => 'Retrace supprimé avec succès'
        ]);
    }
}
