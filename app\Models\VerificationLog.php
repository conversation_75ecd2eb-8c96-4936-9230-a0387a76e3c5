<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VerificationLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'tracking_code',
        'ip_address',
        'user_agent',
        'verification_result',
        'verification_details',
        'verified_at',
    ];

    protected $casts = [
        'verification_details' => 'array',
        'verified_at' => 'datetime',
    ];

    /**
     * Relation avec le document via le tracking code
     */
    public function document()
    {
        return $this->belongsTo(Document::class, 'tracking_code', 'tracking_code');
    }

    /**
     * Scope pour les vérifications réussies
     */
    public function scopeSuccessful($query)
    {
        return $query->where('verification_result', 'success');
    }

    /**
     * Scope pour les vérifications échouées
     */
    public function scopeFailed($query)
    {
        return $query->where('verification_result', 'failed');
    }

    /**
     * Scope pour les vérifications expirées
     */
    public function scopeExpired($query)
    {
        return $query->where('verification_result', 'expired');
    }
}
