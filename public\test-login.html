<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login API - SignSecure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Test Login API - SignSecure</h1>
    
    <div class="test-section info">
        <h3>Instructions</h3>
        <p>Cette page teste la route de login API pour vérifier qu'elle est accessible.</p>
        <p>Assurez-vous que le serveur Laravel est démarré avec: <code>php artisan serve</code></p>
    </div>

    <div class="test-section">
        <h3>Test 1: Vérification de la route de login</h3>
        <button onclick="testLoginRoute()">Tester la route de login</button>
        <div id="result1"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Test de login avec credentials</h3>
        <button onclick="testLoginCredentials()">Tester avec credentials</button>
        <div id="result2"></div>
    </div>

    <div class="test-section">
        <h3>Logs de débogage</h3>
        <div id="logs"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function displayResult(containerId, data, isSuccess = true) {
            const container = document.getElementById(containerId);
            container.className = `test-section ${isSuccess ? 'success' : 'error'}`;
            container.innerHTML = `
                <h3>${isSuccess ? '✅ Succès' : '❌ Erreur'}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        async function testLoginRoute() {
            try {
                log('🧪 Test de la route de login...');
                
                // Test avec OPTIONS pour vérifier l'existence
                const response = await fetch('/api/v1/auth/login', { 
                    method: 'OPTIONS',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = {
                    status: response.status,
                    ok: response.ok,
                    accessible: response.status !== 404,
                    headers: Object.fromEntries(response.headers.entries())
                };
                
                if (response.status !== 404) {
                    log('✅ Route de login accessible', 'success');
                    displayResult('result1', result, true);
                } else {
                    log('❌ Route de login introuvable', 'error');
                    displayResult('result1', result, false);
                }
                
            } catch (error) {
                log(`❌ Erreur lors du test de la route: ${error.message}`, 'error');
                displayResult('result1', { error: error.message }, false);
            }
        }

        async function ensureCsrfCookie() {
            // Get XSRF-TOKEN cookie from Sanctum
            await fetch('/sanctum/csrf-cookie', {
                method: 'GET',
                credentials: 'same-origin'
            });
        }

        function readCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return '';
        }

        function decodeXsrf(token) {
            try { return decodeURIComponent(token || ''); } catch { return token || ''; }
        }

        async function testLoginCredentials() {
            try {
                log('🧪 Test de login avec credentials...');
                
                const credentials = {
                    email: '<EMAIL>',
                    password: 'password'
                };
                
                // Ensure CSRF cookie is set for Sanctum/VerifyCsrfToken
                await ensureCsrfCookie();
                const xsrfCookie = readCookie('XSRF-TOKEN');
                const xsrfHeader = decodeXsrf(xsrfCookie);

                const response = await fetch('/api/v1/auth/login', { 
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        // Sanctum expects X-XSRF-TOKEN matching the XSRF-TOKEN cookie
                        'X-XSRF-TOKEN': xsrfHeader
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify(credentials)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log('✅ Login réussi', 'success');
                    displayResult('result2', { 
                        status: response.status,
                        data: data 
                    }, true);
                } else {
                    log('❌ Login échoué', 'error');
                    displayResult('result2', { 
                        status: response.status,
                        error: data 
                    }, false);
                }
                
            } catch (error) {
                log(`❌ Erreur lors du test de login: ${error.message}`, 'error');
                displayResult('result2', { error: error.message }, false);
            }
        }

        // Initialisation
        log('🚀 Page de test de login chargée', 'success');
        log('Assurez-vous que le serveur Laravel est démarré avec: php artisan serve', 'info');
    </script>
</body>
</html>
