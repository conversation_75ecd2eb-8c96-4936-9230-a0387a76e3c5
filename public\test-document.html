<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test DocumentResource - SignSecure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Test DocumentResource - SignSecure</h1>
    
    <div class="test-section info">
        <h3>Instructions</h3>
        <p>Cette page permet de tester le DocumentResource et de diagnostiquer l'erreur "Tentative de lecture de la propriété « original_filename » sur la chaîne".</p>
        <p>Assurez-vous que le serveur Laravel est démarré et que les routes de test sont accessibles.</p>
    </div>

    <div class="test-section">
        <h3>Test 1: Création d'un document de test</h3>
        <button onclick="testCreateDocument()">Tester la création</button>
        <div id="result1"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Test avec document existant</h3>
        <button onclick="testExistingDocument()">Tester avec document existant</button>
        <div id="result2"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Test direct du téléversement</h3>
        <button onclick="testUpload()">Tester le téléversement</button>
        <div id="result3"></div>
    </div>

    <div class="test-section">
        <h3>Logs de débogage</h3>
        <div id="logs"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function displayResult(containerId, data, isSuccess = true) {
            const container = document.getElementById(containerId);
            container.className = `test-section ${isSuccess ? 'success' : 'error'}`;
            container.innerHTML = `
                <h3>${isSuccess ? '✅ Succès' : '❌ Erreur'}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        async function testCreateDocument() {
            try {
                log('🧪 Test de création de document...');
                const response = await fetch('/api/test/document');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ Document créé avec succès', 'success');
                    displayResult('result1', data, true);
                } else {
                    log('❌ Erreur lors de la création', 'error');
                    displayResult('result1', data, false);
                }
            } catch (error) {
                log(`❌ Erreur réseau: ${error.message}`, 'error');
                displayResult('result1', { error: error.message }, false);
            }
        }

        async function testExistingDocument() {
            try {
                log('🧪 Test avec document existant...');
                const response = await fetch('/api/test/existing-document');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ Test avec document existant réussi', 'success');
                    displayResult('result2', data, true);
                } else {
                    log('❌ Erreur lors du test avec document existant', 'error');
                    displayResult('result2', data, false);
                }
            } catch (error) {
                log(`❌ Erreur réseau: ${error.message}`, 'error');
                displayResult('result2', { error: error.message }, false);
            }
        }

        async function testUpload() {
            try {
                log('🧪 Test du téléversement...');
                
                // Créer un fichier PDF de test
                const testContent = '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(Test PDF) Tj\nET\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000053 00000 n \n0000000112 00000 n \n0000000209 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n297\n%%EOF';
                const blob = new Blob([testContent], { type: 'application/pdf' });
                const file = new File([blob], 'test.pdf', { type: 'application/pdf' });

                const formData = new FormData();
                formData.append('document', file);
                formData.append('otp_method', 'email');

                const response = await fetch('/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'Accept': 'application/json',
                    },
                    body: formData
                });

                const data = await response.json();
                
                if (data.success) {
                    log('✅ Téléversement réussi', 'success');
                    displayResult('result3', data, true);
                } else {
                    log('❌ Erreur lors du téléversement', 'error');
                    displayResult('result3', data, false);
                }
            } catch (error) {
                log(`❌ Erreur lors du test de téléversement: ${error.message}`, 'error');
                displayResult('result3', { error: error.message }, false);
            }
        }

        // Initialisation
        log('🚀 Page de test chargée', 'success');
        log('Assurez-vous que le serveur Laravel est démarré avec: php artisan serve', 'info');
    </script>
</body>
</html>
