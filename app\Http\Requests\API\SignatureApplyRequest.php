<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class SignatureApplyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'document_id' => ['required', 'exists:documents,id'],
            'x_pct' => ['required', 'numeric', 'min:0', 'max:1'],
            'y_pct' => ['required', 'numeric', 'min:0', 'max:1'],
            'page_number' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'document_id.required' => 'L\'ID du document est obligatoire.',
            'document_id.exists' => 'Le document spécifié n\'existe pas.',
            'x_pct.required' => 'La position X (en %) est obligatoire.',
            'x_pct.numeric' => 'La position X (en %) doit être un nombre.',
            'x_pct.min' => 'X% doit être >= 0.',
            'x_pct.max' => 'X% doit être <= 1.',
            'y_pct.required' => 'La position Y (en %) est obligatoire.',
            'y_pct.numeric' => 'La position Y (en %) doit être un nombre.',
            'y_pct.min' => 'Y% doit être >= 0.',
            'y_pct.max' => 'Y% doit être <= 1.',
            'page_number.required' => 'Le numéro de page est obligatoire.',
            'page_number.integer' => 'Le numéro de page doit être un entier.',
            'page_number.min' => 'Le numéro de page doit être au minimum 1.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if ($this->has('document_id')) {
                $document = \App\Models\Document::find($this->document_id);
                
                if ($document && $this->has('page_number')) {
                    // Vérifier que le numéro de page ne dépasse pas le nombre de pages du document
                    if ($this->page_number > $document->pages_count) {
                        $validator->errors()->add(
                            'page_number', 
                            "Le numéro de page ne peut pas dépasser {$document->pages_count} (nombre de pages du document)."
                        );
                    }
                }
            }
        });
    }
}