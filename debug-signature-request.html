<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Signature Request</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .warning {
            border-color: #ffc107;
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Signature Request</h1>
    
    <div class="container">
        <h3>Configuration</h3>
        <div class="form-group">
            <label for="baseUrl">URL de base de l'API:</label>
            <input type="text" id="baseUrl" value="http://localhost/signsecure/public/api/v1" />
        </div>
        <div class="form-group">
            <label for="token">Token Sanctum:</label>
            <input type="text" id="token" placeholder="Entrez votre token d'authentification" />
        </div>
    </div>

    <div class="container">
        <h3>Test 1: Format correct (doit fonctionner)</h3>
        <div class="form-group">
            <label for="documentId1">ID du document:</label>
            <input type="number" id="documentId1" value="1" min="1" />
        </div>
        <div class="form-group">
            <label for="positionX1">Position X:</label>
            <input type="number" id="positionX1" value="100" min="0" step="1" />
        </div>
        <div class="form-group">
            <label for="positionY1">Position Y:</label>
            <input type="number" id="positionY1" value="200" min="0" step="1" />
        </div>
        <div class="form-group">
            <label for="pageNumber1">Numéro de page:</label>
            <input type="number" id="pageNumber1" value="1" min="1" step="1" />
        </div>
        <button onclick="testCorrectFormat()">Test Format Correct</button>
        <div id="result1" class="debug-info"></div>
    </div>

    <div class="container">
        <h3>Test 2: Format incorrect (doit échouer)</h3>
        <div class="form-group">
            <label for="documentId2">ID du document:</label>
            <input type="number" id="documentId2" value="1" min="1" />
        </div>
        <div class="form-group">
            <label for="positionX2">Position X:</label>
            <input type="number" id="positionX2" value="100" min="0" step="1" />
        </div>
        <div class="form-group">
            <label for="positionY2">Position Y:</label>
            <input type="number" id="positionY2" value="200" min="0" step="1" />
        </div>
        <div class="form-group">
            <label for="pageNumber2">Numéro de page:</label>
            <input type="number" id="pageNumber2" value="1" min="1" step="1" />
        </div>
        <button onclick="testIncorrectFormat()">Test Format Incorrect</button>
        <div id="result2" class="debug-info"></div>
    </div>

    <div class="container">
        <h3>Test 3: Format avec positions array (doit échouer)</h3>
        <div class="form-group">
            <label for="documentId3">ID du document:</label>
            <input type="number" id="documentId3" value="1" min="1" />
        </div>
        <div class="form-group">
            <label for="positionX3">Position X:</label>
            <input type="number" id="positionX3" value="100" min="0" step="1" />
        </div>
        <div class="form-group">
            <label for="positionY3">Position Y:</label>
            <input type="number" id="positionY3" value="200" min="0" step="1" />
        </div>
        <div class="form-group">
            <label for="pageNumber3">Numéro de page:</label>
            <input type="number" id="pageNumber3" value="1" min="1" step="1" />
        </div>
        <button onclick="testPositionsArrayFormat()">Test Format Positions Array</button>
        <div id="result3" class="debug-info"></div>
    </div>

    <div class="container">
        <h3>Test 4: Test avec données manquantes</h3>
        <button onclick="testMissingFields()">Test Champs Manquants</button>
        <div id="result4" class="debug-info"></div>
    </div>

    <script>
        function getBaseUrl() {
            return document.getElementById('baseUrl').value;
        }

        function getToken() {
            return document.getElementById('token').value;
        }

        function showResult(elementId, title, data, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            
            let content = `=== ${title} (${timestamp}) ===\n`;
            content += `URL: ${getBaseUrl()}/signatures/apply\n`;
            content += `Méthode: POST\n`;
            content += `Headers: Authorization: Bearer ${getToken().substring(0, 20)}...\n`;
            content += `Content-Type: application/json\n\n`;
            content += `Données envoyées:\n${JSON.stringify(data, null, 2)}\n\n`;
            
            element.textContent = content;
            element.className = `debug-info ${type}`;
        }

        async function makeRequest(url, data) {
            const token = getToken();
            if (!token) {
                alert('Veuillez entrer un token d\'authentification');
                return null;
            }

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const responseData = await response.json();
                return {
                    status: response.status,
                    ok: response.ok,
                    data: responseData
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    data: { error: error.message }
                };
            }
        }

        async function testCorrectFormat() {
            const data = {
                document_id: parseInt(document.getElementById('documentId1').value),
                position_x: parseFloat(document.getElementById('positionX1').value),
                position_y: parseFloat(document.getElementById('positionY1').value),
                page_number: parseInt(document.getElementById('pageNumber1').value)
            };

            showResult('result1', 'Format Correct', data, 'success');

            const result = await makeRequest(`${getBaseUrl()}/signatures/apply`, data);
            
            if (result) {
                const resultElement = document.getElementById('result1');
                resultElement.textContent += `\n=== RÉPONSE ===\n`;
                resultElement.textContent += `Status: ${result.status}\n`;
                resultElement.textContent += `OK: ${result.ok}\n`;
                resultElement.textContent += `Réponse: ${JSON.stringify(result.data, null, 2)}\n`;
                
                if (result.ok) {
                    resultElement.className = 'debug-info success';
                } else {
                    resultElement.className = 'debug-info error';
                }
            }
        }

        async function testIncorrectFormat() {
            const data = {
                document_id: parseInt(document.getElementById('documentId2').value),
                position_x: parseFloat(document.getElementById('positionX2').value),
                position_y: parseFloat(document.getElementById('positionY2').value)
                // page_number manquant intentionnellement
            };

            showResult('result2', 'Format Incorrect (page_number manquant)', data, 'warning');

            const result = await makeRequest(`${getBaseUrl()}/signatures/apply`, data);
            
            if (result) {
                const resultElement = document.getElementById('result2');
                resultElement.textContent += `\n=== RÉPONSE ===\n`;
                resultElement.textContent += `Status: ${result.status}\n`;
                resultElement.textContent += `OK: ${result.ok}\n`;
                resultElement.textContent += `Réponse: ${JSON.stringify(result.data, null, 2)}\n`;
                
                if (result.ok) {
                    resultElement.className = 'debug-info success';
                } else {
                    resultElement.className = 'debug-info error';
                }
            }
        }

        async function testPositionsArrayFormat() {
            const data = {
                document_id: parseInt(document.getElementById('documentId3').value),
                positions: [
                    {
                        x: parseFloat(document.getElementById('positionX3').value),
                        y: parseFloat(document.getElementById('positionY3').value),
                        page: parseInt(document.getElementById('pageNumber3').value)
                    }
                ]
            };

            showResult('result3', 'Format Positions Array (incorrect pour API)', data, 'warning');

            const result = await makeRequest(`${getBaseUrl()}/signatures/apply`, data);
            
            if (result) {
                const resultElement = document.getElementById('result3');
                resultElement.textContent += `\n=== RÉPONSE ===\n`;
                resultElement.textContent += `Status: ${result.status}\n`;
                resultElement.textContent += `OK: ${result.ok}\n`;
                resultElement.textContent += `Réponse: ${JSON.stringify(result.data, null, 2)}\n`;
                
                if (result.ok) {
                    resultElement.className = 'debug-info success';
                } else {
                    resultElement.className = 'debug-info error';
                }
            }
        }

        async function testMissingFields() {
            const testCases = [
                {
                    name: 'Aucun champ',
                    data: {},
                    expectedError: 'Tous les champs manquent'
                },
                {
                    name: 'Seulement document_id',
                    data: { document_id: 1 },
                    expectedError: 'position_x, position_y, page_number manquent'
                },
                {
                    name: 'Seulement position_x',
                    data: { position_x: 100 },
                    expectedError: 'document_id, position_y, page_number manquent'
                },
                {
                    name: 'Seulement position_y',
                    data: { position_y: 200 },
                    expectedError: 'document_id, position_x, page_number manquent'
                },
                {
                    name: 'Seulement page_number',
                    data: { page_number: 1 },
                    expectedError: 'document_id, position_x, position_y manquent'
                }
            ];

            let results = '';
            for (const testCase of testCases) {
                results += `\n--- ${testCase.name} ---\n`;
                results += `Données: ${JSON.stringify(testCase.data)}\n`;
                results += `Erreur attendue: ${testCase.expectedError}\n`;
                
                const result = await makeRequest(`${getBaseUrl()}/signatures/apply`, testCase.data);
                if (result) {
                    results += `Status: ${result.status}\n`;
                    results += `Réponse: ${JSON.stringify(result.data, null, 2)}\n`;
                }
                results += '\n';
            }

            const resultElement = document.getElementById('result4');
            resultElement.textContent = results;
            resultElement.className = 'debug-info warning';
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Page de debug de l\'API Signature chargée');
            console.log('📝 Instructions:');
            console.log('1. Entrez votre token Sanctum');
            console.log('2. Testez les différents formats de données');
            console.log('3. Comparez les réponses pour identifier le problème');
        });
    </script>
</body>
</html>
