# 🔧 Correction de l'API Signature - Format de données incorrect

## ❌ **Problème identifié :**

Votre application utilise **deux formats différents** pour les signatures :

1. **API REST** (`/api/v1/signatures/apply`) - Format attendu :
   ```json
   {
       "document_id": 1,
       "position_x": 100.0,
       "position_y": 200.0,
       "page_number": 1
   }
   ```

2. **Système Web** (`/signatures/{document}/apply`) - Format attendu :
   ```json
   {
       "position_x": 100.0,
       "position_y": 200.0,
       "page_number": 1
   }
   ```

## 🚨 **Code problématique trouvé :**

Dans `resources/views/signatures/preview.blade.php`, ligne 330+ :

```javascript
// ❌ FORMAT INCORRECT pour l'API REST
body: JSON.stringify({
    positions: signaturePositions  // Array de positions
})
```

## ✅ **Solutions :**

### **Option 1: Corriger le format pour l'API REST (Recommandé)**

Modifiez la fonction `applySignature()` dans `resources/views/signatures/preview.blade.php` :

```javascript
async function applySignature() {
    if (signaturePositions.length === 0) {
        toastr.error('Veuillez sélectionner au moins une position pour la signature.');
        return;
    }
    
    const btn = document.getElementById('applyBtn');
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Application...';
    
    try {
        // ✅ FORMAT CORRECT pour l'API REST
        const data = {
            document_id: documentId, // Assurez-vous que cette variable est définie
            position_x: signaturePositions[0].x,
            position_y: signaturePositions[0].y,
            page_number: signaturePositions[0].page
        };

        const response = await fetch(`/api/v1/signatures/apply`, { // URL de l'API REST
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${getToken()}`, // Token Sanctum
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok) {
            toastr.success(result.message || 'Signature appliquée avec succès');
            setTimeout(() => {
                window.location.href = result.data?.document?.id ? 
                    `/documents/${result.data.document.id}` : 
                    '/documents';
            }, 1000);
        } else {
            throw new Error(result.message || 'Erreur lors de l\'application');
        }

    } catch (error) {
        console.error('Erreur:', error);
        toastr.error(error.message || 'Erreur lors de l\'application de la signature');
    } finally {
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

// Fonction pour récupérer le token Sanctum
function getToken() {
    // Récupérez le token depuis localStorage, sessionStorage, ou une variable globale
    return localStorage.getItem('sanctum_token') || 
           sessionStorage.getItem('sanctum_token') || 
           window.sanctumToken || 
           '';
}
```

### **Option 2: Utiliser le système web existant**

Si vous préférez garder le système web, modifiez l'URL :

```javascript
// ✅ Utiliser le système web existant
const response = await fetch(`/signatures/${documentId}/apply`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'Accept': 'application/json'
    },
    body: JSON.stringify({
        position_x: signaturePositions[0].x,
        position_y: signaturePositions[0].y,
        page_number: signaturePositions[0].page
    })
});
```

### **Option 3: Créer un endpoint unifié**

Créez un nouveau contrôleur qui gère les deux formats :

```php
// app/Http/Controllers/API/UnifiedSignatureController.php
public function apply(Request $request): JsonResponse
{
    $data = $request->all();
    
    // Détecter le format
    if (isset($data['positions']) && is_array($data['positions'])) {
        // Format web existant
        $request->merge([
            'position_x' => $data['positions'][0]['x'] ?? null,
            'position_y' => $data['positions'][0]['y'] ?? null,
            'page_number' => $data['positions'][0]['page'] ?? null,
        ]);
    }
    
    // Utiliser la validation existante
    $validated = $request->validate([
        'document_id' => 'required|exists:documents,id',
        'position_x' => 'required|numeric|min:0',
        'position_y' => 'required|numeric|min:0',
        'page_number' => 'required|integer|min:1',
    ]);
    
    // ... reste de la logique
}
```

## 🔍 **Débogage étape par étape :**

### **Étape 1: Vérifiez le format des données**

Ouvrez `debug-signature-request.html` dans votre navigateur et testez :

1. **Format correct** (doit fonctionner)
2. **Format incorrect** (doit échouer avec erreur de validation)
3. **Format positions array** (doit échouer)

### **Étape 2: Vérifiez les variables dans votre code**

Assurez-vous que `documentId` est bien défini :

```javascript
// Au début de votre script
const documentId = {{ $document->id }}; // ou récupérez depuis l'URL
console.log('Document ID:', documentId);
```

### **Étape 3: Vérifiez l'authentification**

Pour l'API REST, vous devez avoir un token Sanctum valide :

```javascript
// Vérifiez que le token est présent
const token = getToken();
if (!token) {
    console.error('Token Sanctum manquant');
    return;
}
```

## 📋 **Checklist de correction :**

- [ ] Changer l'URL de `/signatures/${documentId}/apply` vers `/api/v1/signatures/apply`
- [ ] Modifier le format des données de `{ positions: [...] }` vers `{ document_id, position_x, position_y, page_number }`
- [ ] Ajouter l'en-tête `Authorization: Bearer ${token}` pour l'API REST
- [ ] Vérifier que `documentId` est bien défini
- [ ] Tester avec la page de debug

## 🧪 **Test de validation :**

Après correction, testez avec :

```bash
curl -X POST http://localhost/signsecure/public/api/v1/signatures/apply \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "document_id": 1,
    "position_x": 100.0,
    "position_y": 200.0,
    "page_number": 1
  }'
```

## 🚨 **Erreurs courantes à éviter :**

1. **Oublier `document_id`** - L'API REST l'exige
2. **Utiliser `positions` array** - L'API REST attend des champs individuels
3. **Oublier l'authentification** - L'API REST nécessite un token Sanctum
4. **Mélanger les formats** - Choisissez un format et respectez-le

## ✅ **Vérification finale :**

Après correction, votre requête doit ressembler à :

```javascript
{
    method: 'POST',
    url: '/api/v1/signatures/apply',
    headers: {
        'Authorization': 'Bearer YOUR_TOKEN',
        'Content-Type': 'application/json'
    },
    body: {
        document_id: 1,
        position_x: 100.0,
        position_y: 200.0,
        page_number: 1
    }
}
```

**Testez maintenant avec la page de debug pour confirmer que le problème est résolu !** 🎯
