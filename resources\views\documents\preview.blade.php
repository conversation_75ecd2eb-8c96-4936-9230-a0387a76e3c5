@extends('layouts.app')

@section('title', 'Prévisualisation - SignSecure')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">Prévisualisation du document</h1>
                    <p class="text-muted mb-0">Cliquez sur l'emplacement où vous souhaitez apposer votre signature</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="btn btn-outline-secondary" id="zoomLevel">100%</span>
                    <button class="btn btn-outline-secondary" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- PDF Viewer -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-pdf text-danger me-2"></i>
                            <h6 class="mb-0">{{ $document->original_filename }}</h6>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <span class="badge bg-info">{{ $document->pages_count }} page(s)</span>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-secondary" onclick="previousPage()" id="prevPageBtn">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <span class="btn btn-outline-secondary" id="pageInfo">
                                    Page <span id="currentPage">1</span> sur <span id="totalPages">{{ $document->pages_count }}</span>
                                </span>
                                <button class="btn btn-outline-secondary" onclick="nextPage()" id="nextPageBtn">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="pdf-viewer-container position-relative" style="max-height: calc(100vh - 260px); overflow:auto; z-index: 1;">
                        <div id="pdfViewer" class="text-center">
                            <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement du PDF...</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Signature Position Marker -->
                        <div id="signatureMarker" class="signature-marker d-none" style="position:absolute; z-index: 2;">
                            <div class="signature-placeholder">
                                <i class="fas fa-signature"></i>
                                <span>Signature ici</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Document Info -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        Informations du document
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Nom :</span>
                                <span class="fw-semibold">{{ $document->original_filename }}</span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Taille :</span>
                                <span>{{ $document->file_size_human }}</span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Pages :</span>
                                <span>{{ $document->pages_count }}</span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Téléversé :</span>
                                <span>{{ $document->created_at->format('d/m/Y H:i') }}</span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Code de suivi :</span>
                                <span class="tracking-code">{{ $document->tracking_code }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Signature Instructions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        Instructions
                    </h6>
                </div>
                <div class="card-body">
                    <ol class="mb-0 ps-3">
                        <li class="mb-2">Utilisez les contrôles de zoom pour ajuster la taille d'affichage</li>
                        <li class="mb-2">Naviguez entre les pages si nécessaire</li>
                        <li class="mb-2">Cliquez à l'endroit exact où vous souhaitez placer votre signature</li>
                        <li class="mb-0">Validez la position en cliquant sur "Continuer"</li>
                    </ol>
                </div>
            </div>

            <!-- Signature Position -->
            <div class="card shadow-sm mb-4" id="positionCard" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-crosshairs text-success me-2"></i>
                        Position sélectionnée
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2 mb-3">
                        <div class="col-6">
                            <small class="text-muted">Page :</small>
                            <div class="fw-semibold" id="selectedPage">-</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Position :</small>
                            <div class="fw-semibold" id="selectedPosition">-</div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="proceedToSignature()" id="proceedBtn">
                            <i class="fas fa-arrow-right me-2"></i>
                            Continuer vers la signature
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearPosition()">
                            <i class="fas fa-times me-1"></i>
                            Effacer la position
                        </button>
                    </div>
                </div>
            </div>

            <!-- Signature Preview -->
            @if(Auth::user()->hasSignature())
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-pen-nib text-primary me-2"></i>
                        Votre signature
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="signature-preview mb-3">
                        <img src="{{ Auth::user()->signature_image }}" alt="Ma signature" class="img-fluid" style="max-height: 80px;">
                    </div>
                    <small class="text-muted">Cette signature sera appliquée sur le document</small>
                </div>
            </div>
            @else
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        Signature requise
                    </h6>
                </div>
                <div class="card-body text-center">
                    <p class="text-muted mb-3">Vous devrez créer votre signature à l'étape suivante</p>
                    <i class="fas fa-signature text-muted" style="font-size: 2rem;"></i>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Form for signature position -->
<form id="signatureForm" method="POST" action="{{ route('signatures.create', $document) }}" style="display: none;">
    @csrf
    <input type="hidden" name="position_x" id="positionX">
    <input type="hidden" name="position_y" id="positionY">
    <input type="hidden" name="page_number" id="pageNumber" value="1">
</form>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script>
// PDF.js configuration
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

let pdfDoc = null;
let pageNum = 1;
let pageRendering = false;
let pageNumPending = null;
let scale = 1.0;
let canvas = null;
let ctx = null;

// Signature positions (allow multiple)
let signaturePositions = [];

document.addEventListener('DOMContentLoaded', function() {
    loadPDF();
});

async function loadPDF() {
    try {
        const loadingTask = pdfjsLib.getDocument('{{ route("documents.pdf", $document) }}');
        pdfDoc = await loadingTask.promise;
        
        document.getElementById('totalPages').textContent = pdfDoc.numPages;
        renderPage(pageNum);
    } catch (error) {
        console.error('Error loading PDF:', error);
        document.getElementById('pdfViewer').innerHTML = 
            '<div class="alert alert-danger">Erreur lors du chargement du PDF</div>';
    }
}

async function renderPage(num) {
    pageRendering = true;
    
    try {
        const page = await pdfDoc.getPage(num);
        const viewport = page.getViewport({ scale: scale });
        
        // Create canvas if it doesn't exist
        if (!canvas) {
            canvas = document.createElement('canvas');
            canvas.className = 'pdf-canvas';
            canvas.style.cursor = 'crosshair';
            canvas.addEventListener('click', handleCanvasClick);
            
            const viewer = document.getElementById('pdfViewer');
            viewer.innerHTML = '';
            viewer.appendChild(canvas);
        }
        
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        ctx = canvas.getContext('2d');
        
        const renderContext = {
            canvasContext: ctx,
            viewport: viewport
        };
        
        await page.render(renderContext).promise;
        
        pageRendering = false;
        
        if (pageNumPending !== null) {
            renderPage(pageNumPending);
            pageNumPending = null;
        }
        
        // Update UI
        document.getElementById('currentPage').textContent = num;
        updateNavigationButtons();
        
    } catch (error) {
        console.error('Error rendering page:', error);
        pageRendering = false;
    }
}

function queueRenderPage(num) {
    if (pageRendering) {
        pageNumPending = num;
    } else {
        renderPage(num);
    }
}

function previousPage() {
    if (pageNum <= 1) return;
    pageNum--;
    queueRenderPage(pageNum);
}

function nextPage() {
    if (pageNum >= pdfDoc.numPages) return;
    pageNum++;
    queueRenderPage(pageNum);
}

function zoomIn() {
    scale = Math.min(scale * 1.2, 3.0);
    document.getElementById('zoomLevel').textContent = Math.round(scale * 100) + '%';
    queueRenderPage(pageNum);
}

function zoomOut() {
    scale = Math.max(scale / 1.2, 0.5);
    document.getElementById('zoomLevel').textContent = Math.round(scale * 100) + '%';
    queueRenderPage(pageNum);
}

function updateNavigationButtons() {
    document.getElementById('prevPageBtn').disabled = pageNum <= 1;
    document.getElementById('nextPageBtn').disabled = pageNum >= pdfDoc.numPages;
}

function handleCanvasClick(event) {
    const rect = canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left) / scale;
    const y = (event.clientY - rect.top) / scale;
    
    // Store signature position with canvas dimensions for accurate conversion server-side
    signaturePositions.push({ x: x, y: y, page: pageNum, canvas_w: canvas.width, canvas_h: canvas.height });
    
    // Show signature marker
    showSignatureMarker(event.clientX - rect.left, event.clientY - rect.top);
    
    // Update UI
    updatePositionInfo();
    
    toastr.success('Position de signature sélectionnée !');
}

function showSignatureMarker(x, y) {
    const marker = document.getElementById('signatureMarker');
    const container = document.querySelector('.pdf-viewer-container');
    
    marker.style.left = (x - 75) + 'px'; // Center the 150px wide marker
    marker.style.top = (y - 25) + 'px';  // Position above click point
    marker.classList.remove('d-none');
    
    // Add animation
    marker.style.animation = 'none';
    setTimeout(() => {
        marker.style.animation = 'pulse 1s ease-in-out';
    }, 10);
}

function updatePositionInfo() {
    if (signaturePositions.length === 0) {
        document.getElementById('selectedPage').textContent = '-';
        document.getElementById('selectedPosition').textContent = '-';
        document.getElementById('positionCard').style.display = 'none';
        return;
    }
    const last = signaturePositions[signaturePositions.length - 1];
    document.getElementById('selectedPage').textContent = last.page;
    document.getElementById('selectedPosition').textContent = 
        `X: ${Math.round(last.x)}, Y: ${Math.round(last.y)} (total: ${signaturePositions.length})`;
    
    document.getElementById('positionCard').style.display = 'block';
}

function clearPosition() {
    signaturePositions = [];
    document.getElementById('signatureMarker').classList.add('d-none');
    document.getElementById('positionCard').style.display = 'none';
}

function proceedToSignature() {
    if (!signaturePosition.x || !signaturePosition.y || !signaturePosition.page) {
        toastr.error('Veuillez sélectionner une position pour la signature.');
        return;
    }
    
    // Fill form data
    document.getElementById('positionX').value = signaturePosition.x;
    document.getElementById('positionY').value = signaturePosition.y;
    document.getElementById('pageNumber').value = signaturePosition.page;
    
    // Show loading
    const btn = document.getElementById('proceedBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Préparation...';
    
    // Submit form
    document.getElementById('signatureForm').submit();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
    
    if (e.key === 'ArrowLeft') {
        previousPage();
    } else if (e.key === 'ArrowRight') {
        nextPage();
    } else if (e.key === 'Escape') {
        clearPosition();
    }
});
</script>
@endpush
