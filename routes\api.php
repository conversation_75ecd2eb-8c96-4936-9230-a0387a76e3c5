<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\DocumentController;
use App\Http\Controllers\API\OTPController;
use App\Http\Controllers\API\PhysicalRetraceController;
use App\Http\Controllers\API\SignatureController;
use App\Http\Controllers\API\VerificationController;
use App\Http\Controllers\API\ProfileController as ApiProfileController;
use App\Http\Controllers\API\InvitationController;
use App\Http\Controllers\API\DashboardController as ApiDashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Routes publiques (non authentifiées)
Route::prefix("v1")->group(function () {
    // Authentification
    Route::post("auth/register", [AuthController::class, "register"]);
    Route::post("auth/login", [AuthController::class, "login"]);

    // Vérification d'authenticité (publiques)
    Route::prefix("verification")->group(function () {
        Route::post("verify", [VerificationController::class, "verifyDocument"]);
        Route::post("download", [VerificationController::class, "downloadAuthentic"]);
        Route::post("report", [VerificationController::class, "generateVerificationReport"]);
        Route::post("download-retrace", [VerificationController::class, "downloadRetrace"]);
        Route::get("search", [VerificationController::class, "searchDocuments"]);
        Route::get("stats", [VerificationController::class, "verificationStats"]);
      
    });

    
});

// Routes protégées par Sanctum (authentifiées)
Route::middleware("auth:sanctum")->prefix("v1")->group(function () {
    // Dashboard
    Route::get("dashboard", [\App\Http\Controllers\API\DashboardController::class, "index"]);
    
    // Authentification
    Route::post("auth/logout", [AuthController::class, "logout"]);
    Route::post("auth/logout-all", [AuthController::class, "logoutAll"]);
    Route::post("auth/refresh", [AuthController::class, "refresh"]);
    Route::get("user", [AuthController::class, "user"]);
    Route::post("auth/web-session", [AuthController::class, "createWebSession"]);

    // Profil (API)
    Route::get('profile', [ApiProfileController::class, 'show']);
    Route::put('profile', [ApiProfileController::class, 'update']);
    Route::post('profile/change-password', [ApiProfileController::class, 'changePassword']);

    // Invitations
    Route::post('invitations', [InvitationController::class, 'store']);
    Route::get('invitations/for-me', [InvitationController::class, 'forMe']);

    // Dashboard overview agrégé
    Route::get('dashboard/overview', [ApiDashboardController::class, 'overview']);

    // Documents
    
    Route::apiResource("documents", DocumentController::class);
    Route::get("documents/{document}/download", [DocumentController::class, "download"])->name("api.documents.download");
    Route::get("documents/{document}/preview", [DocumentController::class, "preview"])->name("api.documents.preview");
    Route::get("documents/stats", [DocumentController::class, "statistics"]);
    Route::patch("documents/{document}/status", [DocumentController::class, "updateStatus"]);
    Route::post('documents/upload', [DocumentController::class, 'store'])->name('api.documents.upload');

    // OTP
    Route::post("otp/generate", [OTPController::class, "generate"]);
    Route::post("otp/verify", [OTPController::class, "verify"]);
    Route::get("otp/{document}/status", [OTPController::class, "status"]);
    Route::get("otp/history", [OTPController::class, "history"]);
    Route::post("otp/cancel-all", [OTPController::class, "cancelAll"]);
    Route::post("otp/resend/{document}", [OTPController::class, "resend"]);

    // Signatures
    Route::post("signatures/capture", [SignatureController::class, "capture"]);
    Route::post("signatures/apply", [SignatureController::class, "apply"]);
    Route::get("signatures/current", [SignatureController::class, "current"]);
    Route::delete("signatures/delete", [SignatureController::class, "delete"]);
    Route::get("signatures/history", [SignatureController::class, "history"]);
    Route::post("signatures/preview", [SignatureController::class, "preview"]);

    // Physical Retrace
    Route::apiResource("retraces", PhysicalRetraceController::class);
    Route::post("retraces/request-otp", [PhysicalRetraceController::class, "requestOtp"]);
    Route::post("retraces/verify-otp", [PhysicalRetraceController::class, "verifyOtpAndCreate"]);
    Route::get("retraces/{retrace}/download", [PhysicalRetraceController::class, "download"])->name("api.retraces.download");
});

// Routes de test (développement uniquement)
if (app()->environment('local')) {
    Route::get('/test/document', [\App\Http\Controllers\TestController::class, 'testDocumentResource']);
    Route::get('/test/existing-document', [\App\Http\Controllers\TestController::class, 'testExistingDocument']);
    Route::get('/test/upload-process', [\App\Http\Controllers\TestController::class, 'testUploadProcess']);
}


