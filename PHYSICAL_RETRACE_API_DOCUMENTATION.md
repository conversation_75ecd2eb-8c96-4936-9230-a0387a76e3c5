# API Physical Retrace - Documentation

## Vue d'ensemble

L'API Physical Retrace permet de gérer les demandes de retrace physique de documents. Cette fonctionnalité était auparavant uniquement disponible via l'interface web, mais est maintenant accessible via l'API REST.

## Endpoints

### Base URL
```
/api/v1/retraces
```

### Authentification
Tous les endpoints nécessitent une authentification via Sanctum. Incluez le token Bearer dans l'en-tête :
```
Authorization: Bearer {token}
```

## Endpoints disponibles

### 1. Créer un retrace directement
**POST** `/api/v1/retraces`

Crée un retrace physique sans vérification OTP.

**Corps de la requête :**
```json
{
    "date": "2024-01-15",
    "reference": "REF-2024-001",
    "subject": "Demande de retrace pour document fiscal",
    "notes": "Document perdu lors du déménagement"
}
```

**Réponse de succès (201) :**
```json
{
    "success": true,
    "message": "Retrace créé avec succès",
    "data": {
        "id": 1,
        "tracking_code": "SS-PR-A1B2C3D4E5",
        "date": "2024-01-15",
        "reference": "REF-2024-001",
        "subject": "Demande de retrace pour document fiscal",
        "notes": "Document perdu lors du déménagement",
        "created_at": "2024-01-15T10:30:00Z",
        "pdf_path": "retraces/2024/01/retrace_SS-PR-A1B2C3D4E5.pdf"
    }
}
```

### 2. Demander un code OTP
**POST** `/api/v1/retraces/request-otp`

Demande un code OTP pour la création sécurisée d'un retrace.

**Corps de la requête :**
```json
{
    "date": "2024-01-15",
    "reference": "REF-2024-001",
    "subject": "Demande de retrace pour document fiscal",
    "notes": "Document perdu lors du déménagement"
}
```

**Réponse de succès (200) :**
```json
{
    "success": true,
    "message": "Code OTP envoyé. Vérifiez votre email.",
    "data": {
        "form_data": {
            "date": "2024-01-15",
            "reference": "REF-2024-001",
            "subject": "Demande de retrace pour document fiscal",
            "notes": "Document perdu lors du déménagement"
        }
    }
}
```

### 3. Vérifier OTP et créer le retrace
**POST** `/api/v1/retraces/verify-otp`

Vérifie le code OTP et crée le retrace si valide.

**Corps de la requête :**
```json
{
    "code": "123456",
    "date": "2024-01-15",
    "reference": "REF-2024-001",
    "subject": "Demande de retrace pour document fiscal",
    "notes": "Document perdu lors du déménagement"
}
```

**Réponse de succès (201) :**
```json
{
    "success": true,
    "message": "Retrace créé avec succès après vérification OTP",
    "data": {
        "id": 1,
        "tracking_code": "SS-PR-A1B2C3D4E5",
        "date": "2024-01-15",
        "reference": "REF-2024-001",
        "subject": "Demande de retrace pour document fiscal",
        "notes": "Document perdu lors du déménagement",
        "created_at": "2024-01-15T10:30:00Z",
        "pdf_path": "retraces/2024/01/retrace_SS-PR-A1B2C3D4E5.pdf"
    }
}
```

### 4. Lister tous les retraces
**GET** `/api/v1/retraces`

Récupère la liste paginée des retraces de l'utilisateur.

**Paramètres de requête :**
- `per_page` (optionnel) : Nombre d'éléments par page (défaut: 15)

**Réponse de succès (200) :**
```json
{
    "success": true,
    "message": "Retraces récupérés avec succès",
    "data": [
        {
            "id": 1,
            "tracking_code": "SS-PR-A1B2C3D4E5",
            "date": "2024-01-15",
            "reference": "REF-2024-001",
            "subject": "Demande de retrace pour document fiscal",
            "created_at": "2024-01-15T10:30:00Z"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 1,
        "per_page": 15,
        "total": 1
    }
}
```

### 5. Afficher un retrace spécifique
**GET** `/api/v1/retraces/{id}`

Récupère les détails d'un retrace spécifique.

**Réponse de succès (200) :**
```json
{
    "success": true,
    "message": "Retrace récupéré avec succès",
    "data": {
        "id": 1,
        "tracking_code": "SS-PR-A1B2C3D4E5",
        "date": "2024-01-15",
        "reference": "REF-2024-001",
        "subject": "Demande de retrace pour document fiscal",
        "notes": "Document perdu lors du déménagement",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "pdf_path": "retraces/2024/01/retrace_SS-PR-A1B2C3D4E5.pdf"
    }
}
```

### 6. Télécharger le PDF
**GET** `/api/v1/retraces/{id}/download`

Récupère les informations de téléchargement du PDF.

**Réponse de succès (200) :**
```json
{
    "success": true,
    "message": "PDF disponible pour téléchargement",
    "data": {
        "download_url": "/api/v1/retraces/1/download",
        "filename": "retrace_SS-PR-A1B2C3D4E5.pdf",
        "tracking_code": "SS-PR-A1B2C3D4E5"
    }
}
```

### 7. Supprimer un retrace
**DELETE** `/api/v1/retraces/{id}`

Supprime un retrace et son PDF associé.

**Réponse de succès (200) :**
```json
{
    "success": true,
    "message": "Retrace supprimé avec succès"
}
```

## Codes d'erreur

### 400 - Requête invalide
```json
{
    "success": false,
    "message": "Données invalides",
    "errors": {
        "date": ["La date est obligatoire."],
        "reference": ["La référence est obligatoire."]
    }
}
```

### 401 - Non authentifié
```json
{
    "message": "Unauthenticated."
}
```

### 403 - Accès interdit
```json
{
    "message": "This action is unauthorized."
}
```

### 404 - Retrace non trouvé
```json
{
    "message": "No query results for model [App\\Models\\PhysicalRetrace] {id}"
}
```

### 422 - Validation échouée
```json
{
    "success": false,
    "message": "Code OTP invalide ou expiré",
    "errors": {
        "code": ["Code OTP invalide ou expiré"]
    }
}
```

### 500 - Erreur serveur
```json
{
    "success": false,
    "message": "Erreur lors de l'envoi du code OTP",
    "error": "Détails de l'erreur"
}
```

## Règles de validation

### Retrace
- `date` : Requis, doit être une date valide, ne peut pas être dans le futur
- `reference` : Requis, maximum 255 caractères
- `subject` : Requis, maximum 255 caractères
- `notes` : Optionnel, maximum 1000 caractères

### OTP
- `code` : Requis, exactement 6 caractères

## Workflow recommandé

1. **Création simple** : Utilisez `POST /retraces` pour créer un retrace directement
2. **Création sécurisée** : 
   - Appelez `POST /retraces/request-otp` avec les données du retrace
   - L'utilisateur reçoit un code OTP par email
   - Appelez `POST /retraces/verify-otp` avec le code OTP et les données
   - Le retrace est créé après vérification

## Exemples d'utilisation

### JavaScript/Fetch
```javascript
// Créer un retrace
const response = await fetch('/api/v1/retraces', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        date: '2024-01-15',
        reference: 'REF-2024-001',
        subject: 'Demande de retrace',
        notes: 'Document perdu'
    })
});

const result = await response.json();
```

### cURL
```bash
# Créer un retrace
curl -X POST /api/v1/retraces \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2024-01-15",
    "reference": "REF-2024-001",
    "subject": "Demande de retrace",
    "notes": "Document perdu"
  }'
```

## Notes importantes

- Tous les retraces génèrent automatiquement un code de suivi unique (format: SS-PR-XXXXXXXXXX)
- Un PDF blanc est généré automatiquement avec le code de suivi
- Les fichiers PDF sont stockés dans le stockage privé
- La suppression d'un retrace supprime également le PDF associé
- L'API respecte les politiques d'autorisation définies dans `PhysicalRetracePolicy`
