<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\AuditLogger;
use Illuminate\Http\Request;

class UserManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth:sanctum', 'is.admin']);
    }

    public function index()
    {
        $users = User::orderByDesc('created_at')->paginate(20);
        return view('admin.users', compact('users'));
    }

    public function promote(int $id)
    {
        $user = User::findOrFail($id);
        $user->update(['role' => 'admin']);
        AuditLogger::log('Promote User', "Promotion de l'utilisateur #{$user->id} en admin");
        return redirect()->back()->with('status', 'Utilisateur promu en admin');
    }

    public function demote(int $id)
    {
        $user = User::findOrFail($id);
        // Empêcher de se rétrograder soi-même facilement (optionnel)
        if (auth()->id() === $user->id) {
            return redirect()->back()->with('error', 'Vous ne pouvez pas vous rétrograder vous-même.');
        }
        $user->update(['role' => 'user']);
        AuditLogger::log('Demote User', "Rétrogradation de l'utilisateur #{$user->id} en user");
        return redirect()->back()->with('status', 'Utilisateur rétrogradé en user');
    }
}


