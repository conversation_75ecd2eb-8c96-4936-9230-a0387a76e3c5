<?php

namespace App\Http\Controllers;

use App\Models\PhysicalRetrace;
use App\Services\PDFService;
use App\Services\OTPService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PhysicalRetraceController extends Controller
{
    public function __construct(private PDFService $pdfService, private OTPService $otpService)
    {
        $this->middleware('auth');
    }

    public function create()
    {
        return view('retrace.create');
    }

    public function requestOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date',
            'reference' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $validator->validate();

        $user = Auth::user();

        session([
            'retrace.form' => [
                'date' => $request->input('date'),
                'reference' => $request->input('reference'),
                'subject' => $request->input('subject'),
                'notes' => $request->input('notes'),
            ]
        ]);

        // Envoyer OTP pour retrace selon préférence (sms/email)
        $type = $request->input('otp_method', 'email');
        $this->otpService->generateAndSendForRetrace($user, $type);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => "Code OTP envoyé par {$type}.",
                'redirect' => route('retrace.verify-otp'),
            ]);
        }

        return redirect()->route('retrace.verify-otp');
    }

    public function showVerifyOtp()
    {
        abort_unless(session()->has('retrace.form'), 404);
        return view('retrace.verify-otp');
    }

    public function verifyOtp(Request $request)
    {
        // Vérifier si on a les données en session ou dans la requête
        $form = null;
        if (session()->has('retrace.form')) {
            $form = session('retrace.form');
            session()->forget('retrace.form');
        } elseif ($request->has('_payload')) {
            // Cas où les données sont envoyées depuis le frontend
            $form = $request->input('_payload');
        } else {
            abort(404, 'Données de formulaire manquantes');
        }

        $request->validate(['code' => 'required|string']);
        $user = Auth::user();
        $valid = $this->otpService->verifyForRetrace($user, $request->input('code'));
        if (!$valid) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Code OTP invalide ou expiré',
                    'errors' => ['code' => ['Code OTP invalide ou expiré']]
                ], 422);
            }
            return back()->withErrors(['code' => 'Code OTP invalide ou expiré']);
        }

        // Utiliser le service de génération de codes de suivi
        $trackingCode = \App\Services\TrackingCodeService::generateRetraceCode();

        $retrace = PhysicalRetrace::create([
            'user_id' => $user->id,
            'tracking_code' => $trackingCode,
            'date' => $form['date'],
            'reference' => $form['reference'],
            'subject' => $form['subject'],
            'notes' => $form['notes'] ?? null,
        ]);

        // Générer un PDF blanc contenant uniquement le code de suivi
        $pdfPath = $this->pdfService->generateBlankWithTrackingCodeOnly(
            $retrace->tracking_code
        );

        $retrace->update(['pdf_path' => $pdfPath]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Retrace créé avec succès',
                'data' => [
                    'id' => $retrace->id,
                    'tracking_code' => $retrace->tracking_code,
                    'date' => $retrace->date,
                    'reference' => $retrace->reference,
                    'subject' => $retrace->subject,
                    'notes' => $retrace->notes,
                    'created_at' => $retrace->created_at->toIso8601String(),
                ]
            ]);
        }

        return redirect()->route('retrace.show', $retrace);
    }

    public function show(PhysicalRetrace $retrace)
    {
        $this->authorize('view', $retrace);
        return view('retrace.show', compact('retrace'));
    }
}


