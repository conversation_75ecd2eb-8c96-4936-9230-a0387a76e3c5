@extends('layouts.app')

@section('title', 'Vérification OTP - SignSecure')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-shield-alt text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h2 class="h4 mb-2">Vérification de sécurité</h2>
                        <p class="text-muted">
                            Un code de vérification a été envoyé pour sécuriser l'accès à votre document
                        </p>
                    </div>

                    <!-- Document Info -->
                    <div class="bg-light rounded-3 p-3 mb-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-file-pdf text-danger fs-2"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">{{ $document->original_filename }}</h6>
                                <small class="text-muted">
                                    Téléversé le {{ $document->created_at->format('d/m/Y à H:i') }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- OTP Form (piloté par API) -->
                    <form id="otpForm">
                        <input type="hidden" name="document_id" value="{{ $document->id }}">
                        <div class="mb-4">
                            <label class="form-label fw-semibold mb-3">
                                <i class="fas fa-key text-primary me-2"></i>
                                Code de vérification
                            </label>
                            
                            <div class="d-flex justify-content-center mb-3">
                                <div class="otp-inputs d-flex gap-2">
                                    @for($i = 0; $i < 6; $i++)
                                        <input 
                                            type="text" 
                                            class="otp-input form-control text-center" 
                                            maxlength="1" 
                                            pattern="[0-9]" 
                                            inputmode="numeric"
                                            data-index="{{ $i }}"
                                            {{ $i === 0 ? 'autofocus' : '' }}
                                        >
                                    @endfor
                                </div>
                            </div>
                            
                            <input type="hidden" name="code" id="otpCode">
                            
                            @error('code')
                                <div class="text-danger text-center">
                                    <small><i class="fas fa-exclamation-circle me-1"></i>{{ $message }}</small>
                                </div>
                            @enderror
                        </div>

                        <!-- Method Info -->
                        <div class="text-center mb-4">
                            <div class="d-inline-flex align-items-center bg-primary bg-opacity-10 rounded-pill px-3 py-2">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <small class="text-primary">
                                    Code envoyé à <strong>{{ Auth::user()->email }}</strong>
                                </small>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid mb-4">
                            <button type="button" class="btn btn-primary btn-lg" id="verifyBtn" disabled>
                                <i class="fas fa-check-circle me-2"></i>
                                Vérifier le code
                            </button>
                        </div>

                        <!-- Timer -->
                        <div class="text-center mb-4">
                            <div id="timer" class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Code valide pendant <span id="countdown">10:00</span>
                            </div>
                        </div>

                        <!-- Resend Options -->
                        <div class="text-center">
                            <p class="text-muted mb-3">Vous n'avez pas reçu le code ?</p>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <form class="d-inline" id="resendEmailForm">
                                    <input type="hidden" name="type" value="email">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="resendEmailBtn">
                                        <i class="fas fa-envelope me-1"></i>
                                        Renvoyer par e-mail
                                    </button>
                                </form>
                                
                                @if(Auth::user()->phone)
                                    <form class="d-inline" id="resendSmsForm">
                                        <input type="hidden" name="type" value="sms">
                                        <button type="button" class="btn btn-outline-success btn-sm" id="resendSmsBtn">
                                            <i class="fas fa-sms me-1"></i>
                                            Renvoyer par SMS
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </form>

                    <!-- Help Section -->
                    <div class="mt-5 pt-4 border-top">
                        <div class="row g-3 text-center">
                            <div class="col-4">
                                <div class="text-primary mb-2">
                                    <i class="fas fa-shield-alt fs-5"></i>
                                </div>
                                <small class="text-muted">Sécurisé</small>
                            </div>
                            <div class="col-4">
                                <div class="text-success mb-2">
                                    <i class="fas fa-clock fs-5"></i>
                                </div>
                                <small class="text-muted">Rapide</small>
                            </div>
                            <div class="col-4">
                                <div class="text-info mb-2">
                                    <i class="fas fa-user-check fs-5"></i>
                                </div>
                                <small class="text-muted">Authentifié</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Back Button -->
            <div class="text-center mt-3">
                <a href="{{ route('documents.index') }}" class="btn btn-link text-muted">
                    <i class="fas fa-arrow-left me-1"></i>
                    Retour aux documents
                </a>
            </div>
        </div>
    </div>
</div>


@push('scripts')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const otpInputs = document.querySelectorAll('.otp-input');
    const otpCodeInput = document.getElementById('otpCode');
    const verifyBtn = document.getElementById('verifyBtn');
    const otpForm = document.getElementById('otpForm');
    const countdownElement = document.getElementById('countdown');
    const documentId = {{ $document->id }};
       @isset($otp_expires_at)
        let expiresAt = new Date("{{ $otp_expires_at }}");
    @else
        let expiresAt = null;
    @endisset

    // Sécuriser expiresAt: si absent/illisible, on part sur +10 minutes
    if (!expiresAt || isNaN(expiresAt.getTime())) {
        expiresAt = new Date(Date.now() + 10 * 60 * 1000);
    }

    
 

    // OTP Input Management
    otpInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            const value = e.target.value;
            if (!/^\d*$/.test(value)) {
                e.target.value = '';
                return;
            }
            if (value && index < otpInputs.length - 1) {
                otpInputs[index + 1].focus();
            }
            updateOTPCode();
        });
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                otpInputs[index - 1].focus();
            }
            if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                navigator.clipboard.readText().then(text => {
                    const digits = text.replace(/\D/g, '').slice(0, 6);
                    digits.split('').forEach((digit, i) => {
                        if (otpInputs[i]) otpInputs[i].value = digit;
                    });
                    updateOTPCode();
                    if (digits.length === 6) otpInputs[5].focus();
                });
            }
        });
        input.addEventListener('focus', function() { this.select(); });
    });

    function updateOTPCode() {
        const code = Array.from(otpInputs).map(input => input.value).join('');
        otpCodeInput.value = code;
        verifyBtn.disabled = code.length !== 6;
    }

    // Countdown Timer (dynamique)
    function updateCountdown() {
        const now = new Date();
        let diff = Math.floor((expiresAt - now) / 1000);
        if (!Number.isFinite(diff)) diff = 0;
        if (diff < 0) diff = 0;

        const minutes = Math.floor(diff / 60);
        const seconds = diff % 60;
        countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        // Marquer expiré uniquement si l'on a bien un timer arrivé à 0
        if (minutes === 0 && seconds === 0) {
            document.getElementById('timer').innerHTML = '<span class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Code expiré</span>';
            // On laisse les inputs actifs pour permettre un renvoi immédiat
            otpInputs.forEach(input => input.disabled = true);
            verifyBtn.disabled = true;
            verifyBtn.innerHTML = '<i class="fas fa-times me-2"></i>Code expiré';
            verifyBtn.classList.remove('btn-primary');
            verifyBtn.classList.add('btn-danger');
        }
    }
    setInterval(updateCountdown, 1000);

    // Vérification via API
    verifyBtn.addEventListener('click', async function() {
        const code = otpCodeInput.value;
        if (!code || code.length !== 6) return;
        const original = verifyBtn.innerHTML;
        verifyBtn.disabled = true;
        verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Vérification...';
        try {
            // Utiliser la route web au lieu de l'API
            const response = await fetch(`/documents/${documentId}/verify-otp`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ code: code })
            });
            
            const res = await response.json();
            
            if (res?.success) {
                toastr.success(res.message || 'Code vérifié');
                // Rediriger vers le flux de signature approprié
                window.location.href = `/signatures/${documentId}/preview`;
            } else {
                toastr.error(res?.message || 'Code invalide');
            }
        } catch (e) {
            toastr.error(e.message || 'Erreur de vérification');
        } finally {
            verifyBtn.disabled = false;
            verifyBtn.innerHTML = original;
        }
    });

    // Renvoi OTP via API
    document.getElementById('resendEmailBtn')?.addEventListener('click', () => resend('email'));
    document.getElementById('resendSmsBtn')?.addEventListener('click', () => resend('sms'));

    async function resend(type) {
        const btn = type === 'email' ? document.getElementById('resendEmailBtn') : document.getElementById('resendSmsBtn');
        const original = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Envoi...';
        try {
            // Utiliser la route web au lieu de l'API
            const response = await fetch(`/documents/${documentId}/generate-otp`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ type: type })
            });
            
            const res = await response.json();
            
            if (res?.success) {
                toastr.success(res.message || 'Code renvoyé avec succès');
                if (res.data?.expires_at) expiresAt = new Date(res.data.expires_at);
                otpInputs.forEach(i => { i.value = ''; i.disabled = false; });
                verifyBtn.disabled = true;
                verifyBtn.innerHTML = '<i class="fas fa-check-circle me-2"></i>Vérifier le code';
                verifyBtn.classList.remove('btn-danger');
                verifyBtn.classList.add('btn-primary');
            } else {
                toastr.error(res?.message || 'Erreur lors de l\'envoi du code.');
            }
        } catch (e) {
            toastr.error(e.message || 'Erreur lors de l\'envoi');
        } finally {
            btn.disabled = false;
            btn.innerHTML = original;
        }
    }

    // Auto-focus first input
    if (otpInputs[0]) otpInputs[0].focus();
});
</script>
@if(session('success'))
    <script>toastr.success('{{ session('success') }}');</script>
@endif
@if(session('error'))
    <script>toastr.error('{{ session('error') }}');</script>
@endif
@endpush

@push('styles')
<style>
.otp-input {
    width: 3.5rem;
    height: 3.5rem;
    font-size: 1.5rem;
    font-weight: 600;
    border: 2px solid #e9ecef;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.otp-input:focus {
    border-color: var(--ss-primary);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    transform: scale(1.05);
}

.otp-input.is-invalid {
    border-color: var(--ss-danger);
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.card {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
}

.bg-primary.bg-opacity-10 {
    background-color: rgba(99, 102, 241, 0.1) !important;
}

@media (max-width: 576px) {
    .otp-input {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.2rem;
    }
    
    .card-body {
        padding: 2rem !important;
    }
    
    .otp-inputs {
        gap: 0.5rem !important;
    }
}

/* Loading states */
.btn:disabled {
    opacity: 0.7;
}

/* Timer styles */
#timer {
    font-size: 0.9rem;
}

#countdown {
    font-weight: 600;
    color: var(--ss-primary);
}

/* Resend buttons */
.btn-outline-primary:hover,
.btn-outline-success:hover {
    transform: translateY(-1px);
}
</style>
@endpush
@endsection

@section('scripts')
@endsection
