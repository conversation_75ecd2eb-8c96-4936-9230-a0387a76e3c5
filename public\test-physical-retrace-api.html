<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Physical Retrace</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .info {
            border-color: #17a2b8;
            background-color: #d1ecf1;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            background-color: #f8f9fa;
        }
        .tab.active {
            background-color: white;
            border-color: #dee2e6;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>🧪 Test API Physical Retrace</h1>
    
    <div class="container">
        <div class="form-group">
            <label for="baseUrl">URL de base de l'API:</label>
            <input type="text" id="baseUrl" value="http://localhost/signsecure/public/api/v1" />
        </div>
        <div class="form-group">
            <label for="token">Token Sanctum:</label>
            <input type="text" id="token" placeholder="Entrez votre token d'authentification" />
        </div>
    </div>

    <div class="tabs">
        <div class="tab active" onclick="showTab('create')">Créer un retrace</div>
        <div class="tab" onclick="showTab('otp')">Demander OTP</div>
        <div class="tab" onclick="showTab('verify')">Vérifier OTP</div>
        <div class="tab" onclick="showTab('list')">Lister les retraces</div>
        <div class="tab" onclick="showTab('show')">Afficher un retrace</div>
    </div>

    <!-- Tab 1: Créer un retrace -->
    <div id="create" class="tab-content active">
        <div class="container">
            <h3>Créer un retrace directement</h3>
            <div class="form-group">
                <label for="createDate">Date:</label>
                <input type="date" id="createDate" value="2024-01-15" />
            </div>
            <div class="form-group">
                <label for="createReference">Référence:</label>
                <input type="text" id="createReference" value="TEST-REF-001" />
            </div>
            <div class="form-group">
                <label for="createSubject">Sujet:</label>
                <input type="text" id="createSubject" value="Test de création via API" />
            </div>
            <div class="form-group">
                <label for="createNotes">Notes:</label>
                <textarea id="createNotes" rows="3">Ceci est un test de l'API Physical Retrace</textarea>
            </div>
            <button onclick="createRetrace()">Créer le retrace</button>
            <div id="createResponse" class="response"></div>
        </div>
    </div>

    <!-- Tab 2: Demander OTP -->
    <div id="otp" class="tab-content">
        <div class="container">
            <h3>Demander un code OTP</h3>
            <div class="form-group">
                <label for="otpDate">Date:</label>
                <input type="date" id="otpDate" value="2024-01-15" />
            </div>
            <div class="form-group">
                <label for="otpReference">Référence:</label>
                <input type="text" id="otpReference" value="TEST-REF-002" />
            </div>
            <div class="form-group">
                <label for="otpSubject">Sujet:</label>
                <input type="text" id="otpSubject" value="Test de demande OTP via API" />
            </div>
            <div class="form-group">
                <label for="otpNotes">Notes:</label>
                <textarea id="otpNotes" rows="3">Ceci est un test de la demande OTP</textarea>
            </div>
            <button onclick="requestOtp()">Demander OTP</button>
            <div id="otpResponse" class="response"></div>
        </div>
    </div>

    <!-- Tab 3: Vérifier OTP -->
    <div id="verify" class="tab-content">
        <div class="container">
            <h3>Vérifier OTP et créer le retrace</h3>
            <div class="form-group">
                <label for="verifyCode">Code OTP:</label>
                <input type="text" id="verifyCode" placeholder="Entrez le code OTP reçu" maxlength="6" />
            </div>
            <div class="form-group">
                <label for="verifyDate">Date:</label>
                <input type="date" id="verifyDate" value="2024-01-15" />
            </div>
            <div class="form-group">
                <label for="verifyReference">Référence:</label>
                <input type="text" id="verifyReference" value="TEST-REF-003" />
            </div>
            <div class="form-group">
                <label for="verifySubject">Sujet:</label>
                <input type="text" id="verifySubject" value="Test de vérification OTP via API" />
            </div>
            <div class="form-group">
                <label for="verifyNotes">Notes:</label>
                <textarea id="verifyNotes" rows="3">Ceci est un test de la vérification OTP</textarea>
            </div>
            <button onclick="verifyOtp()">Vérifier OTP et créer</button>
            <div id="verifyResponse" class="response"></div>
        </div>
    </div>

    <!-- Tab 4: Lister les retraces -->
    <div id="list" class="tab-content">
        <div class="container">
            <h3>Lister tous les retraces</h3>
            <div class="form-group">
                <label for="perPage">Éléments par page:</label>
                <input type="number" id="perPage" value="15" min="1" max="100" />
            </div>
            <button onclick="listRetraces()">Lister les retraces</button>
            <div id="listResponse" class="response"></div>
        </div>
    </div>

    <!-- Tab 5: Afficher un retrace -->
    <div id="show" class="tab-content">
        <div class="container">
            <h3>Afficher un retrace spécifique</h3>
            <div class="form-group">
                <label for="retraceId">ID du retrace:</label>
                <input type="number" id="retraceId" placeholder="Entrez l'ID du retrace" min="1" />
            </div>
            <button onclick="showRetrace()">Afficher le retrace</button>
            <div id="showResponse" class="response"></div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Masquer tous les tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Afficher le tab sélectionné
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function getBaseUrl() {
            return document.getElementById('baseUrl').value;
        }

        function getToken() {
            return document.getElementById('token').value;
        }

        function showResponse(elementId, response, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(response, null, 2);
            element.className = 'response ' + (isSuccess ? 'success' : 'error');
        }

        async function makeRequest(url, options = {}) {
            const token = getToken();
            if (!token) {
                alert('Veuillez entrer un token d\'authentification');
                return null;
            }

            const defaultOptions = {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };

            try {
                const response = await fetch(url, { ...defaultOptions, ...options });
                const data = await response.json();
                return { response, data, status: response.status };
            } catch (error) {
                return { error: error.message };
            }
        }

        async function createRetrace() {
            const data = {
                date: document.getElementById('createDate').value,
                reference: document.getElementById('createReference').value,
                subject: document.getElementById('createSubject').value,
                notes: document.getElementById('createNotes').value
            };

            const result = await makeRequest(`${getBaseUrl()}/retraces`, {
                method: 'POST',
                body: JSON.stringify(data)
            });

            if (result.error) {
                showResponse('createResponse', { error: result.error }, false);
            } else {
                showResponse('createResponse', result.data, result.status < 400);
            }
        }

        async function requestOtp() {
            const data = {
                date: document.getElementById('otpDate').value,
                reference: document.getElementById('otpReference').value,
                subject: document.getElementById('otpSubject').value,
                notes: document.getElementById('otpNotes').value
            };

            const result = await makeRequest(`${getBaseUrl()}/retraces/request-otp`, {
                method: 'POST',
                body: JSON.stringify(data)
            });

            if (result.error) {
                showResponse('otpResponse', { error: result.error }, false);
            } else {
                showResponse('otpResponse', result.data, result.status < 400);
            }
        }

        async function verifyOtp() {
            const data = {
                code: document.getElementById('verifyCode').value,
                date: document.getElementById('verifyDate').value,
                reference: document.getElementById('verifyReference').value,
                subject: document.getElementById('verifySubject').value,
                notes: document.getElementById('verifyNotes').value
            };

            const result = await makeRequest(`${getBaseUrl()}/retraces/verify-otp`, {
                method: 'POST',
                body: JSON.stringify(data)
            });

            if (result.error) {
                showResponse('verifyResponse', { error: result.error }, false);
            } else {
                showResponse('verifyResponse', result.data, result.status < 400);
            }
        }

        async function listRetraces() {
            const perPage = document.getElementById('perPage').value;
            const url = `${getBaseUrl()}/retraces?per_page=${perPage}`;

            const result = await makeRequest(url, { method: 'GET' });

            if (result.error) {
                showResponse('listResponse', { error: result.error }, false);
            } else {
                showResponse('listResponse', result.data, result.status < 400);
            }
        }

        async function showRetrace() {
            const id = document.getElementById('retraceId').value;
            if (!id) {
                alert('Veuillez entrer un ID de retrace');
                return;
            }

            const result = await makeRequest(`${getBaseUrl()}/retraces/${id}`, { method: 'GET' });

            if (result.error) {
                showResponse('showResponse', { error: result.error }, false);
            } else {
                showResponse('showResponse', result.data, result.status < 400);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Page de test de l\'API Physical Retrace chargée');
            console.log('📝 Instructions:');
            console.log('1. Assurez-vous que votre serveur Laravel est démarré');
            console.log('2. Entrez votre token Sanctum d\'authentification');
            console.log('3. Testez les différents endpoints de l\'API');
        });
    </script>
</body>
</html>
