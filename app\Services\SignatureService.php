<?php

namespace App\Services;

use App\Models\Document;
use App\Models\User;
use App\Models\Signature;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use setasign\Fpdi\Fpdi;

class SignatureService
{
    /**
     * Sauvegarde la signature d'un utilisateur
     */
    public function saveUserSignature(User $user, string $signatureData): bool
    {
        try {
            // Convertir base64 en fichier
            $imageData = base64_decode(explode(',', $signatureData)[1]);
            $filename = 'signatures/' . Str::uuid() . '.png';
            
            // Sauvegarder l'image
            Storage::disk('private')->put($filename, $imageData);
            
            // Mettre à jour l'utilisateur
            $user->update([
                'signature_image' => $signatureData,
                'signature_path' => $filename,
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Erreur sauvegarde signature', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Applique une signature sur un document PDF
     */
    public function applySignatureToDocument(Document $document, User $user, float $x, float $y, int $pageNumber): Signature
    {
        try {
            // Vérifier que l'utilisateur a une signature
            if (!$user->hasSignature()) {
                throw new \Exception('L\'utilisateur n\'a pas de signature enregistrée');
            }

            // Créer une copie du document original
            $signedFilename = 'documents/signed/' . Str::uuid() . '.pdf';
            $originalPath = Storage::disk('private')->path($document->file_path);
            $signedPath = Storage::disk('private')->path($signedFilename);

            // Créer le répertoire si nécessaire
            Storage::disk('private')->makeDirectory('documents/signed');

            // Appliquer la signature sur le PDF
            $this->applySignatureToPDF($originalPath, $signedPath, $user->signature_image, $x, $y, $pageNumber);

            // Créer l'enregistrement de signature
            $signature = Signature::create([
                'document_id' => $document->id,
                'user_id' => $user->id,
                'position_x' => $x,
                'position_y' => $y,
                'page_number' => $pageNumber,
                'signature_image' => $user->signature_image,
                'applied_at' => now(),
            ]);

            // Mettre à jour le document
            $document->update([
                'signed_file_path' => $signedFilename,
            ]);

            return $signature;

        } catch (\Exception $e) {
            Log::error('Erreur application signature', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Applique plusieurs signatures sur un document PDF en une seule opération
     * @param array<int, array{x:float,y:float,page:int}> $positions
     * @return array<int, Signature>
     */
    public function applyMultipleSignaturesToDocument(Document $document, User $user, array $positions): array
    {
        try {
            if (!$user->hasSignature()) {
                throw new \Exception("L'utilisateur n'a pas de signature enregistrée");
            }

            $signedFilename = 'documents/signed/' . Str::uuid() . '.pdf';
            $originalPath = Storage::disk('private')->path($document->file_path);
            $signedPath = Storage::disk('private')->path($signedFilename);

            // Créer le répertoire si nécessaire
            Storage::disk('private')->makeDirectory('documents/signed');

            // Appliquer toutes les signatures sur le PDF
            $this->applyMultipleSignaturesToPDF($originalPath, $signedPath, $user->signature_image, $positions);

            $signatures = [];

            // Créer les enregistrements de signature
            foreach ($positions as $position) {
                $signature = Signature::create([
                    'document_id' => $document->id,
                    'user_id' => $user->id,
                    'position_x' => $position['x'],
                    'position_y' => $position['y'],
                    'page_number' => $position['page'],
                    'signature_image' => $user->signature_image,
                    'applied_at' => now(),
                ]);

                $signatures[] = $signature;
            }

            // Mettre à jour le document
            $document->update([
                'signed_file_path' => $signedFilename,
            ]);

            return $signatures;

        } catch (\Exception $e) {
            Log::error('Erreur application signatures multiples', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Applique une signature sur un PDF en utilisant FPDI
     */
    private function applySignatureToPDF(string $inputPath, string $outputPath, string $signatureImage, float $x, float $y, int $pageNumber): void
    {
        try {
            $pdf = new Fpdi();

            // Ouvrir le PDF source
            $pageCount = $pdf->setSourceFile($inputPath);

            // Parcourir toutes les pages
            for ($i = 1; $i <= $pageCount; $i++) {
                // Importer la page
                $tplIdx = $pdf->importPage($i);
                $size = $pdf->getTemplateSize($tplIdx);
                $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                $pdf->useTemplate($tplIdx);

                // Ajouter la signature sur la page spécifiée
                if ($i === $pageNumber) {
                    $this->addSignatureToPage($pdf, $signatureImage, $x, $y);
                }
            }

            // Sauvegarder le PDF signé
            $pdf->Output($outputPath, 'F');

        } catch (\Exception $e) {
            Log::error('Erreur application signature PDF', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Applique plusieurs signatures sur un PDF
     */
    private function applyMultipleSignaturesToPDF(string $inputPath, string $outputPath, string $signatureImage, array $positions): void
    {
        try {
            $pdf = new Fpdi();

            // Ouvrir le PDF source
            $pageCount = $pdf->setSourceFile($inputPath);

            // Parcourir toutes les pages
            for ($i = 1; $i <= $pageCount; $i++) {
                // Importer la page
                $tplIdx = $pdf->importPage($i);
                $size = $pdf->getTemplateSize($tplIdx);
                $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                $pdf->useTemplate($tplIdx);

                // Ajouter les signatures sur cette page
                foreach ($positions as $position) {
                    if ($position['page'] === $i) {
                        $this->addSignatureToPage($pdf, $signatureImage, $position['x'], $position['y']);
                    }
                }
            }

            // Sauvegarder le PDF signé
            $pdf->Output($outputPath, 'F');

        } catch (\Exception $e) {
            Log::error('Erreur application signatures multiples PDF', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Ajoute la signature sur une page spécifique
     */
    private function addSignatureToPage(Fpdi $pdf, string $signatureImage, float $xMm, float $yMm, float $widthMm = 50.0, float $heightMm = 25.0): void
    {
        try {
            // Convertir base64 en fichier temporaire
            $imageData = base64_decode(explode(',', $signatureImage)[1]);
            // FPDF a besoin d'une extension ou du type explicite, nous créons un .png
            $tempFile = tempnam(sys_get_temp_dir(), 'signature_') . '.png';
            file_put_contents($tempFile, $imageData);

            // Ajouter l'image à la position spécifiée (unités en mm)
            // Spécifier explicitement le type PNG pour éviter les erreurs de type
            $pdf->Image($tempFile, $xMm, $yMm, $widthMm, $heightMm, 'PNG');

            // Nettoyer le fichier temporaire
            unlink($tempFile);

        } catch (\Exception $e) {
            Log::error('Erreur ajout signature page', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Génère un code de suivi unique pour un document
     */
    public function generateTrackingCode(Document $document): string
    {
        // Nouveau format requis: SS-XXXX-XXXX (X = A-Z ou 0-9)
        // Doit être unique dans la table documents.tracking_code
        do {
            $part1 = strtoupper(Str::random(4));
            $part2 = strtoupper(Str::random(4));
            // Nettoyer pour ne garder que alphanum
            $part1 = preg_replace('/[^A-Z0-9]/', 'A', $part1);
            $part2 = preg_replace('/[^A-Z0-9]/', 'A', $part2);
            $code = "SS-{$part1}-{$part2}";
        } while (Document::where('tracking_code', $code)->exists());

        return $code;
    }

    /**
     * Vérifie si un document peut être signé par un utilisateur
     */
    public function canUserSignDocument(Document $document, User $user): bool
    {
        // Vérifier que l'utilisateur est un signataire du document
        $signer = $document->signers()
            ->where('user_id', $user->id)
            ->where('status', 'pending')
            ->first();

        if (!$signer) {
            return false;
        }

        // Vérifier que c'est son tour de signer
        return $signer->is_current_signer;
    }

    /**
     * Récupère les statistiques de signature pour un utilisateur
     */
    public function getUserSignatureStats(User $user): array
    {
        $totalSignatures = $user->signatures()->count();
        $recentSignatures = $user->signatures()
            ->where('applied_at', '>=', now()->subDays(30))
            ->count();

        $documentsSigned = $user->documentSigners()
            ->where('status', 'signed')
            ->count();

        return [
            'total_signatures' => $totalSignatures,
            'recent_signatures' => $recentSignatures,
            'documents_signed' => $documentsSigned,
        ];
    }

    /**
     * Valide une signature (vérification d'intégrité)
     */
    public function validateSignature(Signature $signature): bool
    {
        try {
            // Vérifier que le fichier signé existe
            if (!Storage::disk('private')->exists($signature->document->signed_file_path)) {
                return false;
            }

            // Vérifier que l'image de signature existe
            if (empty($signature->signature_image)) {
                return false;
            }

            // Vérifier que les coordonnées sont valides
            if ($signature->position_x < 0 || $signature->position_y < 0 || $signature->page_number < 1) {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Erreur validation signature', ['error' => $e->getMessage()]);
            return false;
        }
    }
}
