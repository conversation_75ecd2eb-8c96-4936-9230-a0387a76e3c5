/**
 * Script pour la page d'inscription
 */

document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    const registerBtn = document.getElementById('registerBtn');
    const originalBtnText = registerBtn.innerHTML;

    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Nettoyer les erreurs précédentes
        SignSecureUI.displayValidationErrors({});
        
        const name = document.getElementById('name').value.trim();
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const passwordConfirmation = document.getElementById('password_confirmation').value;

        // Validation côté client
        const errors = {};
        if (!name) errors.name = ['Le nom est obligatoire.'];
        if (!email) errors.email = ['L\'email est obligatoire.'];
        if (!password) errors.password = ['Le mot de passe est obligatoire.'];
        // Strong password client-side check: 8+ chars, upper, lower, number, symbol
        const strongPwdRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,}$/;
        if (password && !strongPwdRegex.test(password)) {
            errors.password = ['Mot de passe trop faible: 8+ caractères, majuscules, minuscules, chiffres et symboles.'];
        }
        if (password !== passwordConfirmation) {
            errors.password_confirmation = ['La confirmation du mot de passe ne correspond pas.'];
        }
        const terms = document.getElementById('terms');
        if (!terms || !terms.checked) {
            errors.terms = ['Vous devez accepter les conditions d\'utilisation.'];
        }

        if (Object.keys(errors).length > 0) {
            SignSecureUI.displayValidationErrors(errors);
            return;
        }

        // Afficher le loader
        SignSecureUI.showLoader(registerBtn);

        try {
            const response = await signSecureAPI.register({
                name,
                email,
                password,
                password_confirmation: passwordConfirmation
            });
            
            if (!response.success) {
                throw new Error(response.message || 'Erreur lors de l\'inscription');
            }
            
            // Stocker le token et créer une session web
            const token = response.data?.token;
            if (token) {
                signSecureAPI.setToken(token);
                try {
                    await signSecureAPI.createWebSession();
                } catch (e) {
                    console.warn('Échec de création de session web:', e);
                }
            }

            SignSecureUI.showToast('Inscription réussie !', 'success');
            
            // Rediriger vers le dashboard après inscription et connexion automatique
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);

        } catch (error) {
            console.error('Erreur d\'inscription:', error);
            
            if (error.data && error.data.errors) {
                // Gérer les erreurs de validation
                SignSecureUI.displayValidationErrors(error.data.errors);
            } else {
                SignSecureUI.showToast(error.message || 'Erreur d\'inscription', 'error');
            }
        } finally {
            SignSecureUI.hideLoader(registerBtn, originalBtnText);
        }
    });

    // Toggle password visibility for password and password confirmation
    document.querySelectorAll('.toggle-password').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const passwordInput = document.getElementById(targetId);
            
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    });

    // Password strength indicator (basic example)
    const passwordInput = document.getElementById('password');
    const passwordStrength = document.getElementById('passwordStrength');
    const passwordConfirmInput = document.getElementById('password_confirmation');
    const passwordMatchText = document.getElementById('passwordMatch');
    const passwordMatchBar = document.getElementById('passwordMatchBar');

    if (passwordInput && passwordStrength) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;

            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^\w\s]/.test(password)) strength++;

            let strengthText = '';
            let strengthClass = '';

            switch (strength) {
                case 0:
                case 1:
                    strengthText = 'Très faible';
                    strengthClass = 'text-danger';
                    break;
                case 2:
                    strengthText = 'Faible';
                    strengthClass = 'text-warning';
                    break;
                case 3:
                    strengthText = 'Moyenne';
                    strengthClass = 'text-info';
                    break;
                case 4:
                    strengthText = 'Bonne';
                    strengthClass = 'text-primary';
                    break;
                case 5:
                    strengthText = 'Excellente';
                    strengthClass = 'text-success';
                    break;
            }

            passwordStrength.textContent = `Force du mot de passe: ${strengthText}`;
            passwordStrength.className = `form-text ${strengthClass}`;
        });
    }

    // Password match indicator bar (green if match, red if not)
    function updatePasswordMatch() {
        if (!passwordConfirmInput || !passwordMatchBar || !passwordMatchText) return;
        const pwd = passwordInput.value;
        const conf = passwordConfirmInput.value;
        if (!conf && !pwd) {
            passwordMatchBar.style.backgroundColor = '#e5e7eb';
            passwordMatchText.textContent = '';
            return;
        }
        if (pwd && conf && pwd === conf) {
            passwordMatchBar.style.backgroundColor = '#10b981';
            passwordMatchText.textContent = 'Les mots de passe correspondent.';
            passwordMatchText.className = 'form-text text-success';
        } else {
            passwordMatchBar.style.backgroundColor = '#ef4444';
            passwordMatchText.textContent = 'Les mots de passe ne correspondent pas.';
            passwordMatchText.className = 'form-text text-danger';
        }
    }

    if (passwordInput) passwordInput.addEventListener('input', updatePasswordMatch);
    if (passwordConfirmInput) passwordConfirmInput.addEventListener('input', updatePasswordMatch);
    if (passwordInput) passwordInput.addEventListener('blur', updatePasswordMatch);
    if (passwordConfirmInput) passwordConfirmInput.addEventListener('blur', updatePasswordMatch);

    // Initialize match state once on load
    updatePasswordMatch();

    // Open modals without flicker and prevent label/checkbox toggling
    const openTermsLink = document.getElementById('openTermsLink');
    const openPrivacyLink = document.getElementById('openPrivacyLink');
    const termsModalEl = document.getElementById('termsModal');
    const privacyModalEl = document.getElementById('privacyModal');

    // Initialize modal instances once to avoid flicker
    const termsModal = termsModalEl ? new bootstrap.Modal(termsModalEl, { backdrop: 'static', keyboard: false }) : null;
    const privacyModal = privacyModalEl ? new bootstrap.Modal(privacyModalEl, { backdrop: 'static', keyboard: false }) : null;

    if (openTermsLink && termsModal) {
        openTermsLink.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            termsModal.show();
        });
    }

    if (openPrivacyLink && privacyModal) {
        openPrivacyLink.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            privacyModal.show();
        });
    }
});
