<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SignatureTokenMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct()
    {
    }

    public function build()
    {
        return $this->subject('Signature - SignSecure')
            ->view('emails.signature-token')
            ->with([
                'message' => 'Ce mail n\'est plus utilisé dans la version actuelle.',
            ]);
    }
}



