<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Optimiser la table documents
        Schema::table('documents', function (Blueprint $table) {
            // Ajouter des index pour améliorer les performances
            $table->index(['user_id', 'status']);
            $table->index(['tracking_code']);
            $table->index(['created_at']);
            
            // Ajouter des contraintes de validation
            $table->string('status')->default('pending')->change();
            
            // Ajouter des champs manquants pour la sécurité
            $table->string('hash_original')->nullable()->comment('Hash SHA256 du fichier original');
            $table->string('hash_signed')->nullable()->comment('Hash SHA256 du fichier signé');
            $table->text('metadata')->nullable()->comment('Métadonnées JSON du document');
            
            // Améliorer le tracking
            $table->string('verification_url')->nullable()->comment('URL de vérification publique');
            $table->timestamp('expires_at')->nullable()->comment('Date d\'expiration du document');
        });

        // Optimiser la table signatures
        Schema::table('signatures', function (Blueprint $table) {
            // Ajouter des champs pour la position et la sécurité
            $table->decimal('position_x', 10, 2)->change();
            $table->decimal('position_y', 10, 2)->change();
            $table->integer('page_number')->unsigned()->change();
            
            // Ajouter des champs de sécurité
            $table->string('signature_hash')->nullable()->comment('Hash de la signature pour vérification');
            $table->timestamp('verified_at')->nullable()->comment('Date de vérification de la signature');
            $table->string('verification_method')->nullable()->comment('Méthode de vérification utilisée');
            
            // Ajouter des contraintes
            $table->index(['document_id', 'user_id']);
            $table->index(['applied_at']);
        });

        // Optimiser la table OTP codes
        Schema::table('otp_codes', function (Blueprint $table) {
            // Améliorer la sécurité
            $table->string('ip_address')->nullable()->comment('Adresse IP de la demande');
            $table->string('user_agent')->nullable()->comment('User agent du navigateur');
            $table->integer('attempts')->default(0)->comment('Nombre de tentatives');
            $table->timestamp('locked_until')->nullable()->comment('Verrouillage temporaire');
            
            // Ajouter des contraintes
            $table->index(['user_id', 'document_id', 'created_at']);
            $table->index(['code', 'expires_at']);
        });

        // Créer une table pour les logs de vérification
        Schema::create('verification_logs', function (Blueprint $table) {
            $table->id();
            $table->string('tracking_code');
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('verification_result'); // success, failed, expired
            $table->text('verification_details')->nullable();
            $table->timestamp('verified_at');
            $table->timestamps();
            
            $table->index(['tracking_code', 'verified_at']);
            $table->index(['verification_result', 'verified_at']);
        });

        // Créer une table pour les paramètres de sécurité
        Schema::create('security_settings', function (Blueprint $table) {
            $table->id();
            $table->string('setting_key')->unique();
            $table->text('setting_value');
            $table->string('description')->nullable();
            $table->timestamps();
        });

        // Insérer les paramètres de sécurité par défaut
        DB::table('security_settings')->insert([
            [
                'setting_key' => 'otp_expiration_minutes',
                'setting_value' => '10',
                'description' => 'Durée de validité des codes OTP en minutes'
            ],
            [
                'setting_key' => 'max_otp_attempts',
                'setting_value' => '3',
                'description' => 'Nombre maximum de tentatives OTP'
            ],
            [
                'setting_key' => 'signature_expiration_days',
                'setting_value' => '365',
                'description' => 'Durée de validité des signatures en jours'
            ],
            [
                'setting_key' => 'max_file_size_mb',
                'setting_value' => '10',
                'description' => 'Taille maximale des fichiers en MB'
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer les nouvelles tables
        Schema::dropIfExists('verification_logs');
        Schema::dropIfExists('security_settings');
        
        // Supprimer les colonnes ajoutées
        Schema::table('documents', function (Blueprint $table) {
            $table->dropColumn(['hash_original', 'hash_signed', 'metadata', 'verification_url', 'expires_at']);
            $table->dropIndex(['user_id', 'status']);
            $table->dropIndex(['tracking_code']);
            $table->dropIndex(['created_at']);
        });
        
        Schema::table('signatures', function (Blueprint $table) {
            $table->dropColumn(['signature_hash', 'verified_at', 'verification_method']);
            $table->dropIndex(['document_id', 'user_id']);
            $table->dropIndex(['applied_at']);
        });
        
        Schema::table('otp_codes', function (Blueprint $table) {
            $table->dropColumn(['ip_address', 'user_agent', 'attempts', 'locked_until']);
            $table->dropIndex(['user_id', 'document_id', 'created_at']);
            $table->dropIndex(['code', 'expires_at']);
        });
    }
};
