<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Corriger le mauvais nom de table éventuel
        if (Schema::hasTable('o_t_p_codes') && !Schema::hasTable('otp_codes')) {
            Schema::rename('o_t_p_codes', 'otp_codes');
        }

        if (Schema::hasTable('otp_codes')) {
            Schema::table('otp_codes', function (Blueprint $table) {
                // Nouvelle colonne pour stocker le hash sans dépendre de change() (pas de doctrine/dbal)
                if (!Schema::hasColumn('otp_codes', 'code_hashed')) {
                    $table->string('code_hashed', 255)->nullable()->after('user_id');
                }

                // Colonnes usuelles si absentes (robustesse)
                if (!Schema::hasColumn('otp_codes', 'type')) {
                    $table->string('type', 20)->nullable()->after('code_hashed');
                }
                if (!Schema::hasColumn('otp_codes', 'expires_at')) {
                    $table->timestamp('expires_at')->nullable()->after('type');
                }
                if (!Schema::hasColumn('otp_codes', 'used_at')) {
                    $table->timestamp('used_at')->nullable()->after('expires_at');
                }
                if (!Schema::hasColumn('otp_codes', 'document_id')) {
                    $table->unsignedBigInteger('document_id')->nullable()->after('user_id');
                }
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('otp_codes')) {
            Schema::table('otp_codes', function (Blueprint $table) {
                if (Schema::hasColumn('otp_codes', 'code_hashed')) {
                    $table->dropColumn('code_hashed');
                }
            });
        }
        // On ne renomme pas la table à l'inverse pour éviter de casser en prod.
    }
};



