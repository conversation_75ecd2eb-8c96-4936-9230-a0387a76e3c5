<?php

namespace App\Services;

class CoordinateConversionService
{
    /**
     * Convertit les coordonnées pixels du canvas en millimètres PDF
     */
    public static function pixelsToMillimeters($xPixels, $yPixels, $canvasWidth, $canvasHeight, $pdfWidth = 210.0, $pdfHeight = 297.0)
    {
        // Convertir les coordonnées relatives (0-1) puis en millimètres
        $xRelative = $xPixels / $canvasWidth;
        $yRelative = $yPixels / $canvasHeight;
        
        // Convertir en millimètres (origine en haut à gauche comme FPDF)
        $xMm = $xRelative * $pdfWidth;
        $yMm = $yRelative * $pdfHeight;
        
        return array(
            'x' => round($xMm, 2),
            'y' => round($yMm, 2)
        );
    }

    /**
     * Convertit les coordonnées millimètres PDF en pixels du canvas
     */
    public static function millimetersToPixels($xMm, $yMm, $canvasWidth, $canvasHeight, $pdfWidth = 210.0, $pdfHeight = 297.0)
    {
        // Convertir les coordonnées relatives (0-1)
        $xRelative = $xMm / $pdfWidth;
        $yRelative = 1 - ($yMm / $pdfHeight); // Inverser Y
        
        // Convertir en pixels
        $xPixels = $xRelative * $canvasWidth;
        $yPixels = $yRelative * $canvasHeight;
        
        return array(
            'x' => round($xPixels, 2),
            'y' => round($yPixels, 2)
        );
    }
}
