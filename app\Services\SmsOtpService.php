<?php

namespace App\Services;

use App\Models\OTPCode;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class SmsOtpService
{
    /**
     * Generate, store and send a 6-digit OTP via FasterMessage SMS API.
     * Returns an array [success => bool, message => string].
     */
    public function sendForEmployee(User $employee): array
    {
        if (!$employee->phone && !$employee->tel) {
            return ['success' => false, 'message' => 'Aucun numéro de téléphone disponible pour cet utilisateur'];
        }

        $phone = $employee->phone ?: $employee->tel;
        $code = $this->generateCode();

        // Invalidate previous active OTPs for user
        OTPCode::where('user_id', $employee->id)
            ->whereNull('used_at')
            ->where('expires_at', '>', now())
            ->update(['used_at' => now()]);

        // Store hashed
        $otp = OTPCode::create([
            'user_id' => $employee->id,
            'document_id' => null,
            'type' => 'sms',
            'code_hashed' => Hash::make($code),
            'expires_at' => now()->addMinutes(10),
        ]);

        $expiresAtHuman = now()->addMinutes(10)->format('d/m/Y H:i');
        $message = $this->buildMessage($code, $expiresAtHuman);

        try {
            $this->sendSms($phone, $message);
            Log::info('OTP SMS envoyé', ['user_id' => $employee->id, 'phone' => $phone]);
            return ['success' => true, 'message' => 'OTP envoyé par SMS'];
        } catch (\Throwable $e) {
            Log::error('Erreur envoi OTP SMS', ['error' => $e->getMessage()]);
            // If send fails, mark OTP as used to avoid dangling codes
            $otp->used_at = now();
            $otp->save();
            return ['success' => false, 'message' => 'Échec envoi SMS: ' . $e->getMessage()];
        }
    }

    private function generateCode(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    private function buildMessage(string $code, string $expiresAt): string
    {
        return "Votre code OTP\nUtilisez ce code pour vérifier votre identité et sécuriser votre opération de signature ou de retrace.\n{$code}\nNe partagez jamais ce code avec personne.\nExpiration: {$expiresAt} (dans 10 minute(s))\nBesoin d’aide ? Contactez-nous :\<EMAIL>\nCet e-mail vous est envoyé par SignSecure. Si vous n’êtes pas à l’origine de cette demande, ignorez ce message.";
    }

    private function sendSms(string $rawPhone, string $text): void
    {
        $endpoint = config('services.fastermessage.endpoint', 'https://fastermessage.com/api/v1/sms/send');
        $apiKey = config('services.fastermessage.api_key');
        $from = config('services.fastermessage.from', 'SignSecure');
        $defaultCode = config('services.fastermessage.default_country_code', '+229');

        if (!$apiKey) {
            throw new \RuntimeException('Clé API FasterMessage manquante');
        }

        // Normalize to E.164
        $normalized = $this->normalizePhoneNumber($rawPhone, $defaultCode);

        $payload = [
            'from' => $from,
            'to' => $normalized,
            'text' => $text,
        ];

        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ["X-API-KEY: " . $apiKey]);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        $result = curl_exec($ch);
        if ($result === false) {
            $err = curl_error($ch);
            curl_close($ch);
            throw new \RuntimeException('Erreur cURL: ' . $err);
        }
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($status < 200 || $status >= 300) {
            throw new \RuntimeException('HTTP ' . $status . ' de FasterMessage: ' . (string)$result);
        }
    }

    private function normalizePhoneNumber(?string $raw, string $defaultCode): string
    {
        $raw = trim((string)$raw);
        $defaultDigits = preg_replace('/\D+/', '', $defaultCode);
        if ($raw === '') {
            throw new \InvalidArgumentException('Numéro de téléphone vide');
        }
        if (str_starts_with($raw, '+')) {
            $digits = preg_replace('/\D+/', '', $raw);
            return '+' . $digits;
        }
        $digits = preg_replace('/\D+/', '', $raw);
        if (str_starts_with($digits, '00')) {
            return '+' . substr($digits, 2);
        }
        if (str_starts_with($digits, $defaultDigits)) {
            return '+' . $digits;
        }
        $digits = ltrim($digits, '0');
        return '+' . $defaultDigits . $digits;
    }
}


