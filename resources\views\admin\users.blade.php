@extends('layouts.app')

@section('title', 'Admin - Utilisateurs')

@section('content')
<div class="container py-4">
    <h3 class="mb-4">Gestion des utilisateurs</h3>
    @if(session('status'))
        <div class="alert alert-success">{{ session('status') }}</div>
    @endif
    @if(session('error'))
        <div class="alert alert-danger">{{ session('error') }}</div>
    @endif
    <div class="card">
        <div class="table-responsive">
            <table class="table table-striped mb-0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Email</th>
                        <th>Rôle</th>
                        <th>C<PERSON>é le</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($users as $user)
                    <tr>
                        <td>{{ $user->id }}</td>
                        <td>{{ $user->name }}</td>
                        <td>{{ $user->email }}</td>
                        <td><span class="badge bg-{{ $user->role === 'admin' ? 'success' : 'secondary' }}">{{ $user->role }}</span></td>
                        <td>{{ $user->created_at->format('d/m/Y H:i') }}</td>
                        <td>
                            @if($user->role === 'admin')
                                <form method="POST" action="{{ url('/admin/users/'.$user->id.'/demote') }}" onsubmit="return confirm('Rétrograder cet utilisateur ?');">
                                    @csrf
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-user-minus me-1"></i>Rétrograder</button>
                                </form>
                            @else
                                <form method="POST" action="{{ url('/admin/users/'.$user->id.'/promote') }}" onsubmit="return confirm('Promouvoir cet utilisateur en admin ?');">
                                    @csrf
                                    <button class="btn btn-sm btn-outline-primary"><i class="fas fa-user-shield me-1"></i>Promouvoir</button>
                                </form>
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="card-body">
            {{ $users->links() }}
        </div>
    </div>
</div>
@endsection


