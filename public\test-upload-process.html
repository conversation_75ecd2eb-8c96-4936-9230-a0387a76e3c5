<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Processus de Téléversement - SignSecure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log-entry { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .log-entry.success { background-color: #d4edda; }
        .log-entry.error { background-color: #f8d7da; }
        .log-entry.info { background-color: #d1ecf1; }
    </style>
</head>
<body>
    <h1>🔍 Test du Processus de Téléversement - SignSecure</h1>
    
    <div class="test-section info">
        <h3>Instructions</h3>
        <p>Cette page teste le processus complet de téléversement pour identifier l'erreur "original_filename sur la chaîne".</p>
        <p>Assurez-vous que le serveur Laravel est démarré avec: <code>php artisan serve</code></p>
    </div>

    <div class="test-section">
        <h3>Test 1: Processus de téléversement complet</h3>
        <button onclick="testUploadProcess()">Tester le processus de téléversement</button>
        <div id="result1"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Test avec document existant</h3>
        <button onclick="testExistingDocument()">Tester avec document existant</button>
        <div id="result2"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Vérification des routes</h3>
        <button onclick="checkRoutes()">Vérifier les routes</button>
        <div id="result3"></div>
    </div>

    <div class="test-section">
        <h3>Logs de débogage</h3>
        <div id="logs"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function displayResult(containerId, data, isSuccess = true) {
            const container = document.getElementById(containerId);
            container.className = `test-section ${isSuccess ? 'success' : 'error'}`;
            container.innerHTML = `
                <h3>${isSuccess ? '✅ Succès' : '❌ Erreur'}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        async function testUploadProcess() {
            try {
                log('🧪 Test du processus de téléversement...');
                const response = await fetch('/api/test/upload-process');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ Processus de téléversement réussi', 'success');
                    displayResult('result1', data, true);
                } else {
                    log('❌ Erreur lors du processus de téléversement', 'error');
                    displayResult('result1', data, false);
                }
            } catch (error) {
                log(`❌ Erreur réseau: ${error.message}`, 'error');
                displayResult('result1', { error: error.message }, false);
            }
        }

        async function testExistingDocument() {
            try {
                log('🧪 Test avec document existant...');
                const response = await fetch('/api/test/existing-document');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ Test avec document existant réussi', 'success');
                    displayResult('result2', data, true);
                } else {
                    log('❌ Erreur lors du test avec document existant', 'error');
                    displayResult('result2', data, false);
                }
            } catch (error) {
                log(`❌ Erreur réseau: ${error.message}`, 'error');
                displayResult('result2', { error: error.message }, false);
            }
        }

        async function checkRoutes() {
            try {
                log('🧪 Vérification des routes...');
                
                const routes = [
                    '/api/test/upload-process',
                    '/api/test/existing-document',
                    '/api/v1/documents/upload',
                    '/api/v1/auth/login'
                ];
                
                const results = {};
                
                for (const route of routes) {
                    try {
                        const response = await fetch(route, { method: 'OPTIONS' });
                        results[route] = {
                            status: response.status,
                            ok: response.ok,
                            accessible: response.status !== 404
                        };
                    } catch (error) {
                        results[route] = {
                            error: error.message,
                            accessible: false
                        };
                    }
                }
                
                log('✅ Vérification des routes terminée', 'success');
                displayResult('result3', { routes: results }, true);
                
            } catch (error) {
                log(`❌ Erreur lors de la vérification des routes: ${error.message}`, 'error');
                displayResult('result3', { error: error.message }, false);
            }
        }

        // Initialisation
        log('🚀 Page de test du processus de téléversement chargée', 'success');
        log('Assurez-vous que le serveur Laravel est démarré avec: php artisan serve', 'info');
        log('Vérifiez les logs Laravel: tail -f storage/logs/laravel.log', 'info');
    </script>
</body>
</html>
