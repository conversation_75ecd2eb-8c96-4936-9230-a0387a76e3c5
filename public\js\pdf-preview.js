/**
 * Script pour la page de prévisualisation PDF et le positionnement de signature
 */

document.addEventListener("DOMContentLoaded", function() {
    const documentId = document.body.dataset.documentId;
    const pdfContainer = document.getElementById("pdf-container");
    const applySignatureBtn = document.getElementById("applySignatureBtn");
    const signaturePreview = document.getElementById("signaturePreview");
    const signaturePositionInfo = document.getElementById("signaturePositionInfo");
    const originalApplyBtnText = applySignatureBtn.innerHTML;

    let pdfDoc = null; // Objet PDF.js du document
    let currentPage = 1; // Page actuellement affichée
    let selectedPosition = null; // { xPct, yPct, pageNumber }
    let signatureImageBase64 = null; // Image de la signature de l'utilisateur

    // Charger la signature de l'utilisateur au démarrage
    async function loadUserSignature() {
        try {
            const response = await signSecureAPI.getCurrentSignature();
            if (response.data && response.data.signature_image) {
                signatureImageBase64 = response.data.signature_image;
                signaturePreview.src = signatureImageBase64;
                signaturePreview.style.display = "block";
                SignSecureUI.showToast("Votre signature a été chargée.", "info");
            } else {
                SignSecureUI.showToast("Aucune signature enregistrée. Veuillez en créer une.", "warning");
                // Rediriger vers la page de création de signature si aucune n'existe
                // setTimeout(() => { window.location.href = '/signatures/create'; }, 2000);
            }
        } catch (error) {
            SignSecureUI.showToast(error.message || "Erreur lors du chargement de la signature.", "error");
        }
    }

    // Charger le document PDF
    async function loadPdf() {
        if (!documentId) {
            SignSecureUI.showToast("ID du document manquant.", "error");
            return;
        }

        try {
            // Récupérer le document via l'API pour obtenir son chemin
            const docResponse = await signSecureAPI.getDocument(documentId);
            const documentPath = docResponse.data.file_path; // Assurez-vous que votre API retourne le chemin du fichier

            // Utiliser PDF.js pour charger le PDF
            const loadingTask = pdfjsLib.getDocument(documentPath);
            pdfDoc = await loadingTask.promise;

            renderPage(currentPage);
            updatePageInfo();

        } catch (error) {
            console.error("Erreur lors du chargement du PDF:", error);
            SignSecureUI.showToast(error.message || "Impossible de charger le document PDF.", "error");
        }
    }

    // Rendre une page spécifique du PDF
    async function renderPage(num) {
        if (!pdfDoc) return;

        currentPage = num;
        updatePageInfo();

        const page = await pdfDoc.getPage(num);
        const viewport = page.getViewport({ scale: 1.5 }); // Ajuster le scale si nécessaire

        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        canvas.classList.add("pdf-page-canvas");
        canvas.dataset.pageNumber = num;

        const renderContext = {
            canvasContext: context,
            viewport: viewport,
        };

        pdfContainer.innerHTML = ""; // Effacer le contenu précédent
        pdfContainer.appendChild(canvas);

        await page.render(renderContext).promise;

        // Ajouter l'écouteur de clic pour positionner la signature
        canvas.addEventListener("click", handlePageClick);

        // Si une position est déjà sélectionnée sur cette page, l'afficher
        if (selectedPosition && selectedPosition.pageNumber === num) {
            displaySignatureMarker(selectedPosition.x, selectedPosition.y);
        }
    }

    // Gérer le clic sur la page pour positionner la signature
    function handlePageClick(e) {
        if (!signatureImageBase64) {
            SignSecureUI.showToast("Veuillez d'abord enregistrer votre signature.", "warning");
            return;
        }

        const canvas = e.target;
        const rect = canvas.getBoundingClientRect();
        
        // Coordonnées relatives au canvas -> pourcentages (0..1)
        const xPx = e.clientX - rect.left;
        const yPx = e.clientY - rect.top;
        const xPct = xPx / rect.width;
        const yPct = yPx / rect.height;

        selectedPosition = {
            xPct: Math.min(Math.max(xPct, 0), 1),
            yPct: Math.min(Math.max(yPct, 0), 1),
            pageNumber: parseInt(canvas.dataset.pageNumber)
        };

        displaySignatureMarker(xPct, yPct);
        updateSignaturePositionInfo();
        applySignatureBtn.disabled = false;
    }

    // Afficher le marqueur de signature sur le canvas
    function displaySignatureMarker(xPct, yPct) {
        const canvas = pdfContainer.querySelector(".pdf-page-canvas");
        if (!canvas) return;

        let marker = document.getElementById("signature-marker");
        if (!marker) {
            marker = document.createElement("img");
            marker.id = "signature-marker";
            marker.src = signatureImageBase64; // Utiliser la signature de l'utilisateur
            marker.style.position = "absolute";
            marker.style.width = "100px"; // Taille de la signature sur le PDF
            marker.style.height = "50px";
            marker.style.border = "2px dashed #007bff";
            marker.style.cursor = "move";
            marker.style.zIndex = 1000;
            pdfContainer.style.position = "relative"; // Assurer que le conteneur est positionné
            pdfContainer.appendChild(marker);
        }

        // Positionner le marqueur par pourcentage et le centrer
        marker.style.left = `${xPct * canvas.width}px`;
        marker.style.top = `${yPct * canvas.height}px`;
        marker.style.transform = 'translate(-50%, -50%)';
    }

    // Mettre à jour les informations de position affichées
    function updateSignaturePositionInfo() {
        if (selectedPosition) {
            signaturePositionInfo.textContent = 
                `Position: Page ${selectedPosition.pageNumber}, X: ${(selectedPosition.xPct*100).toFixed(1)}%, Y: ${(selectedPosition.yPct*100).toFixed(1)}%`;
        } else {
            signaturePositionInfo.textContent = "Cliquez sur le document pour positionner votre signature.";
        }
    }

    // Navigation entre les pages
    document.getElementById("prevPage").addEventListener("click", () => {
        if (currentPage > 1) {
            renderPage(currentPage - 1);
        }
    });

    document.getElementById("nextPage").addEventListener("click", () => {
        if (currentPage < pdfDoc.numPages) {
            renderPage(currentPage + 1);
        }
    });

    function updatePageInfo() {
        document.getElementById("pageInfo").textContent = `Page ${currentPage} / ${pdfDoc ? pdfDoc.numPages : '...'}`;
    }

    // Appliquer la signature via l'API
    applySignatureBtn.addEventListener("click", async function() {
        if (!selectedPosition) {
            SignSecureUI.showToast("Veuillez sélectionner une position pour la signature.", "error");
            return;
        }

        SignSecureUI.showLoader(applySignatureBtn);

        try {
            const response = await signSecureAPI.applySignature(
                documentId,
                selectedPosition.xPct,
                selectedPosition.yPct,
                selectedPosition.pageNumber
            );

            SignSecureUI.showToast("Signature appliquée avec succès !", "success");
            
            // Rediriger vers la page de téléchargement ou de confirmation
            setTimeout(() => {
                window.location.href = `/documents/${documentId}/download-signed`; // Page à créer
            }, 1500);

        } catch (error) {
            SignSecureUI.hideLoader(applySignatureBtn, originalApplyBtnText);
            SignSecureUI.showToast(error.message || "Erreur lors de l'application de la signature.", "error");
        }
    });

    // Initialisation
    loadUserSignature();
    loadPdf();
    updateSignaturePositionInfo();
});
