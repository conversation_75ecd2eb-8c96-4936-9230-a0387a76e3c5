<?php

namespace App\Services;

use App\Models\Document;
use App\Models\OTPCode;
use App\Models\Signature;
use App\Models\VerificationLog;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SecurityService
{
    /**
     * Génère un hash SHA256 sécurisé pour un fichier
     */
    public function generateFileHash(string $filePath): string
    {
        if (!Storage::disk('private')->exists($filePath)) {
            throw new \Exception("Fichier introuvable: {$filePath}");
        }

        $fileContent = Storage::disk('private')->get($filePath);
        return hash('sha256', $fileContent);
    }

    /**
     * Vérifie l'intégrité d'un fichier
     */
    public function verifyFileIntegrity(Document $document): bool
    {
        try {
            // Vérifier le fichier original
            if ($document->hash_original) {
                $currentHash = $this->generateFileHash($document->file_path);
                if ($currentHash !== $document->hash_original) {
                    Log::warning('Intégrité du fichier original compromise', [
                        'document_id' => $document->id,
                        'expected_hash' => $document->hash_original,
                        'current_hash' => $currentHash
                    ]);
                    return false;
                }
            }

            // Vérifier le fichier signé si applicable
            if ($document->is_signed && $document->hash_signed) {
                $currentHash = $this->generateFileHash($document->signed_file_path);
                if ($currentHash !== $document->hash_signed) {
                    Log::warning('Intégrité du fichier signé compromise', [
                        'document_id' => $document->id,
                        'expected_hash' => $document->hash_signed,
                        'current_hash' => $currentHash
                    ]);
                    return false;
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Erreur lors de la vérification d\'intégrité', [
                'document_id' => $document->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Génère un code de suivi sécurisé et unique
     */
    public function generateTrackingCode(): string
    {
        do {
            // Générer un code de 16 caractères avec des caractères alphanumériques
            $code = strtoupper(Str::random(16));
            // Vérifier qu'il n'existe pas déjà
            $exists = Document::where('tracking_code', $code)->exists();
        } while ($exists);

        return $code;
    }

    /**
     * Vérifie la sécurité d'une signature
     */
    public function verifySignatureSecurity(Signature $signature): array
    {
        $securityChecks = [
            'integrity' => true,
            'timestamp_valid' => true,
            'position_valid' => true,
            'warnings' => []
        ];

        try {
            // Vérifier l'intégrité du document
            if (!$this->verifyFileIntegrity($signature->document)) {
                $securityChecks['integrity'] = false;
                $securityChecks['warnings'][] = 'Intégrité du document compromise';
            }

            // Vérifier la validité temporelle
            $maxAge = config('signsecure.signature_max_age_days', 365);
            $signatureAge = now()->diffInDays($signature->applied_at);
            if ($signatureAge > $maxAge) {
                $securityChecks['timestamp_valid'] = false;
                $securityChecks['warnings'][] = "Signature expirée (âge: {$signatureAge} jours)";
            }

            // Vérifier la position de la signature
            if ($signature->position_x < 0 || $signature->position_y < 0) {
                $securityChecks['position_valid'] = false;
                $securityChecks['warnings'][] = 'Position de signature invalide';
            }

            // Vérifier le hash de la signature
            if ($signature->signature_hash) {
                $currentHash = hash('sha256', $signature->signature_image);
                if ($currentHash !== $signature->signature_hash) {
                    $securityChecks['integrity'] = false;
                    $securityChecks['warnings'][] = 'Intégrité de la signature compromise';
                }
            }

        } catch (\Exception $e) {
            Log::error('Erreur lors de la vérification de sécurité de la signature', [
                'signature_id' => $signature->id,
                'error' => $e->getMessage()
            ]);
            $securityChecks['integrity'] = false;
            $securityChecks['warnings'][] = 'Erreur lors de la vérification de sécurité';
        }

        return $securityChecks;
    }

    /**
     * Vérifie la sécurité d'un code OTP
     */
    public function verifyOTPSecurity(OTPCode $otpCode, string $ipAddress = null): array
    {
        $securityChecks = [
            'valid' => true,
            'not_expired' => true,
            'not_used' => true,
            'not_locked' => true,
            'warnings' => []
        ];

        try {
            // Vérifier l'expiration
            if ($otpCode->expires_at < now()) {
                $securityChecks['not_expired'] = false;
                $securityChecks['valid'] = false;
                $securityChecks['warnings'][] = 'Code OTP expiré';
            }

            // Vérifier s'il a déjà été utilisé
            if ($otpCode->used_at) {
                $securityChecks['not_used'] = false;
                $securityChecks['valid'] = false;
                $securityChecks['warnings'][] = 'Code OTP déjà utilisé';
            }

            // Vérifier le verrouillage
            if ($otpCode->locked_until && $otpCode->locked_until > now()) {
                $securityChecks['not_locked'] = false;
                $securityChecks['valid'] = false;
                $securityChecks['warnings'][] = 'Code OTP temporairement verrouillé';
            }

            // Vérifier le nombre de tentatives
            $maxAttempts = config('signsecure.max_otp_attempts', 3);
            if ($otpCode->attempts >= $maxAttempts) {
                $securityChecks['valid'] = false;
                $securityChecks['warnings'][] = 'Nombre maximum de tentatives atteint';
            }

            // Log de la tentative
            if ($ipAddress) {
                $otpCode->increment('attempts');
                $otpCode->update(['ip_address' => $ipAddress]);
            }

        } catch (\Exception $e) {
            Log::error('Erreur lors de la vérification de sécurité OTP', [
                'otp_id' => $otpCode->id,
                'error' => $e->getMessage()
            ]);
            $securityChecks['valid'] = false;
            $securityChecks['warnings'][] = 'Erreur lors de la vérification de sécurité';
        }

        return $securityChecks;
    }

    /**
     * Enregistre un log de vérification
     */
    public function logVerification(string $trackingCode, string $result, array $details = [], string $ipAddress = null): void
    {
        try {
            VerificationLog::create([
                'tracking_code' => $trackingCode,
                'ip_address' => $ipAddress,
                'verification_result' => $result,
                'verification_details' => json_encode($details),
                'verified_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'enregistrement du log de vérification', [
                'tracking_code' => $trackingCode,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Génère un rapport de sécurité pour un document
     */
    public function generateSecurityReport(Document $document): array
    {
        $report = [
            'document_id' => $document->id,
            'tracking_code' => $document->tracking_code,
            'security_level' => 'medium',
            'checks' => [],
            'warnings' => [],
            'recommendations' => []
        ];

        try {
            // Vérifier l'intégrité du fichier
            $integrityCheck = $this->verifyFileIntegrity($document);
            $report['checks']['file_integrity'] = $integrityCheck;

            // Vérifier les signatures
            $signatures = $document->signatures;
            $report['checks']['signatures_count'] = $signatures->count();
            $report['checks']['signatures_valid'] = 0;

            foreach ($signatures as $signature) {
                $signatureSecurity = $this->verifySignatureSecurity($signature);
                if ($signatureSecurity['integrity'] && $signatureSecurity['timestamp_valid']) {
                    $report['checks']['signatures_valid']++;
                }
                
                if (!empty($signatureSecurity['warnings'])) {
                    $report['warnings'] = array_merge($report['warnings'], $signatureSecurity['warnings']);
                }
            }

            // Déterminer le niveau de sécurité
            if ($report['checks']['file_integrity'] && $report['checks']['signatures_valid'] > 0) {
                $report['security_level'] = 'high';
            } elseif ($report['checks']['file_integrity']) {
                $report['security_level'] = 'medium';
            } else {
                $report['security_level'] = 'low';
            }

            // Générer des recommandations
            if (!$report['checks']['file_integrity']) {
                $report['recommendations'][] = 'Vérifier l\'intégrité du fichier original';
            }
            if ($report['checks']['signatures_valid'] === 0) {
                $report['recommendations'][] = 'Aucune signature valide trouvée';
            }
            if (!empty($report['warnings'])) {
                $report['recommendations'][] = 'Résoudre les avertissements de sécurité identifiés';
            }

        } catch (\Exception $e) {
            Log::error('Erreur lors de la génération du rapport de sécurité', [
                'document_id' => $document->id,
                'error' => $e->getMessage()
            ]);
            $report['security_level'] = 'unknown';
            $report['warnings'][] = 'Erreur lors de la génération du rapport de sécurité';
        }

        return $report;
    }
}
