<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    // Configuration pour l'envoi de SMS via Twilio
    'twilio' => [
        'sid' => env('TWILIO_SID'),
        'token' => env('TWILIO_TOKEN'),
        'from' => env('TWILIO_FROM'), // Numéro Twilio
    ],

    // Configuration alternative pour d'autres services SMS
    'sms' => [
        'provider' => env('SMS_PROVIDER', 'twilio'), // twilio, nexmo, etc.
        'api_key' => env('SMS_API_KEY'),
        'api_secret' => env('SMS_API_SECRET'),
        'from' => env('SMS_FROM_NUMBER'),
    ],

    // FasterMessage SMS API
    'fastermessage' => [
        'endpoint' => env('FASTMESSAGE_ENDPOINT', 'https://fastermessage.com/api/v1/sms/send'),
        'api_key' => env('FASTMESSAGE_API_KEY'),
        'from' => env('FASTMESSAGE_FROM', 'SignSecure'),
        'default_country_code' => env('FASTMESSAGE_DEFAULT_COUNTRY_CODE', '+229'),
    ],

    // Configuration pour la signature électronique
    'signature' => [
        'max_file_size' => env('SIGNATURE_MAX_FILE_SIZE', 1048576), // 1MB par défaut
        'allowed_formats' => ['png', 'jpg', 'jpeg'],
        'storage_disk' => env('SIGNATURE_STORAGE_DISK', 'private'),
    ],

    // Configuration pour les documents PDF
    'pdf' => [
        'max_file_size' => env('PDF_MAX_FILE_SIZE', 10485760), // 10MB par défaut
        'max_pages' => env('PDF_MAX_PAGES', 50),
        'storage_disk' => env('PDF_STORAGE_DISK', 'private'),
        'signed_storage_disk' => env('PDF_SIGNED_STORAGE_DISK', 'signed_documents'),
    ],

    // Configuration pour les codes OTP
    'otp' => [
        'length' => env('OTP_LENGTH', 6),
        'expiry_minutes' => env('OTP_EXPIRY_MINUTES', 10),
        'max_attempts' => env('OTP_MAX_ATTEMPTS', 3),
    ],

];