<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class DocumentUploadRe<PERSON> extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'document' => ['required', 'file', 'mimes:pdf', 'max:10240'], // Max 10MB
            'otp_method' => ['nullable', 'string', 'in:email,sms'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'document.required' => 'Le fichier document est obligatoire.',
            'document.file' => 'Le document doit être un fichier.',
            'document.mimes' => 'Le document doit être un fichier PDF.',
            'document.max' => 'Le document ne doit pas dépasser 10 Mo.',
            'otp_method.in' => 'La méthode OTP doit être email ou sms.',
        ];
    }
}