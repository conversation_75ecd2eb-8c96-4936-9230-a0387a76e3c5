@extends('layouts.app')

@section('content')
<div class="max-w-2xl mx-auto p-6 bg-white shadow-md rounded-lg mt-10">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Mon Profil</h2>

    @if (session('status'))
        <div class="mb-4 text-green-600 font-semibold">
            {{ session('status') }}
        </div>
    @endif

    <form id="profileForm" class="space-y-6">

        <!-- Nom -->
        <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Nom</label>
            <input id="name" type="text" name="name" value="{{ old('name', auth()->user()->name) }}"
                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring focus:ring-indigo-200 focus:outline-none">
            @error('name')
                <p class="text-sm text-red-500 mt-1">{{ $message }}</p>
            @enderror
        </div>

        <!-- Email -->
        <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
            <input id="email" type="email" name="email" value="{{ old('email', auth()->user()->email) }}"
                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring focus:ring-indigo-200 focus:outline-none">
            @error('email')
                <p class="text-sm text-red-500 mt-1">{{ $message }}</p>
            @enderror
        </div>

        <!-- Téléphone (si disponible) -->
        <div>
            <label for="phone" class="block text-sm font-medium text-gray-700">Téléphone</label>
            <input id="phone" type="text" name="phone" value="{{ old('phone', auth()->user()->phone ?? '') }}"
                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring focus:ring-indigo-200 focus:outline-none">
            @error('phone')
                <p class="text-sm text-red-500 mt-1">{{ $message }}</p>
            @enderror
        </div>

        <!-- Bouton de soumission -->
        <div>
            <button id="saveProfileBtn" type="submit"
                    class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition">
                Mettre à jour
            </button>
        </div>
    </form>
</div>
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', async function() {
    const form = document.getElementById('profileForm');
    const btn = document.getElementById('saveProfileBtn');
    const originalText = btn.innerHTML;

    // Charger profil via API (source de vérité: API)
    try {
        const resp = await signSecureAPI.getProfile();
        const user = resp?.data?.user;
        if (user) {
            document.getElementById('name').value = user.name || '';
            document.getElementById('email').value = user.email || '';
            const phoneEl = document.getElementById('phone');
            if (phoneEl) phoneEl.value = user.phone || '';
        }
    } catch (e) {
        SignSecureUI.showToast(e.message || 'Erreur lors du chargement du profil', 'error');
    }

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        SignSecureUI.showLoader(btn);
        try {
            const payload = {
                name: document.getElementById('name').value.trim(),
                email: document.getElementById('email').value.trim(),
                phone: (document.getElementById('phone')?.value || '').trim(),
            };
            const resp = await signSecureAPI.updateProfile(payload);
            SignSecureUI.showToast(resp?.message || 'Profil mis à jour', 'success');
        } catch (err) {
            if (err?.data?.errors) {
                SignSecureUI.displayValidationErrors(err.data.errors);
            }
            SignSecureUI.showToast(err.message || 'Erreur lors de la mise à jour', 'error');
        } finally {
            SignSecureUI.hideLoader(btn, originalText);
        }
    });
});
</script>
@endpush
@endsection
