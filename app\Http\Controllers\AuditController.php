<?php

namespace App\Http\Controllers;

use App\Models\AuditLog;
use Illuminate\Http\Request;

class AuditController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth:sanctum', 'is.admin']);
    }

    public function index(Request $request)
    {
        $query = AuditLog::with('user')->orderByDesc('created_at');
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        if ($request->filled('from')) {
            $query->where('created_at', '>=', $request->from);
        }
        if ($request->filled('to')) {
            $query->where('created_at', '<=', $request->to);
        }
        $logs = $query->limit(100)->get();
        return view('admin.audit', compact('logs'));
    }
}


