<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\DocumentSigner;
use App\Models\User;
use App\Services\SignatureService;
use App\Services\PDFService;
use App\Services\CoordinateConversionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SignatureFlowController extends Controller
{
    public function __construct(
        private SignatureService $signatureService,
        private PDFService $pdfService
    ) {
        $this->middleware('auth:sanctum');
    }

    /**
     * Affiche la page de capture de signature (première fois)
     */
    public function showSignatureCapture(Document $document)
    {
        $this->authorize('view', $document);
        
        $user = Auth::user();
        
        // Vérifier si l'utilisateur a déjà une signature
        if ($user->hasSignature()) {
            return redirect()->route('documents.preview', $document);
        }

        // Afficher directement la page de capture de signature
        return view('signatures.capture', compact('document'));
    }

    /**
     * Affiche la page d'invitation de signataires
     */
    public function showInviteForm(Document $document)
    {
        $this->authorize('view', $document);
        
        return view('signatures.invite', compact('document'));
    }

    /**
     * Affiche la page de prévisualisation avec positionnement de signature
     */
    public function showSignaturePreview(Document $document)
    {
        $this->authorize('view', $document);
        
        $user = Auth::user();
        
        // Si pas de signature, rediriger vers la capture
        if (!$user->hasSignature()) {
            return redirect()->route('signatures.capture', $document);
        }

        return view('signatures.preview', compact('document'));
    }

    /**
     * Capture la signature
     */
    public function captureSignature(Request $request, Document $document): JsonResponse
    {
        $this->authorize('view', $document);
        
        $request->validate([
            'signature_data' => 'required|string',
        ]);

        try {
            $user = Auth::user();
            $signatureData = $request->input('signature_data');
            
            // Sauvegarder la signature
            $this->signatureService->saveUserSignature($user, $signatureData);
            
            return response()->json([
                'success' => true,
                'message' => 'Signature enregistrée avec succès',
                'redirect_url' => route('signatures.preview', $document),
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erreur capture signature', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'enregistrement de la signature',
            ], 500);
        }
    }

    /**
     * Applique la signature sur le document
     */
    public function applySignature(Request $request, Document $document): JsonResponse
    {
        $this->authorize('view', $document);
        
        $request->validate([
            'position_x' => 'required|numeric',
            'position_y' => 'required|numeric',
            'page_number' => 'required|integer|min:1',
            'canvas_width' => 'nullable|integer|min:100',
            'canvas_height' => 'nullable|integer|min:100',
        ]);

        try {
            $user = Auth::user();

            // Convertir les coordonnées pixels en millimètres
            $canvasWidth = (int) ($request->input('canvas_width') ?? 800);
            $canvasHeight = (int) ($request->input('canvas_height') ?? 600);
            $pdfWidth = 210.0; // A4 largeur en mm
            $pdfHeight = 297.0; // A4 hauteur en mm

            $convertedCoords = CoordinateConversionService::pixelsToMillimeters(
                (float) $request->position_x,
                (float) $request->position_y,
                $canvasWidth,
                $canvasHeight,
                $pdfWidth,
                $pdfHeight
            );

            Log::info('Conversion coordonnées signature', [
                'original_pixels' => ['x' => $request->position_x, 'y' => $request->position_y],
                'converted_mm' => $convertedCoords,
                'canvas_size' => ['width' => $canvasWidth, 'height' => $canvasHeight],
                'pdf_size' => ['width' => $pdfWidth, 'height' => $pdfHeight]
            ]);

            $this->signatureService->applySignatureToDocument(
                $document,
                $user,
                $convertedCoords['x'],
                $convertedCoords['y'],
                (int) $request->page_number
            );

            // Document signé directement
            $document->update([
                'status' => 'signed',
                'signed_at' => now(),
            ]);
            
            $trackingCode = $this->signatureService->generateTrackingCode($document);
            $document->update(['tracking_code' => $trackingCode]);

            // Ajouter le code de suivi visuellement au PDF signé
            try {
                $document->refresh();
                if (!empty($document->signed_file_path)) {
                    $trackedPath = $this->pdfService->addTrackingCode($document->signed_file_path, $document->tracking_code);
                    $document->update(['signed_file_path' => $trackedPath]);
                }
            } catch (\Throwable $e) {
                \Log::warning('Ajout du code de suivi échoué', [
                    'document_id' => $document->id,
                    'error' => $e->getMessage(),
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Signature appliquée avec succès',
                'document_fully_signed' => true,
                'redirect_url' => route('documents.show', $document),
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erreur application signature', [
                'document_id' => $document->id,
                'user_id' => Auth::id(),
                'position_x' => $request->input('position_x'),
                'position_y' => $request->input('position_y'),
                'page_number' => $request->input('page_number'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'application de la signature',
            ], 500);
        }
    }

    /**
     * Invite des signataires supplémentaires
     */
    public function inviteSigners(Request $request, Document $document): JsonResponse
    {
        $this->authorize('view', $document);
        
        $request->validate([
            'signers' => 'required|array|min:1',
            'signers.*.email' => 'required|email',
            'signers.*.name' => 'required|string|max:255',
            'signers.*.signature_order' => 'required|integer|min:1',
        ]);

        try {
            $signers = collect($request->signers);
            
            foreach ($signers as $signerData) {
                // Créer ou récupérer l'utilisateur
                $user = User::firstOrCreate(
                    ['email' => $signerData['email']],
                    [
                        'name' => $signerData['name'],
                        'password' => bcrypt(\Illuminate\Support\Str::random(16)), // Mot de passe temporaire
                    ]
                );

                // Créer l'entrée de signataire
                DocumentSigner::create([
                    'document_id' => $document->id,
                    'user_id' => $user->id,
                    'email' => $signerData['email'],
                    'name' => $signerData['name'],
                    'signature_order' => $signerData['signature_order'],
                    'status' => 'pending',
                    'expires_at' => now()->addDays(7), // Expire dans 7 jours
                ]);

                // Envoyer l'email d'invitation
                // TODO: Implémenter l'envoi d'email
            }

            return response()->json([
                'success' => true,
                'message' => 'Invitations envoyées avec succès',
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erreur invitation signataires', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'envoi des invitations',
            ], 500);
        }
    }
}
