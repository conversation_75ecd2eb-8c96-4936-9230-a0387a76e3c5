@extends('layouts.app')

@section('content')
<div class="container mx-auto max-w-4xl">
    <!-- Header avec icône -->
    <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full mb-4">
            <i class="fas fa-check-circle text-white text-2xl"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Retrace créé avec succès !</h1>
        <p class="text-gray-600 text-lg">Votre document physique est maintenant traçable numériquement</p>
    </div>

    <!-- Carte principale -->
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-6">
        <!-- Code de suivi en évidence -->
        <div class="text-center mb-8">
            <div class="inline-block bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border-2 border-blue-200">
                <p class="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-2">Code de suivi unique</p>
                <div class="bg-white rounded-xl px-6 py-4 border border-gray-200">
                    <code class="text-3xl font-mono font-bold text-blue-600 tracking-wider">{{ $retrace->tracking_code }}</code>
                </div>
                <p class="text-xs text-gray-500 mt-2">Utilisez ce code pour vérifier l'authenticité</p>
            </div>
        </div>

        <!-- Informations du retrace -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div class="flex items-center p-4 bg-blue-50 rounded-xl">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-calendar-alt text-blue-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Date</p>
                        <p class="text-lg font-semibold text-gray-800">{{ $retrace->date->format('d/m/Y') }}</p>
                    </div>
                </div>

                <div class="flex items-center p-4 bg-green-50 rounded-xl">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-hashtag text-green-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Référence</p>
                        <p class="text-lg font-semibold text-gray-800">{{ $retrace->reference }}</p>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <div class="flex items-center p-4 bg-purple-50 rounded-xl">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-file-alt text-purple-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Objet</p>
                        <p class="text-lg font-semibold text-gray-800">{{ $retrace->subject }}</p>
                    </div>
                </div>

                @if($retrace->notes)
                <div class="flex items-start p-4 bg-orange-50 rounded-xl">
                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-4 mt-1">
                        <i class="fas fa-sticky-note text-orange-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Notes</p>
                        <p class="text-gray-800">{{ $retrace->notes }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Actions -->
        @if($retrace->pdf_path)
        <div class="mt-8 text-center">
            <div class="inline-flex items-center space-x-4">
                <a href="{{ route('retrace.download', $retrace) }}" 
                   class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <i class="fas fa-download mr-2"></i>
                    Télécharger le PDF de retrace
                </a>
                
                <a href="{{ route('retrace.create') }}" 
                   class="inline-flex items-center px-6 py-3 bg-gray-100 text-gray-700 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Créer un autre retrace
                </a>
            </div>
        </div>
        @endif
    </div>

    <!-- Informations utiles -->
    <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-200">
        <div class="flex items-start">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                <i class="fas fa-info-circle text-blue-600"></i>
            </div>
            <div class="text-blue-800">
                <h3 class="font-semibold text-lg mb-2">Comment utiliser ce retrace ?</h3>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-center"><i class="fas fa-check-circle text-blue-500 mr-2"></i>Imprimez le PDF généré et joignez-le à votre document physique</li>
                    <li class="flex items-center"><i class="fas fa-check-circle text-blue-500 mr-2"></i>Partagez le code de suivi avec les parties concernées</li>
                    <li class="flex items-center"><i class="fas fa-check-circle text-blue-500 mr-2"></i>Vérifiez l'authenticité en utilisant le code sur la page de vérification</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection


