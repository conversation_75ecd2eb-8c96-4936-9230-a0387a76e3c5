<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Services\TrackingCodeService;

class PhysicalRetrace extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'tracking_code',
        'date',
        'reference',
        'subject',
        'notes',
        'pdf_path',
    ];

    protected $casts = [
        'date' => 'date',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($retrace) {
            $retrace->tracking_code = TrackingCodeService::generateRetraceCode();
        });
    }
}



