<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\SignatureCaptureRequest;
use App\Http\Requests\API\SignatureApplyRequest;
use App\Http\Resources\DocumentResource;
use App\Http\Resources\UserResource;
use App\Models\Document;
use App\Models\Signature;
use App\Services\PDFService;
use App\Services\SignatureService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SignatureController extends Controller
{
    public function __construct(
        private SignatureService $signatureService,
        private PDFService $pdfService
    ) {
        $this->middleware('auth:sanctum');
    }

    /**
     * Enregistre la signature manuscrite d'un utilisateur
     *
     * @param SignatureCaptureRequest $request
     * @return JsonResponse
     */
    public function capture(SignatureCaptureRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = $request->user();

        try {
            // Décoder l'image base64
            $signatureData = $validated['signature_data'];
            
            // Vérifier le format data:image/png;base64,
            if (!preg_match('/^data:image\/(png|jpeg|jpg);base64,/', $signatureData)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Format de signature invalide',
                ], 422);
            }

            // Extraire les données base64
            $base64Data = preg_replace('#^data:image/\w+;base64,#i', '', $signatureData);
            $base64Data = str_replace(' ', '+', $base64Data); // corrige les espaces éventuels
            $imageData = base64_decode($base64Data, true);    // true = décodage strict

            if ($imageData === false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Données de signature invalides',
                ], 422);
            }

            // Vérifier que c'est bien un PNG
            if (substr($imageData, 1, 3) !== 'PNG') {
                return response()->json([
                    'success' => false,
                    'message' => 'Le fichier décodé n\'est pas un PNG valide',
                ], 422);
            }

            // Générer un nom de fichier unique
            $filename = 'signature_' . $user->id . '_' . Str::uuid() . '.png';
            $path = 'signatures/' . $filename;

            // Sauvegarder l'image
            Storage::disk('private')->put($path, $imageData);

            // Mettre à jour l'utilisateur avec la nouvelle signature
            $user->update([
                'signature_image' => $signatureData, // Stocker le data URL pour affichage
                'signature_path' => $path, // Stocker le chemin pour traitement
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Signature enregistrée avec succès',
                'data' => new UserResource($user->fresh()),
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'enregistrement de la signature',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Applique la signature de l'utilisateur sur un document PDF
     *
     * @param SignatureApplyRequest $request
     * @return JsonResponse
     */
    public function apply(SignatureApplyRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $document = Document::findOrFail($validated['document_id']);
        $user = $request->user();

        // Vérifier que l'utilisateur est propriétaire du document
        $this->authorize('update', $document);

        // Vérifier que l'utilisateur a une signature
        if (!$user->signature_path) {
            return response()->json([
                'message' => 'Aucune signature enregistrée. Veuillez d\'abord créer votre signature.',
            ], 422);
        }

        // Vérifier que le document n'est pas déjà signé
        if ($document->is_signed) {
            return response()->json([
                'message' => 'Ce document est déjà signé',
            ], 422);
        }

        try {
            // Mettre à jour le statut du document
            $document->update(['status' => 'processing']);

            // Vérifier l'existence des fichiers
            if (!Storage::disk('private')->exists($user->signature_path)) {
                return response()->json([
                    'message' => 'Fichier de signature introuvable',
                    'path' => $user->signature_path,
                ], 422);
            }

            if (!Storage::disk('private')->exists($document->file_path)) {
                return response()->json([
                    'message' => 'Document PDF introuvable',
                    'path' => $document->file_path,
                ], 422);
            }

            // Appliquer la signature sur le PDF
            try {
                $signedFilePath = $this->pdfService->applySignatureUsingPercent(
                    $document->file_path,
                    $user->signature_path,
                    $validated['x_pct'],
                    $validated['y_pct'],
                    $validated['page_number']
                );
            } catch (\Exception $e) {
                return response()->json([
                    'message' => 'Erreur lors de l\'application de la signature PDF',
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ], 500);
            }

            // Créer l'enregistrement de signature
            $signature = Signature::create([
                'user_id' => $user->id,
                'document_id' => $document->id,
                'position_x' => $validated['x_pct'],
                'position_y' => $validated['y_pct'],
                'page_number' => $validated['page_number'],
                'signature_image' => $user->signature_image,
                'applied_at' => now(),
            ]);

            // Mettre à jour le document
            $document->update([
                'signed_file_path' => $signedFilePath,
                'status' => 'signed',
                'signed_at' => now(),
            ]);

            return response()->json([
                'message' => 'Signature appliquée avec succès',
                'data' => [
                    'document' => new DocumentResource($document->fresh()),
                    'signature' => [
                        'id' => $signature->id,
                        'position_x' => $signature->position_x,
                        'position_y' => $signature->position_y,
                        'page_number' => $signature->page_number,
                        'applied_at' => $signature->applied_at->toISOString(),
                    ],
                ],
            ], 200);

        } catch (\Exception $e) {
            $document->update(['status' => 'uploaded']);

            return response()->json([
                'message' => 'Erreur lors de l\'application de la signature',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Récupère la signature actuelle de l'utilisateur
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function current(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user->signature_image) {
            return response()->json([
                'message' => 'Aucune signature enregistrée',
                'data' => null,
            ], 200);
        }

        return response()->json([
            'message' => 'Signature récupérée avec succès',
            'data' => [
                'signature_image' => $user->signature_image,
                'created_at' => $user->updated_at->toISOString(),
            ],
        ], 200);
    }

    /**
     * Supprime la signature de l'utilisateur
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user->signature_path) {
            return response()->json([
                'message' => 'Aucune signature à supprimer',
            ], 200);
        }

        try {
            // Supprimer le fichier de signature
            if (Storage::disk('private')->exists($user->signature_path)) {
                Storage::disk('private')->delete($user->signature_path);
            }

            // Mettre à jour l'utilisateur
            $user->update([
                'signature_image' => null,
                'signature_path' => null,
            ]);

            return response()->json([
                'message' => 'Signature supprimée avec succès',
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erreur lors de la suppression de la signature',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Récupère l'historique des signatures de l'utilisateur
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function history(Request $request): JsonResponse
    {
        $signatures = $request->user()->signatures()
            ->with('document:id,original_filename,tracking_code')
            ->latest()
            ->paginate(15);

        $data = $signatures->map(function ($signature) {
            return [
                'id' => $signature->id,
                'document' => [
                    'id' => $signature->document->id,
                    'filename' => $signature->document->original_filename,
                    'tracking_code' => $signature->document->tracking_code,
                ],
                'position' => [
                    'x' => $signature->position_x,
                    'y' => $signature->position_y,
                    'page' => $signature->page_number,
                ],
                'applied_at' => $signature->applied_at->toISOString(),
            ];
        });

        return response()->json([
            'message' => 'Historique des signatures récupéré avec succès',
            'data' => $data,
            'meta' => [
                'current_page' => $signatures->currentPage(),
                'last_page' => $signatures->lastPage(),
                'per_page' => $signatures->perPage(),
                'total' => $signatures->total(),
            ],
        ], 200);
    }

    /**
     * Prévisualise la signature sur un document (sans l'appliquer)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function preview(Request $request): JsonResponse
    {
        $request->validate([
            'document_id' => 'required|exists:documents,id',
            'position_x' => 'required|numeric|min:0',
            'position_y' => 'required|numeric|min:0',
            'page_number' => 'required|integer|min:1',
        ]);

        $document = Document::findOrFail($request->document_id);
        $user = $request->user();

        $this->authorize('view', $document);

        if (!$user->signature_image) {
            return response()->json([
                'message' => 'Aucune signature enregistrée',
            ], 422);
        }

        return response()->json([
            'message' => 'Prévisualisation générée avec succès',
            'data' => [
                'document_id' => $document->id,
                'signature_preview' => $user->signature_image,
                'position' => [
                    'x' => $request->position_x,
                    'y' => $request->position_y,
                    'page' => $request->page_number,
                ],
                'estimated_size' => [
                    'width' => 150, // pixels
                    'height' => 75,  // pixels
                ],
            ],
        ], 200);
    }
}

