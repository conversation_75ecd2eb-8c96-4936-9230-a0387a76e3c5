<?php

/**
 * Script de test pour l'API Signature
 * À exécuter dans le navigateur ou via PHP CLI
 */

// Configuration
$baseUrl = 'http://localhost/signsecure/public/api/v1';
$token = 'YOUR_SANCTUM_TOKEN_HERE'; // Remplacez par un vrai token

// Test 1: Capturer une signature (simulation avec une image base64 simple)
function testCaptureSignature($baseUrl, $token) {
    $url = $baseUrl . '/signatures/capture';
    
    // Créer une image PNG simple en base64 (1x1 pixel transparent)
    $imageData = base64_encode(file_get_contents('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='));
    $signatureData = 'data:image/png;base64,' . $imageData;
    
    $data = [
        'signature_data' => $signatureData
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "=== Test 1: Capturer une signature ===\n";
    echo "URL: $url\n";
    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    return json_decode($response, true);
}

// Test 2: Appliquer une signature (avec tous les champs requis)
function testApplySignature($baseUrl, $token) {
    $url = $baseUrl . '/signatures/apply';
    
    $data = [
        'document_id' => 1, // Remplacez par un vrai ID de document
        'position_x' => 100.0,
        'position_y' => 200.0,
        'page_number' => 1
    ];

    echo "=== Test 2: Appliquer une signature ===\n";
    echo "URL: $url\n";
    echo "Données envoyées: " . json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    return json_decode($response, true);
}

// Test 3: Appliquer une signature avec des données manquantes (doit échouer)
function testApplySignatureMissingFields($baseUrl, $token) {
    $url = $baseUrl . '/signatures/apply';
    
    // Test avec position_x manquant
    $data1 = [
        'document_id' => 1,
        'position_y' => 200.0,
        'page_number' => 1
    ];

    echo "=== Test 3a: Appliquer une signature (position_x manquant) ===\n";
    echo "URL: $url\n";
    echo "Données envoyées: " . json_encode($data1, JSON_PRETTY_PRINT) . "\n\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data1));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    // Test avec position_y manquant
    $data2 = [
        'document_id' => 1,
        'position_x' => 100.0,
        'page_number' => 1
    ];

    echo "=== Test 3b: Appliquer une signature (position_y manquant) ===\n";
    echo "Données envoyées: " . json_encode($data2, JSON_PRETTY_PRINT) . "\n\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data2));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    // Test avec page_number manquant
    $data3 = [
        'document_id' => 1,
        'position_x' => 100.0,
        'position_y' => 200.0
    ];

    echo "=== Test 3c: Appliquer une signature (page_number manquant) ===\n";
    echo "Données envoyées: " . json_encode($data3, JSON_PRETTY_PRINT) . "\n\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data3));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";
}

// Test 4: Prévisualiser une signature
function testPreviewSignature($baseUrl, $token) {
    $url = $baseUrl . '/signatures/preview';
    
    $data = [
        'document_id' => 1, // Remplacez par un vrai ID de document
        'position_x' => 100.0,
        'position_y' => 200.0,
        'page_number' => 1
    ];

    echo "=== Test 4: Prévisualiser une signature ===\n";
    echo "URL: $url\n";
    echo "Données envoyées: " . json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    return json_decode($response, true);
}

// Test 5: Récupérer la signature actuelle
function testGetCurrentSignature($baseUrl, $token) {
    $url = $baseUrl . '/signatures/current';

    echo "=== Test 5: Récupérer la signature actuelle ===\n";
    echo "URL: $url\n\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPGET, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    return json_decode($response, true);
}

// Test 6: Récupérer l'historique des signatures
function testGetSignatureHistory($baseUrl, $token) {
    $url = $baseUrl . '/signatures/history';

    echo "=== Test 6: Récupérer l'historique des signatures ===\n";
    echo "URL: $url\n\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPGET, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "Réponse: $response\n\n";

    return json_decode($response, true);
}

// Exécution des tests
echo "🧪 Tests de l'API Signature\n";
echo "============================\n\n";

if ($token === 'YOUR_SANCTUM_TOKEN_HERE') {
    echo "⚠️  ATTENTION: Remplacez 'YOUR_SANCTUM_TOKEN_HERE' par un vrai token Sanctum\n";
    echo "Pour obtenir un token, connectez-vous via l'API d'authentification\n\n";
} else {
    // Tests avec authentification
    testCaptureSignature($baseUrl, $token);
    testApplySignature($baseUrl, $token);
    testApplySignatureMissingFields($baseUrl, $token);
    testPreviewSignature($baseUrl, $token);
    testGetCurrentSignature($baseUrl, $token);
    testGetSignatureHistory($baseUrl, $token);
}

echo "✅ Tests terminés !\n";
echo "\n📝 Résumé des champs requis pour appliquer une signature:\n";
echo "- document_id: ID du document (obligatoire)\n";
echo "- position_x: Position horizontale en pixels (obligatoire, numérique >= 0)\n";
echo "- position_y: Position verticale en pixels (obligatoire, numérique >= 0)\n";
echo "- page_number: Numéro de page (obligatoire, entier >= 1)\n";
echo "\n🔍 Si vous obtenez des erreurs de validation:\n";
echo "1. Vérifiez que tous les champs sont présents dans votre requête\n";
echo "2. Vérifiez que les types de données sont corrects\n";
echo "3. Vérifiez que les valeurs respectent les contraintes (min, max)\n";
echo "4. Utilisez la page de test HTML pour déboguer interactivement\n";
