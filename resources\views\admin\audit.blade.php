@extends('layouts.app')

@section('title', 'Admin - Journaux d\'audit')

@section('content')
<div class="container py-4">
    <h3 class="mb-4">Journaux d'audit</h3>
    <div class="card mb-3">
        <div class="card-body">
            <form class="row g-2" method="GET">
                <div class="col-md-3"><input class="form-control" type="number" name="user_id" placeholder="User ID" value="{{ request('user_id') }}"></div>
                <div class="col-md-3"><input class="form-control" type="date" name="from" value="{{ request('from') }}"></div>
                <div class="col-md-3"><input class="form-control" type="date" name="to" value="{{ request('to') }}"></div>
                <div class="col-md-3"><button class="btn btn-primary w-100" type="submit"><i class="fas fa-filter me-2"></i>Filtrer</button></div>
            </form>
        </div>
    </div>
    <div class="card">
        <div class="table-responsive">
            <table class="table table-striped mb-0">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Utilisateur</th>
                        <th>Action</th>
                        <th>Description</th>
                        <th>IP</th>
                        <th>Agent</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($logs as $log)
                    <tr>
                        <td>{{ $log->created_at->format('d/m/Y H:i') }}</td>
                        <td>{{ $log->user?->name ?? '-' }}</td>
                        <td>{{ $log->action }}</td>
                        <td>{{ $log->description }}</td>
                        <td>{{ $log->ip_address }}</td>
                        <td class="text-truncate" style="max-width: 240px;" title="{{ $log->user_agent }}">{{ $log->user_agent }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection


