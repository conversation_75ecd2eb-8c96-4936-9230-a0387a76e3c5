<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("signatures", function (Blueprint $table) {
            $table->id();
            $table->foreignId("user_id")->constrained()->onDelete("cascade");
            $table->foreignId("document_id")->constrained()->onDelete("cascade");
            $table->decimal("position_x", 8, 2);
            $table->decimal("position_y", 8, 2);
            $table->integer("page_number");
            $table->longText("signature_image"); // Base64 de l'image de signature
            $table->timestamp("applied_at");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("signatures");
    }
};
