/**
 * Script pour la page du tableau de bord (dashboard)
 */

document.addEventListener("DOMContentLoaded", function() {
    const documentsList = document.getElementById("documentsList");
    const documentStats = document.getElementById("documentStats");
    const elUserName = document.getElementById("userName");
    const elUserEmail = document.getElementById("userEmail");
    const elTotal = document.getElementById("totalDocuments");
    const elSigned = document.getElementById("signedDocuments");
    const elPending = document.getElementById("pendingDocuments");
    const elVerif = document.getElementById("verificationsCount");

    async function ensureApiToken() {
        if (signSecureAPI.token) return;
        try {
            const res = await signSecureAPI.createWebSession();
            const token = res?.token || res?.access_token || res?.data?.token || res?.plainTextToken;
            if (token) signSecureAPI.setToken(token);
        } catch (e) {
            // Ignorer si indisponible; les routes web restent utilisables
        }
    }

    async function loadDashboardData() {
        try {
            await ensureApiToken();
            // Charger les informations utilisateur
            const userResponse = await signSecureAPI.getUser();
            if (userResponse.success && userResponse.data?.user) {
                if (elUserName) elUserName.textContent = userResponse.data.user.name;
                if (elUserEmail) elUserEmail.textContent = userResponse.data.user.email;
            }

            // Charger les statistiques des documents
            // Charger l'overview agrégé
            const overview = await signSecureAPI.request('/dashboard/overview');
            if (overview.success && overview.data) {
                const { documents, verifications, retraces, recent_activity } = overview.data;
                const setText = (id, v) => { const el = document.getElementById(id); if (el) el.textContent = v ?? '-'; };

                // Documents
                setText('signedTotal', documents?.signed_total ?? 0);
                setText('lastSignedFilename', documents?.last_signed?.filename ?? '-');
                setText('lastSignedDate', documents?.last_signed?.date ? new Date(documents.last_signed.date).toLocaleString('fr-FR') : '-');
                const validEl = document.getElementById('validDocumentsCount');
                if (validEl) validEl.textContent = `Vous avez ${documents?.valid_documents_count ?? 0} documents valides.`;

                // Vérifications
                setText('verificationsTotal', verifications?.verifications_total ?? 0);
                setText('lastVerificationResult', verifications?.last_verification?.result ?? '-');
                setText('lastVerificationCode', verifications?.last_verification?.tracking_code ?? '-');

                // Retraces
                setText('retracesTotal', retraces?.retraces_total ?? 0);
                setText('lastRetraceCode', retraces?.last_retrace?.tracking_code ?? '-');
                setText('lastRetraceDate', retraces?.last_retrace?.date ? new Date(retraces.last_retrace.date).toLocaleString('fr-FR') : '-');

                // Activité récente
                const list = document.getElementById('recentActivityList');
                if (list) {
                    list.innerHTML = '';
                    (recent_activity || []).forEach(item => {
                        const li = document.createElement('li');
                        li.className = 'list-group-item d-flex justify-content-between align-items-center';
                        const when = item.at ? new Date(item.at).toLocaleString('fr-FR') : '-';
                        const label = item.type === 'signed' ? 'Document signé'
                                     : item.type === 'upload' ? 'Document téléversé'
                                     : item.type === 'verification' ? 'Vérification'
                                     : item.type === 'retrace' ? 'Retrace'
                                     : 'Activité';
                        li.innerHTML = `<div><strong>${label}:</strong> ${item.title || ''}</div><small class="text-muted">${when} • ${item.status || ''}</small>`;
                        list.appendChild(li);
                    });
                    if ((recent_activity || []).length === 0) {
                        list.innerHTML = '<li class="list-group-item text-muted">Aucune activité récente</li>';
                    }
                }
            }

            // Charger la liste des documents récents
            const documentsResponse = await signSecureAPI.getDocuments(1);
            if (documentsResponse.success && documentsResponse.data) {
                renderDocuments(documentsResponse.data);
            } else {
                renderDocuments([]);
            }

        } catch (error) {
            console.error('Erreur lors du chargement du tableau de bord:', error);
            SignSecureUI.showToast(error.message || "Erreur lors du chargement du tableau de bord.", "error");
            
            // Afficher des valeurs par défaut en cas d'erreur
            if (elTotal) elTotal.textContent = "0";
            if (elSigned) elSigned.textContent = "0";
            if (elPending) elPending.textContent = "0";
            if (elVerif) elVerif.textContent = "0";
            renderDocuments([]);
        }
    }

    function renderDocuments(documents) {
        if (!documentsList) return;
        
        documentsList.innerHTML = ""; // Vider la liste existante
        
        if (!Array.isArray(documents) || documents.length === 0) {
            documentsList.innerHTML = 
                `<div class="text-center text-muted py-4">
                    <i class="fas fa-folder-open fa-3x mb-3"></i>
                    <p>Aucun document trouvé. Téléversez votre premier document !</p>
                </div>`;
            return;
        }

        documents.forEach(doc => {
            const docItem = document.createElement("div");
            docItem.classList.add("list-group-item", "list-group-item-action", "d-flex", "justify-content-between", "align-items-center");
            
            const statusBadgeClass = doc.status === 'signed' ? 'success' : 
                                   doc.status === 'processing' ? 'info' : 'warning';
            
            const statusText = doc.status === 'signed' ? 'Signé' : 
                              doc.status === 'processing' ? 'En cours' : 'En attente';
            
            docItem.innerHTML = `
                <div>
                    <i class="fas fa-file-pdf me-2 text-danger"></i>
                    <strong>${doc.original_filename || 'Document sans nom'}</strong>
                    <small class="text-muted ms-3">${doc.file_size_human || 'Taille inconnue'}</small>
                </div>
                <div>
                    <span class="badge bg-${statusBadgeClass} me-2">${statusText}</span>
                    <button class="btn btn-sm btn-outline-primary me-1 btn-preview" data-id="${doc.id}" title="Prévisualiser">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${doc.status === 'signed' ? 
                        `<button class=\"btn btn-sm btn-outline-success btn-download\" data-id=\"${doc.id}\" title=\"Télécharger\">\n                            <i class=\"fas fa-download\"></i>\n                        </button>` : 
                        `<div class=\"btn-group btn-group-sm\" role=\"group\">\n                            <a href=\"/documents/${doc.id}/verify-otp\" class=\"btn btn-outline-info\" title=\"Signer\">\n                                <i class=\"fas fa-signature\"></i>\n                            </a>\n                            <a href=\"/signatures/${doc.id}/invite\" class=\"btn btn-outline-secondary\" title=\"Inviter des signataires\">\n                                <i class=\"fas fa-users\"></i>\n                            </a>\n                        </div>`
                    }
                </div>
            `;
            documentsList.appendChild(docItem);
        });

        // Actions pilotées par l'API
        documentsList.querySelectorAll('.btn-preview').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const id = e.currentTarget.getAttribute('data-id');
                try {
                    const blob = await signSecureAPI.getDocumentPreview(id);
                    const url = URL.createObjectURL(blob);
                    window.open(url, '_blank');
                    setTimeout(() => URL.revokeObjectURL(url), 60000);
                } catch (err) {
                    SignSecureUI.showToast(err.message || 'Erreur de prévisualisation', 'error');
                }
            });
        });

        documentsList.querySelectorAll('.btn-download').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const id = e.currentTarget.getAttribute('data-id');
                try {
                    const blob = await signSecureAPI.downloadDocument(id);
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'document.pdf';
                    document.body.appendChild(a);
                    a.click();
                    a.remove();
                    setTimeout(() => URL.revokeObjectURL(url), 60000);
                } catch (err) {
                    SignSecureUI.showToast(err.message || 'Erreur de téléchargement', 'error');
                }
            });
        });
    }

    // Charger les données au démarrage
    loadDashboardData();
    
    // Rafraîchir les données toutes les 30 secondes
    setInterval(loadDashboardData, 30000);
});


