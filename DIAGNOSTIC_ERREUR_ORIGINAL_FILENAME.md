# 🔍 Guide de Diagnostic - Erreur "original_filename sur la chaîne"

## 🚨 **Problème identifié**

L'erreur "Tentative de lecture de la propriété « original_filename » sur la chaîne" indique que le code essaie d'accéder à une propriété d'un objet qui est en fait une chaîne de caractères.

## 🔍 **Étapes de diagnostic**

### **Étape 1: Vérifier les logs Laravel**

```bash
# Vérifier les logs en temps réel
tail -f storage/logs/laravel.log

# Ou consulter le fichier complet
cat storage/logs/laravel.log
```

### **Étape 2: Tester les routes de diagnostic**

```bash
# Démarrer le serveur
php artisan serve

# Tester la création d'un document
curl http://localhost:8000/api/test/document

# Tester avec un document existant
curl http://localhost:8000/api/test/existing-document
```

### **Étape 3: Utiliser la page de test HTML**

Ouvrir `http://localhost:8000/test-document.html` dans le navigateur et exécuter les tests.

## 🧪 **Tests de diagnostic**

### **Test 1: Vérification du modèle Document**

```php
// Dans tinker ou un contrôleur de test
$document = new \App\Models\Document([
    'user_id' => 1,
    'original_filename' => 'test.pdf',
    'stored_filename' => 'test_stored.pdf',
    'file_path' => 'test/path',
    'file_size' => 1024,
    'mime_type' => 'application/pdf',
    'pages_count' => 1,
    'status' => 'uploaded',
]);

// Vérifier le type
var_dump(get_class($document));
var_dump($document->getAttributes());
var_dump($document->original_filename);
```

### **Test 2: Vérification du DocumentResource**

```php
// Créer le resource
$resource = new \App\Http\Resources\DocumentResource($document);

// Vérifier le type
var_dump(get_class($resource));
var_dump(get_class($resource->resource));

// Tester la conversion
$data = $resource->toArray(request());
var_dump($data);
```

### **Test 3: Vérification de la base de données**

```sql
-- Vérifier la structure de la table
DESCRIBE documents;

-- Vérifier les données existantes
SELECT id, original_filename, stored_filename, file_path FROM documents LIMIT 5;

-- Vérifier les contraintes
SHOW CREATE TABLE documents;
```

## 🚨 **Causes possibles**

### **1. Problème de migration**
- La colonne `original_filename` n'existe pas
- La migration n'a pas été exécutée
- Problème de type de données

### **2. Problème de modèle Eloquent**
- Le modèle Document n'hérite pas correctement de Model
- Problème avec les fillable/guarded
- Problème avec les casts

### **3. Problème de resource**
- Le resource reçoit une chaîne au lieu d'un modèle
- Problème avec la relation `$this->resource`
- Erreur dans la méthode `toArray`

### **4. Problème de contrôleur**
- Le document n'est pas correctement créé
- Problème avec la requête de base de données
- Erreur lors de l'instanciation du modèle

## 🔧 **Solutions proposées**

### **Solution 1: Vérifier et corriger la migration**

```bash
# Vérifier le statut des migrations
php artisan migrate:status

# Exécuter les migrations manquantes
php artisan migrate

# Vérifier la structure de la table
php artisan tinker
>>> Schema::getColumnListing('documents');
```

### **Solution 2: Corriger le modèle Document**

```php
// Vérifier que le modèle hérite correctement
class Document extends Model
{
    use HasFactory;
    
    protected $table = 'documents';
    
    protected $fillable = [
        'user_id',
        'original_filename',
        'stored_filename',
        'file_path',
        // ... autres champs
    ];
    
    // Ajouter des accesseurs si nécessaire
    public function getOriginalFilenameAttribute($value)
    {
        return $value ?? 'Document sans nom';
    }
}
```

### **Solution 3: Améliorer le DocumentResource**

```php
public function toArray(Request $request): array
{
    try {
        // Vérifier que le resource est valide
        if (!$this->resource || !is_object($this->resource)) {
            throw new \Exception('Resource invalide dans DocumentResource');
        }
        
        // Vérifier que c'est bien un modèle Document
        if (!($this->resource instanceof \App\Models\Document)) {
            throw new \Exception('Resource n\'est pas un modèle Document');
        }
        
        return [
            'id' => $this->resource->id ?? 0,
            'original_filename' => $this->resource->original_filename ?? 'Document sans nom',
            // ... autres champs
        ];
        
    } catch (\Exception $e) {
        Log::error('Erreur dans DocumentResource::toArray', [
            'error' => $e->getMessage(),
            'resource_type' => gettype($this->resource),
            'resource_class' => is_object($this->resource) ? get_class($this->resource) : 'N/A'
        ]);
        
        return [
            'error' => 'Erreur lors du traitement du document',
            'details' => $e->getMessage()
        ];
    }
}
```

### **Solution 4: Améliorer le contrôleur**

```php
public function store(DocumentUploadRequest $request): JsonResponse
{
    try {
        // ... validation et stockage du fichier
        
        // Créer le document avec vérification
        $document = Document::create([
            'user_id' => $request->user()->id,
            'original_filename' => $file->getClientOriginalName(),
            // ... autres champs
        ]);
        
        // Vérifier que le document a été créé
        if (!$document || !$document->exists) {
            throw new \Exception('Échec de la création du document');
        }
        
        // Vérifier les attributs
        Log::info('Document créé', [
            'id' => $document->id,
            'class' => get_class($document),
            'attributes' => $document->getAttributes(),
            'original_filename' => $document->original_filename
        ]);
        
        // Créer le resource avec gestion d'erreur
        try {
            $documentResource = new DocumentResource($document);
            $data = $documentResource->toArray($request);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la création du resource', [
                'error' => $e->getMessage(),
                'document' => $document->toArray()
            ]);
            throw $e;
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Document téléversé avec succès',
            'data' => $data
        ], 201);
        
    } catch (\Exception $e) {
        Log::error('Erreur lors du téléversement', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        return response()->json([
            'success' => false,
            'message' => 'Erreur lors du téléversement',
            'error' => $e->getMessage()
        ], 500);
    }
}
```

## 📋 **Checklist de vérification**

- [ ] Les migrations sont à jour et exécutées
- [ ] La table `documents` contient la colonne `original_filename`
- [ ] Le modèle Document hérite correctement de Model
- [ ] Les attributs fillable sont correctement définis
- [ ] Le DocumentResource gère les erreurs
- [ ] Le contrôleur vérifie la création du document
- [ ] Les logs sont activés et consultables

## 🚀 **Test de résolution**

Après avoir appliqué les corrections :

1. **Redémarrer le serveur** : `php artisan serve`
2. **Tester la route de diagnostic** : `/api/test/document`
3. **Tester le téléversement** via la page HTML
4. **Vérifier les logs** pour confirmer l'absence d'erreurs

## 📞 **Support**

Si le problème persiste après avoir suivi ce guide :

1. Consulter les logs Laravel (`storage/logs/laravel.log`)
2. Vérifier la structure de la base de données
3. Tester avec un document simple
4. Vérifier les versions des packages Laravel

---

**Note** : Ce guide est conçu pour diagnostiquer et résoudre l'erreur spécifique "original_filename sur la chaîne". Suivez les étapes dans l'ordre pour identifier la cause racine du problème.
