<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class SignatureCaptureRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'signature_data' => [
                'required', 
                'string',
                'regex:/^data:image\/(png|jpeg|jpg);base64,/',
                'max:1048576' // 1MB max pour l'image base64
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'signature_data.required' => 'Les données de signature sont obligatoires.',
            'signature_data.regex' => 'Le format de signature doit être une image base64 valide (PNG, JPEG ou JPG).',
            'signature_data.max' => 'La signature est trop volumineuse (maximum 1MB).',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Nettoyer les données de signature si nécessaire
        if ($this->has('signature_data')) {
            $signatureData = $this->input('signature_data');
            
            // Supprimer les espaces et retours à la ligne
            $signatureData = preg_replace('/\s+/', '', $signatureData);
            
            $this->merge([
                'signature_data' => $signatureData,
            ]);
        }
    }
}