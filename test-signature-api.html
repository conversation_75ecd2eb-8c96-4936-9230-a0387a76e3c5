<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Signature</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            background-color: #f8f9fa;
        }
        .tab.active {
            background-color: white;
            border-color: #dee2e6;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .canvas-container {
            border: 2px dashed #ccc;
            margin: 20px 0;
            text-align: center;
            padding: 20px;
        }
        #signatureCanvas {
            border: 1px solid #ddd;
            background-color: white;
        }
        .clear-btn {
            background-color: #dc3545;
            margin-top: 10px;
        }
        .clear-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <h1>🧪 Test API Signature</h1>
    
    <div class="container">
        <div class="form-group">
            <label for="baseUrl">URL de base de l'API:</label>
            <input type="text" id="baseUrl" value="http://localhost/signsecure/public/api/v1" />
        </div>
        <div class="form-group">
            <label for="token">Token Sanctum:</label>
            <input type="text" id="token" placeholder="Entrez votre token d'authentification" />
        </div>
    </div>

    <div class="tabs">
        <div class="tab active" onclick="showTab('capture')">Capturer une signature</div>
        <div class="tab" onclick="showTab('apply')">Appliquer une signature</div>
        <div class="tab" onclick="showTab('preview')">Prévisualiser</div>
        <div class="tab" onclick="showTab('current')">Signature actuelle</div>
        <div class="tab" onclick="showTab('history')">Historique</div>
    </div>

    <!-- Tab 1: Capturer une signature -->
    <div id="capture" class="tab-content active">
        <div class="container">
            <h3>Capturer une signature</h3>
            <div class="canvas-container">
                <canvas id="signatureCanvas" width="400" height="200"></canvas>
                <br>
                <button onclick="clearCanvas()" class="clear-btn">Effacer</button>
            </div>
            <button onclick="captureSignature()">Capturer la signature</button>
            <div id="captureResponse" class="response"></div>
        </div>
    </div>

    <!-- Tab 2: Appliquer une signature -->
    <div id="apply" class="tab-content">
        <div class="container">
            <h3>Appliquer une signature sur un document</h3>
            <div class="form-group">
                <label for="applyDocumentId">ID du document:</label>
                <input type="number" id="applyDocumentId" placeholder="Entrez l'ID du document" min="1" />
            </div>
            <div class="form-group">
                <label for="positionX">Position X (coordonnée horizontale):</label>
                <input type="number" id="positionX" value="100" min="0" step="1" />
                <small>Position horizontale en pixels depuis la gauche de la page</small>
            </div>
            <div class="form-group">
                <label for="positionY">Position Y (coordonnée verticale):</label>
                <input type="number" id="positionY" value="100" min="0" step="1" />
                <small>Position verticale en pixels depuis le haut de la page</small>
            </div>
            <div class="form-group">
                <label for="pageNumber">Numéro de page:</label>
                <input type="number" id="pageNumber" value="1" min="1" step="1" />
                <small>Page sur laquelle appliquer la signature (commence à 1)</small>
            </div>
            <button onclick="applySignature()">Appliquer la signature</button>
            <div id="applyResponse" class="response"></div>
        </div>
    </div>

    <!-- Tab 3: Prévisualiser une signature -->
    <div id="preview" class="tab-content">
        <div class="container">
            <h3>Prévisualiser une signature sur un document</h3>
            <div class="form-group">
                <label for="previewDocumentId">ID du document:</label>
                <input type="number" id="previewDocumentId" placeholder="Entrez l'ID du document" min="1" />
            </div>
            <div class="form-group">
                <label for="previewPositionX">Position X:</label>
                <input type="number" id="previewPositionX" value="100" min="0" step="1" />
            </div>
            <div class="form-group">
                <label for="previewPositionY">Position Y:</label>
                <input type="number" id="previewPositionY" value="100" min="0" step="1" />
            </div>
            <div class="form-group">
                <label for="previewPageNumber">Numéro de page:</label>
                <input type="number" id="previewPageNumber" value="1" min="1" step="1" />
            </div>
            <button onclick="previewSignature()">Prévisualiser</button>
            <div id="previewResponse" class="response"></div>
        </div>
    </div>

    <!-- Tab 4: Signature actuelle -->
    <div id="current" class="tab-content">
        <div class="container">
            <h3>Signature actuelle de l'utilisateur</h3>
            <button onclick="getCurrentSignature()">Récupérer la signature actuelle</button>
            <div id="currentResponse" class="response"></div>
        </div>
    </div>

    <!-- Tab 5: Historique des signatures -->
    <div id="history" class="tab-content">
        <div class="container">
            <h3>Historique des signatures</h3>
            <button onclick="getSignatureHistory()">Récupérer l'historique</button>
            <div id="historyResponse" class="response"></div>
        </div>
    </div>

    <script>
        let canvas, ctx, isDrawing = false;

        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function getBaseUrl() {
            return document.getElementById('baseUrl').value;
        }

        function getToken() {
            return document.getElementById('token').value;
        }

        function showResponse(elementId, response, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(response, null, 2);
            element.className = 'response ' + (isSuccess ? 'success' : 'error');
        }

        async function makeRequest(url, options = {}) {
            const token = getToken();
            if (!token) {
                alert('Veuillez entrer un token d\'authentification');
                return null;
            }

            const defaultOptions = {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };

            try {
                const response = await fetch(url, { ...defaultOptions, ...options });
                const data = await response.json();
                return { response, data, status: response.status };
            } catch (error) {
                return { error: error.message };
            }
        }

        // Initialisation du canvas
        function initCanvas() {
            canvas = document.getElementById('signatureCanvas');
            ctx = canvas.getContext('2d');
            
            // Style du canvas
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            
            // Événements de dessin
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);
            
            // Support tactile
            canvas.addEventListener('touchstart', startDrawing);
            canvas.addEventListener('touchmove', draw);
            canvas.addEventListener('touchend', stopDrawing);
        }

        function startDrawing(e) {
            isDrawing = true;
            draw(e);
        }

        function draw(e) {
            if (!isDrawing) return;
            
            e.preventDefault();
            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX || e.touches[0].clientX) - rect.left;
            const y = (e.clientY || e.touches[0].clientY) - rect.top;
            
            ctx.lineTo(x, y);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(x, y);
        }

        function stopDrawing() {
            isDrawing = false;
            ctx.beginPath();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        async function captureSignature() {
            const dataUrl = canvas.toDataURL('image/png');
            
            const result = await makeRequest(`${getBaseUrl()}/signatures/capture`, {
                method: 'POST',
                body: JSON.stringify({
                    signature_data: dataUrl
                })
            });

            if (result.error) {
                showResponse('captureResponse', { error: result.error }, false);
            } else {
                showResponse('captureResponse', result.data, result.status < 400);
            }
        }

        async function applySignature() {
            const data = {
                document_id: parseInt(document.getElementById('applyDocumentId').value),
                position_x: parseFloat(document.getElementById('positionX').value),
                position_y: parseFloat(document.getElementById('positionY').value),
                page_number: parseInt(document.getElementById('pageNumber').value)
            };

            // Validation côté client
            if (!data.document_id || !data.position_x || !data.position_y || !data.page_number) {
                alert('Tous les champs sont obligatoires');
                return;
            }

            console.log('Données envoyées:', data);

            const result = await makeRequest(`${getBaseUrl()}/signatures/apply`, {
                method: 'POST',
                body: JSON.stringify(data)
            });

            if (result.error) {
                showResponse('applyResponse', { error: result.error }, false);
            } else {
                showResponse('applyResponse', result.data, result.status < 400);
            }
        }

        async function previewSignature() {
            const data = {
                document_id: parseInt(document.getElementById('previewDocumentId').value),
                position_x: parseFloat(document.getElementById('previewPositionX').value),
                position_y: parseFloat(document.getElementById('previewPositionY').value),
                page_number: parseInt(document.getElementById('previewPageNumber').value)
            };

            const result = await makeRequest(`${getBaseUrl()}/signatures/preview`, {
                method: 'POST',
                body: JSON.stringify(data)
            });

            if (result.error) {
                showResponse('previewResponse', { error: result.error }, false);
            } else {
                showResponse('previewResponse', result.data, result.status < 400);
            }
        }

        async function getCurrentSignature() {
            const result = await makeRequest(`${getBaseUrl()}/signatures/current`, { method: 'GET' });

            if (result.error) {
                showResponse('currentResponse', { error: result.error }, false);
            } else {
                showResponse('currentResponse', result.data, result.status < 400);
            }
        }

        async function getSignatureHistory() {
            const result = await makeRequest(`${getBaseUrl()}/signatures/history`, { method: 'GET' });

            if (result.error) {
                showResponse('historyResponse', { error: result.error }, false);
            } else {
                showResponse('historyResponse', result.data, result.status < 400);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Page de test de l\'API Signature chargée');
            initCanvas();
            
            console.log('📝 Instructions:');
            console.log('1. Assurez-vous que votre serveur Laravel est démarré');
            console.log('2. Entrez votre token Sanctum d\'authentification');
            console.log('3. Testez les différents endpoints de l\'API');
            console.log('4. Pour appliquer une signature, tous les champs sont obligatoires:');
            console.log('   - document_id: ID du document');
            console.log('   - position_x: Position horizontale (en pixels)');
            console.log('   - position_y: Position verticale (en pixels)');
            console.log('   - page_number: Numéro de page (commence à 1)');
        });
    </script>
</body>
</html>
