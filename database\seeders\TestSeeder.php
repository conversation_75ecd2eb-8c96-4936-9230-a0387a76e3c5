<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Document;

class TestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur de test
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        $this->command->info("Utilisateur de test créé: {$user->id}");

        // Créer un document de test
        $document = Document::firstOrCreate(
            ['tracking_code' => 'TEST123'],
            [
                'user_id' => $user->id,
                'original_filename' => 'test_document.pdf',
                'stored_filename' => 'test_stored.pdf',
                'file_path' => 'test/path',
                'file_size' => 1024,
                'mime_type' => 'application/pdf',
                'pages_count' => 1,
                'status' => 'uploaded',
            ]
        );

        $this->command->info("Document de test créé: {$document->id}");
    }
}
