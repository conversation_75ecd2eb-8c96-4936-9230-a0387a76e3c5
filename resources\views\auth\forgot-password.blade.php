@extends('layouts.app')

@section('title', 'Mot de passe oublié - SignSecure')

@section('content')
<div class="card mx-auto mt-5 p-4" style="max-width: 480px;">
    <h3 class="text-center mb-3">Mot de passe oublié</h3>
    <p class="text-muted text-center mb-4">Entrez votre adresse e-mail pour recevoir un lien de réinitialisation.</p>

    @if (session('status'))
        <div class="alert alert-success">{{ session('status') }}</div>
    @endif

    <form method="POST" action="{{ route('password.email') }}">
        @csrf
        <div class="mb-3">
            <label for="email" class="form-label">Adresse e-mail</label>
            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required autofocus>
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="d-grid">
            <button type="submit" class="btn btn-primary">Envoyer le lien</button>
        </div>
    </form>

    <div class="text-center mt-3">
        <a href="{{ route('login') }}" class="text-decoration-none">Retour à la connexion</a>
    </div>
</div>
@endsection


