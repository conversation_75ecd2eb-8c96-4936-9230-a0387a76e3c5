<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AdminUserSeeder extends Seeder
{
    public function run(): void
    {
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin SignSecure',
                'password' => Hash::make('MotDePasseUltraSecurisé'),
                'role' => 'admin',
            ]
        );
    }
}


