<?php

namespace App\Services;

use App\Models\OTPCode;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;

class OTPService
{
    /**
     * Génère et envoie un code OTP à l'utilisateur pour un document spécifique.
     *
     * @param User $user
     * @param string $type
     * @param int|null $document_id
     * @return string
     * @throws \Exception
     */
    public function generateAndSend(User $user, string $type = 'email', ?int $document_id = null): string
    {
        // Invalider les codes OTP précédents non utilisés pour ce document
        $query = $user->otpCodes()
            ->whereNull('used_at')
            ->where('expires_at', '>', now())
            ->when($document_id, fn($q) => $q->where('document_id', $document_id));

        if ($document_id) {
            $query->where('document_id', $document_id);
        }

        $query->update(['used_at' => now()]);

        // Générer un nouveau code
        $code = $this->generateCode();

        // Créer l'enregistrement OTP (stockage hashé dans code_hashed si dispo, sinon code)
        $data = [
            'user_id' => $user->id,
            'document_id' => $document_id,
            'type' => $type,
            'expires_at' => now()->addMinutes(10),
        ];

        if (Schema::hasColumn('otp_codes', 'code_hashed')) {
            $data['code_hashed'] = Hash::make($code);
        } else {
            // fallback si colonne 'code' courte: tenter string(255)
            $data['code'] = Hash::make($code);
        }

        $otpCode = OTPCode::create($data);

        // Envoi
        if ($type === 'email') {
            $this->sendByEmail($user, $code);
        } elseif ($type === 'sms') {
            $this->sendBySMS($user, $code);
        } else {
            throw new \Exception("Type d'envoi OTP non supporté: $type");
        }

        return $code;
    }

    /**
     * Vérifie un code OTP pour un document spécifique.
     *
     * @param User $user
     * @param string $code
     * @param int|null $document_id
     * @return bool
     */
    public function verify(User $user, string $code, ?int $document_id = null): bool
    {
        // On ne peut plus filtrer par code en base (stockage hashé). On récupère les OTP valides récents
        $query = $user->otpCodes()
            ->whereNull('used_at')
            ->where('expires_at', '>', now());

        if ($document_id) {
            $query->where('document_id', $document_id);
        }

        $otpCodes = $query->get();

        foreach ($otpCodes as $otpCode) {
            $stored = $otpCode->code_hashed ?? $otpCode->code;
            if (!$stored) {
                continue;
            }

            $looksHashed = str_starts_with($stored, '$2y$')
                || str_starts_with($stored, '$2b$')
                || str_starts_with($stored, '$argon2');

            $matches = $looksHashed ? Hash::check($code, $stored) : ($stored === $code);

            if ($matches) {
                // Marquer utilisé
                $otpCode->used_at = now();
                // Migrer silencieusement les anciens enregistrements en clair
                if (!$looksHashed) {
                    $otpCode->code_hashed = Hash::make($code);
                    $otpCode->code = null;
                }
                $otpCode->save();
                return true;
            }
        }

        return false;
    }

    /**
     * Génère un code OTP à 6 chiffres.
     */
    private function generateCode(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    private function sendByEmail(User $user, string $code): void
    {
        $expirationMinutes = config('signsecure.otp.expiration_minutes', 10);
        $expiresAt = now()->addMinutes($expirationMinutes)->format('d/m/Y H:i');
        $subject = 'Code de vérification SignSecure';
        $viewData = [
            'code' => $code,
            'expires_at' => $expiresAt,
            'expiration_minutes' => $expirationMinutes,
            // Force explicit brand to avoid fallback to "Laravel" when APP_NAME is not set
            'companyName' => 'SignSecure',
            'brandFirst' => 'Sign',
            'brandSecond' => 'Secure',
            'logoUrl' => null,
            'ctaUrl' => null,
            'supportText' => config('mail.from.address', '<EMAIL>'),
        ];

        Mail::send(['html' => 'emails.otp-code', 'text' => 'emails.otp-code.txt'], $viewData, function ($mail) use ($user, $subject) {
            $mail->to($user->email, $user->name)->subject($subject);
        });
    }

    private function sendBySMS(User $user, string $code): void
    {
        if (!$user->phone) throw new \Exception("Aucun numéro de téléphone enregistré.");

        $endpoint = config('services.fastermessage.endpoint');
        $apiKey = config('services.fastermessage.api_key');
        $from = config('services.fastermessage.from', 'SignSecure');
        $defaultCode = config('services.fastermessage.default_country_code', '+229');

        // Normalize to E.164
        $normalizedPhone = $this->normalizePhoneNumber($user->phone, $defaultCode);

        $text = "Votre code OTP SignSecure est: {$code}. Valide 10 minutes.";

        if (!$endpoint || !$apiKey) {
            // Fallback to log if not configured
            Log::warning('FasterMessage non configuré. SMS non envoyé, fallback log.', [
                'phone' => $normalizedPhone,
                'text' => $text,
            ]);
            return;
        }

        try {
            $ch = curl_init($endpoint);
            $payload = [
                'from' => $from,
                'to' => $normalizedPhone,
                'text' => $text,
            ];
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ["X-API-KEY: " . $apiKey]);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
            $result = curl_exec($ch);
            if ($result === false) {
                $error = curl_error($ch);
                curl_close($ch);
                throw new \RuntimeException('Erreur cURL FasterMessage: ' . $error);
            }
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode < 200 || $httpCode >= 300) {
                Log::error('FasterMessage API HTTP error', ['status' => $httpCode, 'response' => $result]);
                throw new \RuntimeException('Envoi SMS échoué (HTTP ' . $httpCode . ')');
            }

            Log::info('SMS OTP envoyé via FasterMessage', [
                'phone' => $normalizedPhone,
                'response' => $result,
            ]);
        } catch (\Throwable $e) {
            Log::error('Échec envoi SMS OTP', ['error' => $e->getMessage()]);
            // Ne pas interrompre le flow principal silencieusement: remonter l'erreur
            throw $e;
        }
    }

    private function normalizePhoneNumber(?string $raw, string $defaultCode): string
    {
        $raw = trim((string)$raw);
        $defaultDigits = preg_replace('/\D+/', '', $defaultCode);
        if ($raw === '') {
            throw new \InvalidArgumentException('Numéro de téléphone vide');
        }
        if (str_starts_with($raw, '+')) {
            $digits = preg_replace('/\D+/', '', $raw);
            return '+' . $digits;
        }
        $digits = preg_replace('/\D+/', '', $raw);
        if (str_starts_with($digits, '00')) {
            return '+' . substr($digits, 2);
        }
        if (str_starts_with($digits, $defaultDigits)) {
            return '+' . $digits;
        }
        $digits = ltrim($digits, '0');
        return '+' . $defaultDigits . $digits;
    }

    /**
     * Vérifie si l'utilisateur peut renvoyer un code OTP pour un document.
     * 
     * @param User $user
     * @param int|null $document_id
     * @param int $waitMinutes Temps d'attente minimum entre deux envois (default 2 min)
     */
    public function canResend(User $user, ?int $document_id = null, int $waitMinutes = 2): bool
    {
        $lastOtp = $user->otpCodes()
            ->when($document_id, fn($q) => $q->where('document_id', $document_id))
            ->latest()
            ->first();

        if (!$lastOtp) return true;

        // Peut renvoyer si le dernier code expire dans plus de $waitMinutes
        return $lastOtp->created_at->addMinutes($waitMinutes)->isPast();
    }

    /**
     * Génère et envoie un code OTP pour les retraces (sans document_id).
     *
     * @param User $user
     * @param string $type
     * @return string
     * @throws \Exception
     */
    public function generateAndSendForRetrace(User $user, string $type = 'email'): string
    {
        // Invalider les codes OTP précédents non utilisés pour les retraces
        $user->otpCodes()
            ->whereNull('document_id')
            ->whereNull('used_at')
            ->where('expires_at', '>', now())
            ->update(['used_at' => now()]);

        // Générer un nouveau code
        $code = $this->generateCode();

        // Créer l'enregistrement OTP sans document_id
        $data = [
            'user_id' => $user->id,
            'document_id' => null, // Pas de document pour les retraces
            'type' => $type,
            'expires_at' => now()->addMinutes(10),
        ];

        if (Schema::hasColumn('otp_codes', 'code_hashed')) {
            $data['code_hashed'] = Hash::make($code);
        } else {
            $data['code'] = Hash::make($code);
        }

        $otpCode = OTPCode::create($data);

        // Envoi
        if ($type === 'email') {
            $this->sendByEmail($user, $code);
        } elseif ($type === 'sms') {
            $this->sendBySMS($user, $code);
        } else {
            throw new \Exception("Type d'envoi OTP non supporté: $type");
        }

        return $code;
    }

    /**
     * Vérifie un code OTP pour les retraces (sans document_id).
     *
     * @param User $user
     * @param string $code
     * @return bool
     */
    public function verifyForRetrace(User $user, string $code): bool
    {
        // Récupérer les OTP valides récents sans document_id
        $otpCodes = $user->otpCodes()
            ->whereNull('document_id')
            ->whereNull('used_at')
            ->where('expires_at', '>', now())
            ->get();

        foreach ($otpCodes as $otpCode) {
            $stored = $otpCode->code_hashed ?? $otpCode->code;
            if (!$stored) {
                continue;
            }

            $looksHashed = str_starts_with($stored, '$2y$')
                || str_starts_with($stored, '$2b$')
                || str_starts_with($stored, '$argon2');

            $matches = $looksHashed ? Hash::check($code, $stored) : ($stored === $code);

            if ($matches) {
                // Marquer utilisé
                $otpCode->used_at = now();
                // Migrer silencieusement les anciens enregistrements en clair
                if (!$looksHashed) {
                    $otpCode->code_hashed = Hash::make($code);
                    $otpCode->code = null;
                }
                $otpCode->save();
                return true;
            }
        }

        return false;
    }
}
