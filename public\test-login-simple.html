<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Simple - SignSecure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        input, button { padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        button { background-color: #007bff; color: white; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .log-entry { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .log-entry.success { background-color: #d4edda; }
        .log-entry.error { background-color: #f8d7da; }
        .log-entry.info { background-color: #d1ecf1; }
    </style>
</head>
<body>
    <h1>🔍 Test Login Simple - SignSecure</h1>
    
    <div class="test-section info">
        <h3>Test de Login Direct</h3>
        <p>Cette page teste directement l'API de login pour diagnostiquer le problème.</p>
    </div>

    <div class="test-section">
        <h3>Formulaire de Test</h3>
        <div>
            <input type="email" id="testEmail" placeholder="<EMAIL>" value="<EMAIL>">
            <input type="password" id="testPassword" placeholder="password" value="password">
            <button onclick="testLoginDirect()">Tester Login Direct</button>
        </div>
        <div id="result"></div>
    </div>

    <div class="test-section">
        <h3>Test avec l'API Client</h3>
        <button onclick="testLoginAPI()">Tester avec API Client</button>
        <div id="resultAPI"></div>
    </div>

    <div class="test-section">
        <h3>Logs de débogage</h3>
        <div id="logs"></div>
    </div>

    <script src="/js/api.js"></script>
    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function displayResult(containerId, data, isSuccess = true) {
            const container = document.getElementById(containerId);
            container.className = `test-section ${isSuccess ? 'success' : 'error'}`;
            container.innerHTML = `
                <h3>${isSuccess ? '✅ Succès' : '❌ Erreur'}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        async function testLoginDirect() {
            try {
                const email = document.getElementById('testEmail').value;
                const password = document.getElementById('testPassword').value;
                
                log('🧪 Test de login direct...');
                log(`Email: ${email}, Password: ${password ? '***' : 'VIDE'}`);
                
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                log(`Status: ${response.status}`);
                log(`Response: ${JSON.stringify(data)}`);
                
                if (response.ok) {
                    log('✅ Login réussi', 'success');
                    displayResult('result', { status: response.status, data }, true);
                } else {
                    log('❌ Login échoué', 'error');
                    displayResult('result', { status: response.status, error: data }, false);
                }
                
            } catch (error) {
                log(`❌ Erreur: ${error.message}`, 'error');
                displayResult('result', { error: error.message }, false);
            }
        }

        async function testLoginAPI() {
            try {
                const email = document.getElementById('testEmail').value;
                const password = document.getElementById('testPassword').value;
                
                log('🧪 Test avec API Client...');
                log(`Email: ${email}, Password: ${password ? '***' : 'VIDE'}`);
                
                const response = await signSecureAPI.login({ email, password });
                
                if (response.success) {
                    log('✅ Login réussi avec API Client', 'success');
                    displayResult('resultAPI', response, true);
                } else {
                    log('❌ Login échoué avec API Client', 'error');
                    displayResult('resultAPI', response, false);
                }
                
            } catch (error) {
                log(`❌ Erreur avec API Client: ${error.message}`, 'error');
                displayResult('resultAPI', { error: error.message }, false);
            }
        }

        // Initialisation
        log('🚀 Page de test de login simple chargée', 'success');
        log('Assurez-vous que le serveur Laravel est démarré', 'info');
    </script>
</body>
</html>
