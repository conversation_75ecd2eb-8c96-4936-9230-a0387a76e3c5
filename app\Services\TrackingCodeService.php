<?php

namespace App\Services;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class TrackingCodeService
{
    /**
     * Génère un code de suivi court pour un document signé
     * Format: SS-XXXX-XXXX (8 caractères + tirets)
     */
    public static function generateDocumentCode(): string
    {
        do {
            $code = 'SS-' . strtoupper(Str::random(4)) . '-' . strtoupper(Str::random(4));
        } while (self::codeExists($code, 'documents'));
        
        return $code;
    }

    /**
     * Génère un code de suivi court pour un retrace physique
     * Format: SS-PR-XXXX-XXXX (12 caractères + tirets)
     */
    public static function generateRetraceCode(): string
    {
        do {
            $code = 'SS-PR-' . strtoupper(Str::random(4)) . '-' . strtoupper(Str::random(4));
        } while (self::codeExists($code, 'physical_retraces'));
        
        return $code;
    }

    /**
     * Vérifie si un code existe déjà dans la base de données
     */
    private static function codeExists(string $code, string $table): bool
    {
        return DB::table($table)->where('tracking_code', $code)->exists();
    }

    /**
     * Valide le format d'un code de suivi
     */
    public static function isValidFormat(string $code): bool
    {
        // Format pour documents: SS-XXXX-XXXX
        if (preg_match('/^SS-[A-Z0-9]{4}-[A-Z0-9]{4}$/', $code)) {
            return true;
        }
        
        // Format pour retraces: SS-PR-XXXX-XXXX
        if (preg_match('/^SS-PR-[A-Z0-9]{4}-[A-Z0-9]{4}$/', $code)) {
            return true;
        }
        
        return false;
    }

    /**
     * Détermine le type de code (document ou retrace)
     */
    public static function getCodeType(string $code): string
    {
        if (preg_match('/^SS-PR-/', $code)) {
            return 'retrace';
        }
        
        if (preg_match('/^SS-/', $code)) {
            return 'document';
        }
        
        return 'unknown';
    }
}
