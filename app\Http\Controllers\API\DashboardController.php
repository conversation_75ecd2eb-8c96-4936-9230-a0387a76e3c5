<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Document;
use App\Models\VerificationLog;
use App\Models\PhysicalRetrace;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        // Statistiques de l'utilisateur
        $totalDocuments = Document::where('user_id', $user->id)->count();
        $signedDocuments = Document::where('user_id', $user->id)->where('status', 'signed')->count();
        $pendingDocuments = Document::where('user_id', $user->id)->where('status', 'pending')->count();
        
        // Documents récents
        $recentDocuments = Document::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Vérifications
        $userTrackingCodes = Document::where('user_id', $user->id)
            ->whereNotNull('tracking_code')
            ->pluck('tracking_code');
        $verificationsTotal = VerificationLog::whereIn('tracking_code', $userTrackingCodes)->count();
        
        // Retraces physiques
        $retracesTotal = PhysicalRetrace::where('user_id', $user->id)->count();

        return response()->json([
            'success' => true,
            'data' => [
                'totalDocuments' => $totalDocuments,
                'signedDocuments' => $signedDocuments,
                'pendingDocuments' => $pendingDocuments,
                'recentDocuments' => $recentDocuments,
                'verificationsTotal' => $verificationsTotal,
                'retracesTotal' => $retracesTotal,
            ]
        ]);
    }
}


