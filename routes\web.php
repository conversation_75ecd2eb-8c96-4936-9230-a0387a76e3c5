<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PhysicalRetraceController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Routes servant uniquement les vues Blade pour le front.
| La logique métier et les appels AJAX seront gérés via l'API.
|
*/

// Page d'accueil
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentification (pages de connexion et d'inscription)
Route::middleware('guest')->group(function () {
    Route::view('/login', 'auth.login')->name('login');
    Route::view('/register', 'auth.register')->name('register');
    Route::get('/forgot-password', [\App\Http\Controllers\AuthController::class, 'showForgotPasswordForm'])->name('password.request');
    Route::post('/forgot-password', [\App\Http\Controllers\AuthController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/reset-password/{token}', [\App\Http\Controllers\AuthController::class, 'showResetForm'])->name('password.reset');
    Route::post('/reset-password', [\App\Http\Controllers\AuthController::class, 'resetPassword'])->name('password.update');
});

// Route pour créer une session après connexion API
Route::post('/auth/create-session', function(\Illuminate\Http\Request $request) {
    $token = $request->input('api_token');
    if ($token) {
        // Récupérer l'utilisateur via le token Sanctum
        $user = \Laravel\Sanctum\PersonalAccessToken::findToken($token)?->tokenable;
        
        if ($user) {
            // Connecter l'utilisateur avec le guard web
            Auth::guard('web')->login($user);
            return redirect()->route('dashboard');
        }
    }
    return redirect()->route('login')->with('error', 'Token invalide');
})->name('auth.create-session');

// Routes protégées par authentification
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Profil (vue seulement, actions via API)
    Route::view('/profile', 'profile.edit')->name('profile.show');
    Route::view('/profile/edit', 'profile.edit')->name('profile.edit');

    // Documents
    Route::prefix('documents')->name('documents.')->group(function () {
        Route::get('/', [\App\Http\Controllers\DocumentController::class, 'index'])->name('index');
        Route::view('/upload', 'documents.upload')->name('upload');
        Route::get('/{document}', [\App\Http\Controllers\DocumentController::class, 'show'])->name('show');
        Route::get('/{document}/preview', [\App\Http\Controllers\DocumentController::class, 'preview'])->name('preview');
        Route::get('/{document}/pdf', [\App\Http\Controllers\DocumentController::class, 'getPdf'])->name('pdf');
        Route::get('/{document}/verify-otp', [\App\Http\Controllers\DocumentController::class, 'showOtpForm'])->name('verify-otp');
        Route::post('/{document}/verify-otp', [\App\Http\Controllers\DocumentController::class, 'verifyOtp']);
        Route::post('/{document}/generate-otp', [\App\Http\Controllers\DocumentController::class, 'generateOtp'])->name('generate-otp');
        Route::get('/{document}/download', [\App\Http\Controllers\DocumentController::class, 'download'])->name('download');
    });

    // Routes pour le flux de signature complet
    Route::prefix('signatures')->name('signatures.')->group(function () {
        Route::get('/{document}/capture', [\App\Http\Controllers\SignatureFlowController::class, 'showSignatureCapture'])->name('capture');
        Route::get('/{document}/preview', [\App\Http\Controllers\SignatureFlowController::class, 'showSignaturePreview'])->name('preview');
        Route::get('/{document}/invite', [\App\Http\Controllers\SignatureFlowController::class, 'showInviteForm'])->name('invite');
        Route::post('/{document}/capture', [\App\Http\Controllers\SignatureFlowController::class, 'captureSignature'])->name('capture-signature');
        Route::post('/{document}/apply', [\App\Http\Controllers\SignatureFlowController::class, 'applySignature'])->name('apply-signature');
        Route::post('/{document}/invite', [\App\Http\Controllers\SignatureFlowController::class, 'inviteSigners'])->name('invite-signers');
    });

    // Signatures
    Route::prefix('signatures')->name('signatures.')->group(function () {
        Route::view('/create', 'signatures.create')->name('create');
        Route::view('/current', 'signatures.current')->name('current');
    });

    // Retrace d'un document physique
    Route::prefix('retrace')->name('retrace.')->group(function () {
        Route::get('/create', [PhysicalRetraceController::class, 'create'])->name('create');
        Route::post('/request-otp', [PhysicalRetraceController::class, 'requestOtp'])->name('request-otp');
        Route::get('/verify-otp', [PhysicalRetraceController::class, 'showVerifyOtp'])->name('verify-otp');
        Route::post('/verify-otp', [PhysicalRetraceController::class, 'verifyOtp'])->name('verify-otp.post');
        Route::get('/{retrace}', [PhysicalRetraceController::class, 'show'])->name('show');
        Route::get('/{retrace}/download', function (\App\Models\PhysicalRetrace $retrace) {
            abort_unless(Auth::id() === $retrace->user_id, 403);
            if (!$retrace->pdf_path) abort(404);
            $path = \Illuminate\Support\Facades\Storage::disk('private')->path($retrace->pdf_path);
            return response()->download($path, 'retrace_' . $retrace->tracking_code . '.pdf');
        })->name('download');

    });

    // Admin
    Route::prefix('admin')->middleware('is.admin')->group(function () {
        Route::get('/dashboard', [\App\Http\Controllers\AdminController::class, 'index']);
        Route::get('/users', [\App\Http\Controllers\UserManagementController::class, 'index']);
        Route::post('/users/{id}/promote', [\App\Http\Controllers\UserManagementController::class, 'promote']);
        Route::post('/users/{id}/demote', [\App\Http\Controllers\UserManagementController::class, 'demote']);
        Route::get('/audit', [\App\Http\Controllers\AuditController::class, 'index']);
    });
});

// Routes publiques de vérification (accessibles sans authentification)
Route::post('/verification/download-document', function (\Illuminate\Http\Request $request) {
    $request->validate([
        'tracking_code' => ['required','string','max:20','regex:/^SS(-PR)?-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
    ]);

    $trackingCode = strtoupper(trim($request->tracking_code));
    
    // Chercher d'abord un document signé
    $document = \App\Models\Document::where('tracking_code', $trackingCode)
        ->where('status', 'signed')
        ->first();

    if ($document && $document->signed_file_path && \Illuminate\Support\Facades\Storage::disk('private')->exists($document->signed_file_path)) {
        $path = \Illuminate\Support\Facades\Storage::disk('private')->path($document->signed_file_path);
        $content = file_get_contents($path);
        
        return response($content, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $document->original_filename . '"',
            'X-Document-Tracking-Code' => $document->tracking_code,
        ]);
    }

    return response()->json([
        'message' => 'Document non trouvé ou non disponible.',
    ], 404);
})->name('download-document');

// Variante GET pour téléchargement de document authentique (évite CSRF sur POST)
Route::get('/verification/download-document', function (\Illuminate\Http\Request $request) {
    $request->validate([
        'tracking_code' => ['required','string','max:20','regex:/^SS(-PR)?-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
    ]);

    $trackingCode = strtoupper(trim($request->query('tracking_code')));
    $document = \App\Models\Document::where('tracking_code', $trackingCode)
        ->where('status', 'signed')
        ->first();

    if ($document && $document->signed_file_path && \Illuminate\Support\Facades\Storage::disk('private')->exists($document->signed_file_path)) {
        $path = \Illuminate\Support\Facades\Storage::disk('private')->path($document->signed_file_path);
        $content = file_get_contents($path);
        return response($content, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $document->original_filename . '"',
            'X-Document-Tracking-Code' => $document->tracking_code,
        ]);
    }

    return response()->json([
        'message' => 'Document non trouvé ou non disponible.',
    ], 404);
})->name('download-document.get');

// Route pour vérifier un document ou retrace
Route::post('/verification/verify-document', function (\Illuminate\Http\Request $request) {
    $request->validate([
        'tracking_code' => ['required','string','max:20','regex:/^SS(-PR)?-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
    ], [
        'tracking_code.required' => 'Le code de suivi est obligatoire.',
        'tracking_code.regex' => 'Format de code de suivi invalide. Exemple: SS-ABCD-1234 ou SS-PR-ABCD-1234',
    ]);

    $trackingCode = strtoupper($request->tracking_code);

    // Rechercher d'abord un document signé avec ce code
    $document = \App\Models\Document::where('tracking_code', $trackingCode)
        ->where('status', 'signed')
        ->first();

    // Ajouter une vérification de debug
    if ($document) {
        Log::info('Document trouvé', [
            'type' => gettype($document),
            'class' => get_class($document),
            'attributes' => $document->getAttributes()
        ]);
    } else {
        Log::info('Aucun document trouvé pour le code: ' . $trackingCode);
    }

    // Si aucun document, chercher un retrace physique
    if (!$document) {
        $retrace = \App\Models\PhysicalRetrace::where('tracking_code', $trackingCode)->first();
        if ($retrace) {
            // Log succès vérification retrace
            try {
                \App\Models\VerificationLog::create([
                    'tracking_code' => $retrace->tracking_code,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'verification_result' => 'success',
                    'verification_details' => [ 'type' => 'physical_retrace' ],
                    'verified_at' => now(),
                ]);
            } catch (\Throwable $e) {}
            return response()->json([
                'success' => true,
                'message' => 'Code de suivi trouvé (retrace physique).',
                'verified' => true,
                'type' => 'physical_retrace',
                'data' => [
                    'tracking_code' => $retrace->tracking_code,
                    'date' => $retrace->date?->toDateString(),
                    'reference' => $retrace->reference,
                    'subject' => $retrace->subject,
                    'notes' => $retrace->notes,
                    'pdf_available' => !empty($retrace->pdf_path),
                ],
            ], 200);
        }

        // Log échec vérification
        try {
            \App\Models\VerificationLog::create([
                'tracking_code' => $trackingCode,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'verification_result' => 'failed',
                'verification_details' => [ 'reason' => 'not_found' ],
                'verified_at' => now(),
            ]);
        } catch (\Throwable $e) {}
        return response()->json([
            'success' => false,
            'message' => 'Aucun élément trouvé avec ce code de suivi.',
            'verified' => false,
            'data' => null,
        ], 404);
    }

    // Vérifier que le document signé existe toujours
    if (!$document->signed_file_path || !\Illuminate\Support\Facades\Storage::disk('private')->exists($document->signed_file_path)) {
        return response()->json([
            'success' => false,
            'message' => 'Le document signé n\'est plus disponible dans le système.',
            'verified' => false,
            'data' => null,
        ], 404);
    }

    // Récupérer les informations de signature
    $signature = $document->signatures()->latest()->first();

    // Log succès vérification document
    try {
        \App\Models\VerificationLog::create([
            'tracking_code' => $document->tracking_code,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'verification_result' => 'success',
            'verification_details' => [ 'type' => 'document' ],
            'verified_at' => now(),
        ]);
    } catch (\Throwable $e) {}
    return response()->json([
        'success' => true,
        'message' => 'Document authentique vérifié avec succès.',
        'verified' => true,
        'data' => [
            'document' => [
                'id' => $document->id,
                'original_filename' => $document->original_filename,
                'tracking_code' => $document->tracking_code,
                'file_size' => $document->file_size,
                'pages_count' => $document->pages_count,
                'uploaded_at' => $document->created_at->toISOString(),
                'signed_at' => $document->signed_at->toISOString(),
            ],
            'signer' => [
                'name' => $document->user->name,
                'email' => $document->user->email,
            ],
            'signature' => $signature ? [
                'position_x' => $signature->position_x,
                'position_y' => $signature->position_y,
                'page_number' => $signature->page_number,
            ] : null,
        ],
    ], 200);
})->name('verify-document');

// Route pour générer un rapport de vérification
Route::post('/verification/generate-report', function (\Illuminate\Http\Request $request) {
    $request->validate([
        'tracking_code' => ['required','string','max:20','regex:/^SS(-PR)?-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
    ]);

    $trackingCode = strtoupper($request->tracking_code);
    
    // Chercher d'abord un document signé
    $document = \App\Models\Document::where('tracking_code', $trackingCode)
        ->where('status', 'signed')
        ->first();

    if ($document) {
        $pdf = app(\App\Services\PDFService::class);
        $signature = $document->signatures()->latest()->first();
        
        $sections = [
            'Document' => [
                'Nom' => $document->original_filename,
                'Code de suivi' => $document->tracking_code,
                'Pages' => $document->pages_count,
                'Taille' => number_format($document->file_size / 1024, 2) . ' KB',
                'Date d\'upload' => $document->created_at->format('d/m/Y H:i'),
                'Date de signature' => $document->signed_at?->format('d/m/Y H:i'),
            ],
            'Signataire' => [
                'Nom' => $document->user->name,
                'Email' => $document->user->email,
            ],
            'Signature' => $signature ? [
                'Position X' => $signature->position_x . ' mm',
                'Position Y' => $signature->position_y . ' mm',
                'Page' => $signature->page_number,
            ] : ['Aucune signature trouvée' => ''],
            'Résultat de vérification' => [
                'Statut' => 'verified',
                'Authenticité' => 'confirmée',
                'Intégrité' => 'vérifiée',
                'Méthode' => 'digital_signature',
            ],
        ];
        
        $path = $pdf->generateVerificationReportPdf('Rapport de vérification - Document', $sections);
        $full = \Illuminate\Support\Facades\Storage::disk('private')->path($path);
        $content = file_get_contents($full);
        
        return response($content, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="verification_report_' . $document->tracking_code . '.pdf"',
        ]);
    }

    // Chercher un retrace physique
    $retrace = \App\Models\PhysicalRetrace::where('tracking_code', $trackingCode)->first();
    if ($retrace) {
        $pdf = app(\App\Services\PDFService::class);
        $sections = [
            'Informations retrace' => [
                'Code de suivi' => $retrace->tracking_code,
                'Date' => $retrace->date?->toDateString(),
                'Référence' => $retrace->reference,
                'Objet' => $retrace->subject,
                'Notes' => $retrace->notes,
            ],
            'Résultat de vérification' => [
                'Statut' => 'verified',
                'Authenticité' => 'confirmée',
                'Intégrité' => 'n/a',
                'Méthode' => 'tracking_code',
            ],
        ];
        $path = $pdf->generateVerificationReportPdf('Rapport de vérification - Retrace', $sections);
        $full = \Illuminate\Support\Facades\Storage::disk('private')->path($path);
        $content = file_get_contents($full);
        
        return response($content, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="verification_report_' . $retrace->tracking_code . '.pdf"',
        ]);
    }

    return response()->json([
        'message' => 'Aucun document ou retrace trouvé avec ce code de suivi.',
    ], 404);
})->name('generate-report');

// Pages publiques de vérification
Route::prefix('verify')->name('verify.')->group(function () {
    Route::view('/', 'verify.index')->name('index');
    Route::view('/{trackingCode}', 'verify.show')->name('show');
});

// Route publique pour télécharger un retrace par code de suivi
Route::post('/retrace/download-by-code', function (\Illuminate\Http\Request $request) {
    $request->validate([
        'tracking_code' => ['required','string','max:20','regex:/^SS-PR-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
    ]);

    $trackingCode = strtoupper($request->tracking_code);
    $retrace = \App\Models\PhysicalRetrace::where('tracking_code', $trackingCode)->first();

    if (!$retrace) {
        return response()->json([
            'message' => 'Aucun retrace trouvé avec ce code de suivi.',
        ], 404);
    }

    // Assurer la présence du PDF; régénérer si manquant
    if (!$retrace->pdf_path || !\Illuminate\Support\Facades\Storage::disk('private')->exists($retrace->pdf_path)) {
        try {
            $pdfService = app(\App\Services\PDFService::class);
            $newPath = $pdfService->generateBlankWithTrackingCodeOnly($retrace->tracking_code);
            $retrace->update(['pdf_path' => $newPath]);
        } catch (\Throwable $e) {
            return response()->json([
                'message' => 'Le PDF de retrace n\'est plus disponible et n\'a pas pu être régénéré.',
            ], 404);
        }
    }

    $path = \Illuminate\Support\Facades\Storage::disk('private')->path($retrace->pdf_path);
    $content = file_get_contents($path);
    
    return response($content, 200, [
        'Content-Type' => 'application/pdf',
        'Content-Disposition' => 'attachment; filename="retrace_' . $retrace->tracking_code . '.pdf"',
        'X-Retrace-Tracking-Code' => $retrace->tracking_code,
    ]);
})->name('download-by-code');

// Variante GET pour téléchargement de retrace par code (évite CSRF sur POST)
Route::get('/retrace/download-by-code', function (\Illuminate\Http\Request $request) {
    $request->validate([
        'tracking_code' => ['required','string','max:20','regex:/^SS-PR-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
    ]);

    $trackingCode = strtoupper($request->query('tracking_code'));
    $retrace = \App\Models\PhysicalRetrace::where('tracking_code', $trackingCode)->first();

    if (!$retrace) {
        return response()->json([
            'message' => 'Aucun retrace trouvé avec ce code de suivi.',
        ], 404);
    }

    // Assurer la présence du PDF; régénérer si manquant
    if (!$retrace->pdf_path || !\Illuminate\Support\Facades\Storage::disk('private')->exists($retrace->pdf_path)) {
        try {
            $pdfService = app(\App\Services\PDFService::class);
            $newPath = $pdfService->generateBlankWithTrackingCodeOnly($retrace->tracking_code);
            $retrace->update(['pdf_path' => $newPath]);
        } catch (\Throwable $e) {
            return response()->json([
                'message' => 'Le PDF de retrace n\'est plus disponible et n\'a pas pu être régénéré.',
            ], 404);
        }
    }

    $path = \Illuminate\Support\Facades\Storage::disk('private')->path($retrace->pdf_path);
    $content = file_get_contents($path);
    return response($content, 200, [
        'Content-Type' => 'application/pdf',
        'Content-Disposition' => 'attachment; filename="retrace_' . $retrace->tracking_code . '.pdf"',
        'X-Retrace-Tracking-Code' => $retrace->tracking_code,
    ]);
})->name('download-by-code.get');

// Variante GET avec paramètre chemin (plus robuste)
Route::get('/retrace/download/{trackingCode}', function (string $trackingCode) {
    $trackingCode = strtoupper(trim($trackingCode));
    if (!preg_match('/^SS-PR-[A-Z0-9]{4}-[A-Z0-9]{4}$/', $trackingCode)) {
        abort(404);
    }

    $retrace = \App\Models\PhysicalRetrace::where('tracking_code', $trackingCode)->first();
    if (!$retrace) {
        abort(404);
    }

    if (!$retrace->pdf_path || !\Illuminate\Support\Facades\Storage::disk('private')->exists($retrace->pdf_path)) {
        try {
            $pdfService = app(\App\Services\PDFService::class);
            $newPath = $pdfService->generateBlankWithTrackingCodeOnly($retrace->tracking_code);
            $retrace->update(['pdf_path' => $newPath]);
        } catch (\Throwable $e) {
            abort(404);
        }
    }

    $path = \Illuminate\Support\Facades\Storage::disk('private')->path($retrace->pdf_path);
    $content = file_get_contents($path);
    return response($content, 200, [
        'Content-Type' => 'application/pdf',
        'Content-Disposition' => 'attachment; filename="retrace_' . $retrace->tracking_code . '.pdf"',
        'X-Retrace-Tracking-Code' => $retrace->tracking_code,
    ]);
})->name('download-by-code.path');








