@extends('layouts.app')

@section('title', 'Admin - Dashboard')

@section('content')
<div class="container py-4">
    <h3 class="mb-4">Tableau de bord Admin</h3>
    <div class="row g-3">
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <i class="fas fa-users fa-2x text-primary me-3"></i>
                    <div>
                        <div class="h4 mb-0">{{ $totalUsers }}</div>
                        <small class="text-muted">Utilisateurs</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <i class="fas fa-user-check fa-2x text-success me-3"></i>
                    <div>
                        <div class="h4 mb-0">{{ $activeLast7 }}</div>
                        <small class="text-muted">Actifs 7j</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <i class="fas fa-file-signature fa-2x text-info me-3"></i>
                    <div>
                        <div class="h4 mb-0">{{ $totalSignatures }}</div>
                        <small class="text-muted">Signatures</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <i class="fas fa-clipboard-list fa-2x text-warning me-3"></i>
                    <div>
                        <div class="h4 mb-0">{{ $auditCount }}</div>
                        <small class="text-muted">Journaux</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection


