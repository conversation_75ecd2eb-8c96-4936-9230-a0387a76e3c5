<?php

namespace App\Policies;

use App\Models\Document;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class DocumentPolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Document $document): Response
    {
        return $user->id === $document->user_id
            ? Response::allow()
            : Response::deny("Vous n'êtes pas autorisé à voir ce document.");
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, Document $document): Response
    {
        return $user->id === $document->user_id
            ? Response::allow()
            : Response::deny("Vous n'êtes pas autorisé à modifier ce document.");
    }

    public function delete(User $user, Document $document): Response
    {
        return $user->id === $document->user_id
            ? Response::allow()
            : Response::deny("Vous n'êtes pas autorisé à supprimer ce document.");
    }

    public function restore(User $user, Document $document): bool
    {
        return $user->id === $document->user_id;
    }

    public function forceDelete(User $user, Document $document): bool
    {
        return $user->id === $document->user_id;
    }

    public function sign(User $user, Document $document): Response
    {
        return $user->id === $document->user_id && $document->status === 'uploaded'
            ? Response::allow()
            : Response::deny("Vous n'êtes pas autorisé à signer ce document ou il est déjà signé.");
    }

    public function download(User $user, Document $document): Response
    {
        return $user->id === $document->user_id && $document->status === 'signed'
            ? Response::allow()
            : Response::deny("Vous n'êtes pas autorisé à télécharger ce document ou il n'est pas encore signé.");
    }
}
