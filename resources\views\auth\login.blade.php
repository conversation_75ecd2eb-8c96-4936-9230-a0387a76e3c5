@extends('layouts.app')

@section('title', 'Connexion - SignSecure')

@section('content')
<div class="card mx-auto mt-5 p-4" style="max-width: 400px;">
    <h3 class="text-center mb-4">Connexion</h3>

    <form id="loginForm" class="needs-validation" novalidate>
        <div class="mb-3">
            <label for="email" class="form-label">Adresse e-mail</label>
            <input type="email" id="email" name="email" class="form-control" placeholder="<EMAIL>" required>
            <div class="invalid-feedback">Veuillez entrer votre e-mail.</div>
        </div>

        <div class="mb-3">
            <label for="password" class="form-label">Mot de passe</label>
            <div class="input-group">
                <input type="password" id="password" name="password" class="form-control" placeholder="Votre mot de passe" required>
                <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                    <i class="fas fa-eye" id="togglePasswordIcon"></i>
                </button>
                <div class="invalid-feedback">Veuillez entrer votre mot de passe.</div>
            </div>
        </div>

        <button type="submit" id="loginBtn" class="btn btn-primary w-100">
            Se connecter
        </button>
    </form>
    
    <div class="text-center mt-3">
        <p class="mb-2"><a href="{{ route('password.request') }}" class="text-decoration-none">Mot de passe oublié ?</a></p>
        <p class="mb-0">Vous n'avez pas de compte ? <a href="{{ route('register') }}" class="text-decoration-none">S'inscrire</a></p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('togglePasswordIcon');

    // Toggle password visibility
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.type === 'password' ? 'text' : 'password';
        passwordInput.type = type;
        toggleIcon.classList.toggle('fa-eye-slash');
        toggleIcon.classList.toggle('fa-eye');
    });

    // Form submission
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        if (!loginForm.checkValidity()) {
            loginForm.classList.add('was-validated');
            return;
        }

        // Nettoyer les erreurs précédentes
        SignSecureUI.displayValidationErrors({});

        loginBtn.disabled = true;
        const originalText = loginBtn.innerHTML;
        SignSecureUI.showLoader(loginBtn);

        const email = document.getElementById('email').value.trim();
        const password = passwordInput.value;

        try {
            const response = await signSecureAPI.login({ email, password });

            if (!response.success || !response.data?.token) {
                throw new Error(response.message || "Réponse API invalide");
            }

            // Stocker le token temporairement
            const token = response.data.token;
            
            // Créer une session web via une requête POST classique
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/auth/create-session';
            form.style.display = 'none';
            
            const tokenInput = document.createElement('input');
            tokenInput.name = 'api_token';
            tokenInput.value = token;
            form.appendChild(tokenInput);
            
            const csrfInput = document.createElement('input');
            csrfInput.name = '_token';
            csrfInput.value = '{{ csrf_token() }}';
            form.appendChild(csrfInput);
            
            document.body.appendChild(form);
            form.submit();

        } catch (error) {
            console.error('Erreur de connexion:', error);
            
            if (error.data && error.data.errors) {
                // Erreurs de validation
                SignSecureUI.displayValidationErrors(error.data.errors);
            } else {
                // Erreur générale
                SignSecureUI.showToast(error.message || "Erreur de connexion", "error");
            }
        } finally {
            SignSecureUI.hideLoader(loginBtn, originalText);
        }
    });
});
</script>
@endsection






