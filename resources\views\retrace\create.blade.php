@extends('layouts.app')

@section('content')
<div class="container mx-auto max-w-3xl">
    <!-- Header avec icône -->
    <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
            <i class="fas fa-file-signature text-white text-2xl"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Retracer un document physique</h1>
        <p class="text-gray-600 text-lg">Créez un suivi numérique pour vos documents physiques signés</p>
    </div>

    <!-- Formulaire dans une carte -->
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
        <form method="POST" action="{{ route('retrace.request-otp') }}" class="space-y-6">
            @csrf
            
            <!-- Date et Référence sur la même ligne -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                    <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">
                        <i class="fas fa-calendar-alt text-blue-500 mr-2"></i>Date du document
                    </label>
                    <input type="date" name="date" 
                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200" 
                           required />
                </div>
                
                <div class="space-y-2">
                    <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">
                        <i class="fas fa-hashtag text-green-500 mr-2"></i>Référence
                    </label>
                    <input type="text" name="reference" placeholder="Ex: REF-2024-001"
                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200" 
                           required />
                </div>
            </div>

            <!-- Objet -->
            <div class="space-y-2">
                <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">
                    <i class="fas fa-file-alt text-purple-500 mr-2"></i>Objet du document
                </label>
                <input type="text" name="subject" placeholder="Ex: Contrat de location commerciale"
                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200" 
                       required />
            </div>

            <!-- Notes -->
            <div class="space-y-2">
                <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">
                    <i class="fas fa-sticky-note text-orange-500 mr-2"></i>Notes additionnelles
                </label>
                <textarea name="notes" rows="4" placeholder="Informations complémentaires, détails, etc."
                          class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 resize-none"></textarea>
            </div>

            <!-- Méthode OTP -->
            <div class="space-y-2">
                <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">
                    <i class="fas fa-shield-alt text-green-500 mr-2"></i>Méthode de vérification
                </label>
                <div class="flex items-center gap-6">
                    <label class="inline-flex items-center gap-2">
                        <input type="radio" name="otp_method" value="email" class="form-check-input" checked>
                        <span>Email</span>
                    </label>
                    @if(Auth::user() && Auth::user()->phone)
                    <label class="inline-flex items-center gap-2">
                        <input type="radio" name="otp_method" value="sms" class="form-check-input">
                        <span>SMS ({{ Auth::user()->phone }})</span>
                    </label>
                    @endif
                </div>
            </div>

            <!-- Bouton de soumission -->
            <div class="pt-4">
                <button type="submit" 
                        class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-4 px-6 rounded-xl hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Demander le code de vérification
                </button>
            </div>

            <!-- Info sur le processus -->
            <div class="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                    <div class="text-sm text-blue-700">
                        <p class="font-medium mb-1">Comment ça marche ?</p>
                        <p>Après soumission, un code de vérification sera envoyé à votre email. Une fois validé, un PDF sera généré avec un code de suivi unique pour tracer votre document physique.</p>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection


