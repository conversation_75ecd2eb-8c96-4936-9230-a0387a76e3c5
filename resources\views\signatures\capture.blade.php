@extends('layouts.app')

@section('title', 'Capture de signature - SignSecure')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-pen-nib text-primary" style="font-size: 3rem;"></i>
                        </div>
                        <h2 class="h4 mb-2">Créez votre signature</h2>
                        <p class="text-muted">
                            Dessinez votre signature ci-dessous. Elle sera utilisée pour signer vos documents.
                        </p>
                    </div>

                    <!-- Document Info -->
                    <div class="bg-light rounded-3 p-3 mb-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-file-pdf text-danger fs-2"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">{{ $document->original_filename }}</h6>
                                <small class="text-muted">
                                    Prêt à être signé
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Signature Pad -->
                    <div class="mb-4">
                        <label class="form-label fw-semibold mb-3">
                            <i class="fas fa-signature text-primary me-2"></i>
                            Votre signature
                        </label>
                        
                        <div class="signature-pad-container border rounded-3 p-3 bg-white">
                            <canvas id="signaturePad" class="signature-pad w-100" style="height: 200px; border: 2px dashed #dee2e6; border-radius: 0.5rem;"></canvas>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="clearSignatureBtn">
                                <i class="fas fa-eraser me-1"></i>
                                Effacer
                            </button>
                            <small class="text-muted">
                                Utilisez votre souris ou votre doigt sur mobile
                            </small>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <i class="fas fa-info-circle me-2 mt-1"></i>
                            <div>
                                <strong>Conseils pour une bonne signature :</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Dessinez votre signature comme vous le feriez sur papier</li>
                                    <li>Assurez-vous qu'elle soit lisible et cohérente</li>
                                    <li>Vous pourrez la modifier plus tard dans vos paramètres</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-lg" id="saveSignatureBtn" disabled>
                            <i class="fas fa-save me-2"></i>
                            Enregistrer ma signature et continuer
                        </button>
                        <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Retour aux documents
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const signaturePadCanvas = document.getElementById('signaturePad');
    const clearSignatureBtn = document.getElementById('clearSignatureBtn');
    const saveSignatureBtn = document.getElementById('saveSignatureBtn');
    const documentId = {{ $document->id }};

    let signaturePad = new SignaturePad(signaturePadCanvas, {
        backgroundColor: 'rgb(255, 255, 255)',
        penColor: 'rgb(0, 0, 0)',
        minWidth: 1,
        maxWidth: 2.5,
    });

    // Redimensionner le canvas
    function resizeCanvas() {
        const ratio = Math.max(window.devicePixelRatio || 1, 1);
        const canvas = signaturePadCanvas;
        const rect = canvas.getBoundingClientRect();
        
        canvas.width = rect.width * ratio;
        canvas.height = rect.height * ratio;
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';
        
        const ctx = canvas.getContext('2d');
        ctx.scale(ratio, ratio);
        
        signaturePad.clear();
    }

    // Initialiser le canvas
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Effacer la signature
    clearSignatureBtn.addEventListener('click', function() {
        signaturePad.clear();
        updateSaveButton();
    });

    // Mettre à jour le bouton de sauvegarde
    function updateSaveButton() {
        saveSignatureBtn.disabled = signaturePad.isEmpty();
    }

    // Écouter les événements de dessin
    signaturePad.addEventListener('beginStroke', updateSaveButton);
    signaturePad.addEventListener('endStroke', updateSaveButton);

    // Sauvegarder la signature
    saveSignatureBtn.addEventListener('click', async function() {
        if (signaturePad.isEmpty()) {
            toastr.error('Veuillez dessiner votre signature d\'abord.');
            return;
        }

        const originalText = saveSignatureBtn.innerHTML;
        saveSignatureBtn.disabled = true;
        saveSignatureBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...';

        try {
            const signatureData = signaturePad.toDataURL();
            
            const response = await fetch(`/signatures/${documentId}/capture`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    signature_data: signatureData
                })
            });

            const result = await response.json();

            if (result.success) {
                toastr.success(result.message);
                setTimeout(() => {
                    window.location.href = result.redirect_url;
                }, 1000);
            } else {
                throw new Error(result.message || 'Erreur lors de l\'enregistrement');
            }

        } catch (error) {
            console.error('Erreur:', error);
            toastr.error(error.message || 'Erreur lors de l\'enregistrement de la signature');
        } finally {
            saveSignatureBtn.disabled = false;
            saveSignatureBtn.innerHTML = originalText;
        }
    });

    // Initialiser le bouton
    updateSaveButton();
});
</script>
@endpush

@push('styles')
<style>
.signature-pad {
    touch-action: none;
    cursor: crosshair;
}

.signature-pad-container {
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

@media (max-width: 768px) {
    .signature-pad {
        height: 150px !important;
    }
    
    .card-body {
        padding: 2rem !important;
    }
}
</style>
@endpush
@endsection

