<?php

/**
 * Script de test automatique pour l'endpoint web de signature
 * Ce script teste le endpoint /signatures/{document}/apply
 */

// Configuration
$baseUrl = 'http://localhost/signsecure/public';
$documentId = 1; // Remplacez par un vrai ID de document

echo "🧪 Test automatique de l'endpoint web de signature\n";
echo "================================================\n\n";

// Test 1: Format correct
echo "=== Test 1: Format correct ===\n";
$data1 = [
    'position_x' => 100.0,
    'position_y' => 200.0,
    'page_number' => 1
];

echo "Données envoyées: " . json_encode($data1, JSON_PRETTY_PRINT) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . "/signatures/{$documentId}/apply");
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data1));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Réponse: $response\n\n";

// Test 2: Format incorrect (positions array)
echo "=== Test 2: Format incorrect (positions array) ===\n";
$data2 = [
    'positions' => [
        [
            'x' => 100.0,
            'y' => 200.0,
            'page' => 1
        ]
    ]
];

echo "Données envoyées: " . json_encode($data2, JSON_PRETTY_PRINT) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . "/signatures/{$documentId}/apply");
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data2));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Réponse: $response\n\n";

// Test 3: Champs manquants
echo "=== Test 3: Champs manquants ===\n";
$data3 = [
    'position_x' => 100.0
    // position_y et page_number manquent
];

echo "Données envoyées: " . json_encode($data3, JSON_PRETTY_PRINT) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . "/signatures/{$documentId}/apply");
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data3));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Réponse: $response\n\n";

echo "✅ Tests terminés !\n\n";

echo "📝 Analyse des résultats:\n";
echo "- Test 1 (Format correct): Doit retourner HTTP 200 ou 422 (validation CSRF)\n";
echo "- Test 2 (Format incorrect): Doit retourner HTTP 422 avec erreur de validation\n";
echo "- Test 3 (Champs manquants): Doit retourner HTTP 422 avec erreur de validation\n\n";

echo "🔍 Si vous obtenez des erreurs CSRF, c'est normal en test direct.\n";
echo "Le problème principal était le format des données, maintenant corrigé !\n";
