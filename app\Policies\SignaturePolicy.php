<?php

namespace App\Policies;

use App\Models\Signature;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SignaturePolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Signature $signature): Response
    {
        return $user->id === $signature->user_id
            ? Response::allow()
            : Response::deny("Vous n'êtes pas autorisé à voir cette signature.");
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, Signature $signature): Response
    {
        return $user->id === $signature->user_id
            ? Response::allow()
            : Response::deny("Vous n'êtes pas autorisé à modifier cette signature.");
    }

    public function delete(User $user, Signature $signature): Response
    {
        return $user->id === $signature->user_id
            ? Response::allow()
            : Response::deny("Vous n'êtes pas autorisé à supprimer cette signature.");
    }

    public function restore(User $user, Signature $signature): bool
    {
        return $user->id === $signature->user_id;
    }

    public function forceDelete(User $user, Signature $signature): bool
    {
        return $user->id === $signature->user_id;
    }

    public function apply(User $user): Response
    {
        return $user->hasSignature()
            ? Response::allow()
            : Response::deny("Vous devez d'abord créer une signature avant de pouvoir l'appliquer.");
    }
}
