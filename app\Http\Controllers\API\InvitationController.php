<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\DocumentSigner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\Rule;

class InvitationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Inviter un signataire sur un document
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'document_id' => ['required', 'integer', 'exists:documents,id'],
            'email' => ['required', 'email', 'max:255'],
            'name' => ['nullable', 'string', 'max:255'],
            'signature_order' => ['nullable', 'integer', 'min:1'],
        ]);

        $document = Document::findOrFail($validated['document_id']);
        $this->authorize('update', $document);

        // Ne pas inviter si déjà signé ou signé en cours
        if ($document->status === 'signed') {
            return response()->json([
                'success' => false,
                'message' => 'Le document est déjà signé.',
            ], 422);
        }

        // Vérifier doublon d'invitation
        $exists = DocumentSigner::where('document_id', $document->id)
            ->where(function ($q) use ($validated) {
                $q->where('email', $validated['email']);
            })
            ->whereNull('signed_at')
            ->exists();
        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Une invitation en attente existe déjà pour cet email.',
            ], 422);
        }

        $signer = DocumentSigner::create([
            'document_id' => $document->id,
            'user_id' => null,
            'email' => $validated['email'],
            'name' => $validated['name'] ?? null,
            'status' => 'pending',
            'signature_order' => $validated['signature_order'] ?? (int)($document->signers()->max('signature_order') + 1),
        ]);

        // TODO: envoyer un email d'invitation avec lien (si logique dispo)
        try {
            if (class_exists(\App\Mail\SignatureTokenMail::class)) {
                // Mail::to($signer->email)->send(new \App\Mail\SignatureTokenMail(/* payload */));
            }
        } catch (\Throwable $e) {
            // On log mais on n'échoue pas l'invitation
            \Log::warning('Invitation mail non envoyée', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Invitation envoyée avec succès.',
            'data' => [
                'signer_id' => $signer->id,
                'email' => $signer->email,
                'name' => $signer->name,
                'signature_order' => $signer->signature_order,
                'status' => $signer->status,
            ],
        ], 201);
    }

    /**
     * Liste des invitations que l'utilisateur a reçues et qui sont en attente
     */
    public function forMe(Request $request): JsonResponse
    {
        $user = $request->user();
        $invitations = DocumentSigner::with(['document:id,original_filename,tracking_code'])
            ->where(function ($q) use ($user) {
                $q->where('user_id', $user->id)
                  ->orWhere('email', $user->email);
            })
            ->where('status', 'pending')
            ->whereNull('signed_at')
            ->latest()
            ->limit(10)
            ->get()
            ->map(function (DocumentSigner $s) {
                return [
                    'id' => $s->id,
                    'email' => $s->email,
                    'name' => $s->name,
                    'document' => [
                        'id' => $s->document->id,
                        'filename' => $s->document->original_filename,
                        'tracking_code' => $s->document->tracking_code,
                    ],
                    'expires_at' => $s->expires_at?->toISOString(),
                    'signature_order' => $s->signature_order,
                ];
            });

        return response()->json([
            'success' => true,
            'message' => 'Invitations récupérées avec succès',
            'data' => $invitations,
        ], 200);
    }
}


