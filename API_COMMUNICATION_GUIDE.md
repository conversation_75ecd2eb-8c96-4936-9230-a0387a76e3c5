# Guide de Communication API SignSecure

## 🚀 Vue d'ensemble

Ce guide explique comment utiliser la communication API améliorée entre le frontend et le backend Laravel de SignSecure.

## ✨ Améliorations apportées

### 1. **Structure de réponse standardisée**
Toutes les réponses API suivent maintenant un format cohérent :
```json
{
    "success": true/false,
    "message": "Message descriptif",
    "data": { ... },
    "error": "Détails de l'erreur (si applicable)"
}
```

### 2. **Gestion d'erreurs améliorée**
- Validation des données côté client et serveur
- Messages d'erreur détaillés et localisés
- Gestion des erreurs réseau et serveur

### 3. **Authentification robuste**
- Tokens Laravel Sanctum
- Gestion automatique des tokens expirés
- Protection CSRF

### 4. **Interface utilisateur cohérente**
- Toasts de notification
- Indicateurs de chargement
- Validation en temps réel

## 🔧 Configuration requise

### Backend (Laravel)
```bash
# Installer les dépendances
composer install

# Configurer la base de données
php artisan migrate

# Démarrer le serveur
php artisan serve
```

### Frontend
```bash
# Installer les dépendances (si applicable)
npm install

# Compiler les assets (si applicable)
npm run dev
```

## 📡 Utilisation de l'API

### Initialisation
L'API est automatiquement initialisée dans toutes les pages via le fichier `api.js`.

### Authentification
```javascript
// Connexion
const response = await signSecureAPI.login(email, password);
if (response.success) {
    // Connexion réussie
    console.log('Token:', response.data.token);
}

// Inscription
const response = await signSecureAPI.register({
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'password123',
    password_confirmation: 'password123'
});
```

### Gestion des documents
```javascript
// Téléverser un document
const response = await signSecureAPI.uploadDocument(file, 'email');

// Récupérer les documents
const documents = await signSecureAPI.getDocuments();

// Télécharger un document
const blob = await signSecureAPI.downloadDocument(documentId);
```

### Gestion des OTP
```javascript
// Générer un OTP
const response = await signSecureAPI.generateOTP(documentId, 'email');

// Vérifier un OTP
const response = await signSecureAPI.verifyOTP(documentId, code);
```

### Gestion des signatures
```javascript
// Capturer une signature
const response = await signSecureAPI.captureSignature(signatureData);

// Appliquer une signature
const response = await signSecureAPI.applySignature(
    documentId, 
    positionX, 
    positionY, 
    pageNumber
);
```

## 🧪 Tests et débogage

### Console de test (développement uniquement)
En mode développement, un helper de test est disponible dans la console :

```javascript
// Vérifier l'état de l'API
testAPI.checkAPIStatus();

// Lancer tous les tests
testAPI.runAllTests();

// Tests individuels
testAPI.testLogin();
testAPI.testRegister();
testAPI.testGetUser();
```

### Logs de débogage
```javascript
// Activer les logs détaillés
console.log('Réponse API:', response);
console.error('Erreur API:', error);
```

## 🎨 Interface utilisateur

### Toasts de notification
```javascript
// Utiliser l'utilitaire UI
SignSecureUI.showToast('Message de succès', 'success');
SignSecureUI.showToast('Message d\'erreur', 'error');
SignSecureUI.showToast('Information', 'info');
SignSecureUI.showToast('Avertissement', 'warning');
```

### Indicateurs de chargement
```javascript
// Afficher le loader
SignSecureUI.showLoader(button);

// Masquer le loader
SignSecureUI.hideLoader(button, 'Texte original');
```

### Validation des formulaires
```javascript
// Afficher les erreurs de validation
SignSecureUI.displayValidationErrors(errors);

// Valider un formulaire
const errors = SignSecureForm.validateForm(form);
```

## 🔒 Sécurité

### Protection CSRF
- Tokens CSRF automatiquement inclus dans toutes les requêtes
- Vérification côté serveur

### Authentification
- Tokens Bearer pour l'API
- Expiration automatique des tokens
- Déconnexion sécurisée

### Validation
- Validation côté client et serveur
- Sanitisation des données
- Protection contre les injections

## 🚨 Gestion des erreurs

### Types d'erreurs
1. **Erreurs de validation** : Données invalides
2. **Erreurs d'authentification** : Token expiré ou invalide
3. **Erreurs de permission** : Accès refusé
4. **Erreurs serveur** : Problèmes internes
5. **Erreurs réseau** : Problèmes de connexion

### Gestion des erreurs
```javascript
try {
    const response = await signSecureAPI.someMethod();
    // Traitement du succès
} catch (error) {
    if (error.data && error.data.errors) {
        // Erreurs de validation
        SignSecureUI.displayValidationErrors(error.data.errors);
    } else {
        // Erreur générale
        SignSecureUI.showToast(error.message, 'error');
    }
}
```

## 📱 Responsive et accessibilité

### Support mobile
- Interface adaptée aux petits écrans
- Navigation tactile
- Formulaires optimisés

### Accessibilité
- Messages d'erreur clairs
- Indicateurs visuels
- Navigation au clavier

## 🔧 Personnalisation

### Configuration de l'API
```javascript
// Modifier l'URL de base
signSecureAPI.baseURL = 'https://api.example.com/api/v1';

// Ajouter des headers personnalisés
signSecureAPI.request('/endpoint', {
    headers: {
        'Custom-Header': 'value'
    }
});
```

### Styles personnalisés
```css
/* Personnaliser les toasts */
.toast-success {
    background-color: #28a745;
}

/* Personnaliser les loaders */
.loader {
    border-color: #007bff;
}
```

## 📚 Ressources

### Documentation
- [Laravel Sanctum](https://laravel.com/docs/sanctum)
- [Laravel API Resources](https://laravel.com/docs/eloquent-resources)
- [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)

### Support
- Vérifier les logs Laravel (`storage/logs/laravel.log`)
- Utiliser la console de test en développement
- Vérifier la configuration CORS et Sanctum

## 🎯 Bonnes pratiques

1. **Toujours vérifier `response.success`** avant de traiter les données
2. **Gérer les erreurs** avec try/catch
3. **Utiliser les utilitaires UI** pour une expérience cohérente
4. **Valider les données** côté client et serveur
5. **Tester l'API** en développement avec le helper de test

## 🚀 Déploiement

### Production
- Désactiver le helper de test
- Configurer les domaines CORS
- Utiliser HTTPS
- Configurer l'expiration des tokens

### Environnement
```bash
# Production
APP_ENV=production
APP_DEBUG=false

# Développement
APP_ENV=local
APP_DEBUG=true
```

---

**Note** : Ce guide est en constante évolution. Consultez la documentation officielle pour les dernières mises à jour.
