<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Models\User;

class AssignTestSignature extends Command
{
    protected $signature = 'test:assign-signature {userId}';
    protected $description = 'Assigne une signature PNG de test à un utilisateur';

    public function handle()
    {
        $userId = $this->argument('userId');
        $user = User::find($userId);

        if (!$user) {
            $this->error("Utilisateur non trouvé.");
            return Command::FAILURE;
        }

        // Fichier source dans le stockage privé
        $sourcePath = Storage::disk('private')->path('signatures/signature_test.png');

        if (!file_exists($sourcePath)) {
            $this->error("Le fichier de signature de test est introuvable : $sourcePath");
            return Command::FAILURE;
        }

        // Nouveau chemin pour cet utilisateur
        $destinationPath = 'signatures/signature_' . $user->id . '_test.png';

        // Co<PERSON> le fichier vers un nouveau nom
        Storage::disk('private')->put(
            $destinationPath,
            file_get_contents($sourcePath)
        );

        // Mise à jour de l'utilisateur
        $user->signature_path = $destinationPath;
        $user->signature_image = base64_encode(file_get_contents($sourcePath));
        $user->save();

        $this->info("Signature PNG de test assignée à l'utilisateur #{$user->id}");
        $this->info("Chemin : storage/app/private/$destinationPath");

        return Command::SUCCESS;
    }
}
