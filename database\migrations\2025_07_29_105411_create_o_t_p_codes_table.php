<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("otp_codes", function (Blueprint $table) {
            $table->id();
            $table->foreignId("user_id")->constrained()->onDelete("cascade");
            $table->string("code", 6);
            $table->string("type")->default("email"); // email, sms
            $table->timestamp("expires_at");
            $table->timestamp("used_at")->nullable();
            $table->timestamps();

            // Index pour améliorer les performances
            $table->index(["user_id", "code"]);
            $table->index(["expires_at"]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("otp_codes");
    }
};
