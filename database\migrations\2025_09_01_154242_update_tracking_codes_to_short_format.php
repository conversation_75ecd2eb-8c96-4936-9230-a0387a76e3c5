<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Services\TrackingCodeService;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mettre à jour les codes de suivi des documents existants
        $documents = DB::table('documents')->whereNotNull('tracking_code')->get();
        
        foreach ($documents as $document) {
            // Vérifier si le code actuel est déjà au bon format
            if (!preg_match('/^SS-[A-Z0-9]{4}-[A-Z0-9]{4}$/', $document->tracking_code)) {
                $newCode = TrackingCodeService::generateDocumentCode();
                DB::table('documents')
                    ->where('id', $document->id)
                    ->update(['tracking_code' => $newCode]);
            }
        }

        // Mettre à jour les codes de suivi des retraces physiques existants
        $retraces = DB::table('physical_retraces')->whereNotNull('tracking_code')->get();
        
        foreach ($retraces as $retrace) {
            // Vérifier si le code actuel est déjà au bon format
            if (!preg_match('/^SS-PR-[A-Z0-9]{4}-[A-Z0-9]{4}$/', $retrace->tracking_code)) {
                $newCode = TrackingCodeService::generateRetraceCode();
                DB::table('physical_retraces')
                    ->where('id', $retrace->id)
                    ->update(['tracking_code' => $newCode]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Cette migration ne peut pas être annulée car elle modifie des données
        // Les anciens codes UUID ne peuvent pas être restaurés
    }
};
