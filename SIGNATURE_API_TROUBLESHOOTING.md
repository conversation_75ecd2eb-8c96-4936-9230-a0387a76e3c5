# 🔍 Guide de dépannage - API Signature

## ❌ Erreur : "Le champ de position x est obligatoire. (et 2 autres erreurs)"

### **Problème identifié :**
Lors de l'application d'une signature sur un document, l'API exige **4 champs obligatoires** qui doivent tous être présents dans la requête.

### **Champs requis pour l'endpoint `POST /api/v1/signatures/apply` :**

```json
{
    "document_id": 1,           // ✅ OBLIGATOIRE - ID du document
    "position_x": 100.0,        // ✅ OBLIGATOIRE - Position horizontale (pixels)
    "position_y": 200.0,        // ✅ OBLIGATOIRE - Position verticale (pixels)
    "page_number": 1            // ✅ OBLIGATOIRE - Numéro de page (commence à 1)
}
```

### **Règles de validation :**

| Champ | Type | Contrainte | Description |
|-------|------|------------|-------------|
| `document_id` | integer | `required`, `exists:documents,id` | ID du document existant |
| `position_x` | numeric | `required`, `numeric`, `min:0` | Position X en pixels (≥ 0) |
| `position_y` | numeric | `required`, `numeric`, `min:0` | Position Y en pixels (≥ 0) |
| `page_number` | integer | `required`, `integer`, `min:1` | Numéro de page (≥ 1) |

### **🔧 Solutions :**

#### **1. Vérifiez votre requête côté client**

Assurez-vous que votre frontend envoie **tous** les champs requis :

```javascript
// ❌ INCORRECT - Champs manquants
const data = {
    document_id: 1,
    position_x: 100
    // position_y et page_number manquent !
};

// ✅ CORRECT - Tous les champs présents
const data = {
    document_id: 1,
    position_x: 100.0,
    position_y: 200.0,
    page_number: 1
};
```

#### **2. Vérifiez les types de données**

```javascript
// ❌ INCORRECT - Types invalides
const data = {
    document_id: "1",           // String au lieu d'integer
    position_x: "100",          // String au lieu de numeric
    position_y: "200",          // String au lieu de numeric
    page_number: "1"            // String au lieu d'integer
};

// ✅ CORRECT - Types valides
const data = {
    document_id: parseInt("1"),     // Integer
    position_x: parseFloat("100"),  // Numeric
    position_y: parseFloat("200"),  // Numeric
    page_number: parseInt("1")      // Integer
};
```

#### **3. Vérifiez les valeurs**

```javascript
// ❌ INCORRECT - Valeurs invalides
const data = {
    document_id: 1,
    position_x: -50,           // < 0 (doit être ≥ 0)
    position_y: -100,          // < 0 (doit être ≥ 0)
    page_number: 0             // < 1 (doit être ≥ 1)
};

// ✅ CORRECT - Valeurs valides
const data = {
    document_id: 1,
    position_x: 0,             // ≥ 0
    position_y: 0,             // ≥ 0
    page_number: 1             // ≥ 1
};
```

### **🧪 Tests de débogage :**

#### **A. Utilisez la page de test HTML :**
Ouvrez `test-signature-api.html` dans votre navigateur et testez l'endpoint "Appliquer une signature".

#### **B. Utilisez le script PHP :**
Exécutez `php test-signature-api.php` pour tester tous les endpoints.

#### **C. Testez avec cURL :**

```bash
# ✅ Test correct
curl -X POST http://localhost/signsecure/public/api/v1/signatures/apply \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "document_id": 1,
    "position_x": 100.0,
    "position_y": 200.0,
    "page_number": 1
  }'

# ❌ Test incorrect (champs manquants)
curl -X POST http://localhost/signsecure/public/api/v1/signatures/apply \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "document_id": 1,
    "position_x": 100.0
  }'
```

### **📋 Checklist de vérification :**

- [ ] `document_id` est présent et est un entier valide
- [ ] `position_x` est présent et est un nombre ≥ 0
- [ ] `position_y` est présent et est un nombre ≥ 0
- [ ] `page_number` est présent et est un entier ≥ 1
- [ ] Tous les champs sont envoyés dans le corps de la requête
- [ ] Le Content-Type est `application/json`
- [ ] L'authentification est valide (token Sanctum)

### **🔍 Débogage avancé :**

#### **1. Vérifiez les logs Laravel :**
```bash
tail -f storage/logs/laravel.log
```

#### **2. Activez le mode debug :**
Dans `.env`, vérifiez que `APP_DEBUG=true`

#### **3. Vérifiez la requête reçue :**
Ajoutez temporairement ce code dans le contrôleur :

```php
public function apply(SignatureApplyRequest $request): JsonResponse
{
    // Debug temporaire
    \Log::info('Requête reçue:', $request->all());
    
    $validated = $request->validated();
    // ... reste du code
}
```

### **📱 Exemple complet côté client :**

```javascript
async function applySignature(documentId, x, y, page) {
    try {
        const response = await fetch('/api/v1/signatures/apply', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                document_id: parseInt(documentId),
                position_x: parseFloat(x),
                position_y: parseFloat(y),
                page_number: parseInt(page)
            })
        });

        if (!response.ok) {
            const error = await response.json();
            console.error('Erreur de validation:', error);
            throw new Error(error.message || 'Erreur lors de l\'application de la signature');
        }

        const result = await response.json();
        console.log('Signature appliquée avec succès:', result);
        return result;

    } catch (error) {
        console.error('Erreur:', error);
        throw error;
    }
}

// Utilisation
applySignature(1, 100.0, 200.0, 1)
    .then(result => console.log('Succès:', result))
    .catch(error => console.error('Erreur:', error));
```

### **🚨 Erreurs courantes et solutions :**

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Le champ de position x est obligatoire" | `position_x` manquant | Ajoutez le champ dans la requête |
| "Le champ de position y est obligatoire" | `position_y` manquant | Ajoutez le champ dans la requête |
| "Le champ de page number est obligatoire" | `page_number` manquant | Ajoutez le champ dans la requête |
| "Le champ de document id est obligatoire" | `document_id` manquant | Ajoutez le champ dans la requête |
| "La position x doit être un nombre" | Type incorrect | Utilisez `parseFloat()` ou `Number()` |
| "La position y doit être un nombre" | Type incorrect | Utilisez `parseFloat()` ou `Number()` |
| "Le numéro de page doit être un entier" | Type incorrect | Utilisez `parseInt()` ou `Math.floor()` |

### **✅ Vérification finale :**

Après avoir corrigé votre code, testez avec la page de test HTML pour vérifier que tout fonctionne correctement.

Si le problème persiste, vérifiez :
1. Les logs Laravel pour plus de détails
2. La requête exacte envoyée (via les outils de développement du navigateur)
3. Que tous les champs sont bien présents et du bon type
