<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\DocumentResource;
use App\Models\Document;
use App\Models\PhysicalRetrace;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use App\Services\PDFService;

class VerificationController extends Controller
{
    /**
     * Vérifie l'authenticité d'un document via son code de suivi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyDocument(Request $request): JsonResponse
    {
        $request->validate([
            'tracking_code' => ['required','string','max:20','regex:/^SS(-PR)?-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
        ], [
            'tracking_code.required' => 'Le code de suivi est obligatoire.',
            'tracking_code.regex' => 'Format de code de suivi invalide. Exemple: SS-ABCD-1234 ou SS-PR-ABCD-1234',
        ]);

        $trackingCode = strtoupper($request->tracking_code);

        // Rechercher d'abord un document signé avec ce code
        $document = Document::where('tracking_code', $trackingCode)
            ->where('status', 'signed')
            ->first();

        // Si aucun document, chercher un retrace physique
        if (!$document) {
            $retrace = PhysicalRetrace::where('tracking_code', $trackingCode)->first();
            if ($retrace) {
                return response()->json([
                    'success' => true,
                    'message' => 'Code de suivi trouvé (retrace physique).',
                    'verified' => true,
                    'type' => 'physical_retrace',
                    'data' => [
                        'tracking_code' => $retrace->tracking_code,
                        'date' => $retrace->date?->toDateString(),
                        'reference' => $retrace->reference,
                        'subject' => $retrace->subject,
                        'notes' => $retrace->notes,
                        'pdf_available' => !empty($retrace->pdf_path),
                    ],
                ], 200);
            }

            return response()->json([
                'success' => false,
                'message' => 'Aucun élément trouvé avec ce code de suivi.',
                'verified' => false,
                'data' => null,
            ], 404);
        }

        // Vérifier que le document signé existe toujours
        if (!$document->signed_file_path || !Storage::disk('private')->exists($document->signed_file_path)) {
            return response()->json([
                'success' => false,
                'message' => 'Le document signé n\'est plus disponible dans le système.',
                'verified' => false,
                'data' => null,
            ], 404);
        }

        // Récupérer les informations de signature
        $signature = $document->signatures()->latest()->first();

        return response()->json([
            'success' => true,
            'message' => 'Document authentique vérifié avec succès.',
            'verified' => true,
            'data' => [
                'document' => [
                    'id' => $document->id,
                    'original_filename' => $document->original_filename,
                    'tracking_code' => $document->tracking_code,
                    'file_size' => $document->file_size,
                    'pages_count' => $document->pages_count,
                    'uploaded_at' => $document->created_at->toISOString(),
                    'signed_at' => $document->signed_at->toISOString(),
                ],
                'signer' => [
                    'name' => $document->user->name,
                    'email' => $document->user->email,
                ],
                'signature' => $signature ? [
                    'position_x' => $signature->position_x,
                    'position_y' => $signature->position_y,
                    'page_number' => $signature->page_number,
                    'applied_at' => $signature->applied_at->toISOString(),
                ] : null,
                'verification' => [
                    'verified_at' => now()->toISOString(),
                    'status' => 'authentic',
                    'integrity' => 'intact',
                ],
            ],
        ], 200);
    }

    /**
     * Télécharge le document authentique pour comparaison
     *
     * @param Request $request
     * @return Response|JsonResponse
     */
    public function downloadAuthentic(Request $request)
    {
        $request->validate([
            'tracking_code' => ['required','string','max:20','regex:/^SS(-PR)?-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
        ]);

        $trackingCode = strtoupper($request->tracking_code);

        $document = Document::where('tracking_code', $trackingCode)
            ->where('status', 'signed')
            ->first();

        if (!$document) {
            return response()->json([
                'message' => 'Aucun document signé trouvé avec ce code de suivi.',
            ], 404);
        }

        if (!$document->signed_file_path || !Storage::disk('private')->exists($document->signed_file_path)) {
            return response()->json([
                'message' => 'Le document signé n\'est plus disponible.',
            ], 404);
        }

    $path = Storage::disk('private')->path($document->signed_file_path);

return response()->download(
    $path,
    'authentique_' . $document->original_filename,
    [
        'Content-Type' => 'application/pdf',
        'X-Document-Tracking-Code' => $document->tracking_code,
        'X-Document-Verified-At' => now()->toISOString(),
    ]
);

    }

    /**
     * Télécharge le PDF blanc d'un retrace physique à partir du code de suivi
     */
    public function downloadRetrace(Request $request)
    {
        $request->validate([
            'tracking_code' => ['required','string','max:20','regex:/^SS-PR-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
        ]);

        $trackingCode = strtoupper($request->tracking_code);

        $retrace = PhysicalRetrace::where('tracking_code', $trackingCode)->first();

        if (!$retrace) {
            return response()->json([
                'message' => 'Aucun retrace trouvé avec ce code de suivi.',
            ], 404);
        }

        if (!$retrace->pdf_path || !Storage::disk('private')->exists($retrace->pdf_path)) {
            return response()->json([
                'message' => 'Le PDF de retrace n\'est plus disponible.',
            ], 404);
        }

        $path = Storage::disk('private')->path($retrace->pdf_path);
        $content = file_get_contents($path);
        
        return response($content, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="retrace_' . $retrace->tracking_code . '.pdf"',
            'X-Retrace-Tracking-Code' => $retrace->tracking_code,
        ]);
    }
    /**
     * Génère un rapport de vérification détaillé
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateVerificationReport(Request $request)
    {
        $request->validate([
            'tracking_code' => ['required','string','max:20','regex:/^SS(-PR)?-[A-Z0-9]{4}-[A-Z0-9]{4}$/i'],
        ]);

        $trackingCode = strtoupper($request->tracking_code);

        // Supporte à la fois un document signé et un retrace physique
        $document = Document::where('tracking_code', $trackingCode)
            ->where('status', 'signed')
            ->with(['user', 'signatures'])
            ->first();

        if (!$document) {
            $retrace = PhysicalRetrace::where('tracking_code', $trackingCode)->first();
            if ($retrace) {
                $pdf = app(PDFService::class);
                $sections = [
                    'Informations retrace' => [
                        'Code de suivi' => $retrace->tracking_code,
                        'Date' => $retrace->date?->toDateString(),
                        'Référence' => $retrace->reference,
                        'Objet' => $retrace->subject,
                        'Notes' => $retrace->notes,
                    ],
                    'Résultat de vérification' => [
                        'Statut' => 'verified',
                        'Authenticité' => 'confirmée',
                        'Intégrité' => 'n/a',
                        'Méthode' => 'tracking_code',
                    ],
                ];
                $path = $pdf->generateVerificationReportPdf('Rapport de vérification - Retrace', $sections);
                $full = Storage::disk('private')->path($path);
                return response()->download($full, 'verification_report_'.$retrace->tracking_code.'.pdf', [
                    'Content-Type' => 'application/pdf'
                ]);
            }

            return response()->json([
                'message' => 'Aucun document signé ou retrace trouvé avec ce code de suivi.',
            ], 404);
        }

        $signature = $document->signatures()->latest()->first();
        $pdf = app(PDFService::class);
        $sections = [
            'Document' => [
                'Nom' => $document->original_filename,
                'Code de suivi' => $document->tracking_code,
                'Pages' => $document->pages_count,
                'Taille' => $this->formatBytes($document->file_size),
                'MIME' => $document->mime_type,
            ],
            'Téléversement' => [
                'Date' => $document->created_at->toISOString(),
                'Utilisateur' => $document->user->name . ' <' . $document->user->email . '>',
            ],
            'Signature' => [
                'Signé le' => $document->signed_at->toISOString(),
                'Position' => $signature ? ('page '.$signature->page_number.' (x='.$signature->position_x.', y='.$signature->position_y.')') : 'N/A',
            ],
            'Vérification' => [
                'Statut' => 'verified',
                'Authenticité' => 'confirmée',
                'Intégrité' => 'intacte',
                'Méthode' => 'tracking_code',
            ],
        ];
        $path = $pdf->generateVerificationReportPdf('Rapport de vérification - Document signé', $sections);
        $full = Storage::disk('private')->path($path);
        return response()->download($full, 'verification_report_'.$document->tracking_code.'.pdf', [
            'Content-Type' => 'application/pdf'
        ]);
    }

    /**
     * Recherche des documents par différents critères
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchDocuments(Request $request): JsonResponse
    {
        $request->validate([
            'filename' => 'nullable|string|max:255',
            'signer_email' => 'nullable|email',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = Document::where('status', 'signed')
            ->with(['user:id,name,email']);

        // Filtrer par nom de fichier
        if ($request->filename) {
            $query->where('original_filename', 'LIKE', '%' . $request->filename . '%');
        }

        // Filtrer par email du signataire
        if ($request->signer_email) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('email', $request->signer_email);
            });
        }

        // Filtrer par période
        if ($request->date_from) {
            $query->where('signed_at', '>=', $request->date_from);
        }

        if ($request->date_to) {
            $query->where('signed_at', '<=', $request->date_to . ' 23:59:59');
        }

        $perPage = $request->get('per_page', 15);
        $documents = $query->latest('signed_at')->paginate($perPage);

        $results = $documents->map(function ($document) {
            return [
                'tracking_code' => $document->tracking_code,
                'filename' => $document->original_filename,
                'signer' => [
                    'name' => $document->user->name,
                    'email' => $document->user->email,
                ],
                'signed_at' => $document->signed_at->toISOString(),
                'file_size' => $this->formatBytes($document->file_size),
                'pages_count' => $document->pages_count,
            ];
        });

        return response()->json([
            'message' => 'Recherche effectuée avec succès.',
            'data' => $results,
            'meta' => [
                'current_page' => $documents->currentPage(),
                'last_page' => $documents->lastPage(),
                'per_page' => $documents->perPage(),
                'total' => $documents->total(),
            ],
        ], 200);
    }

    /**
     * Statistiques de vérification
     *
     * @return JsonResponse
     */
    public function verificationStats(): JsonResponse
    {
        $stats = [
            'total_signed_documents' => Document::where('status', 'signed')->count(),
            'total_verifications_today' => 0, // À implémenter avec un système de logs
            'total_verifications_this_month' => 0, // À implémenter avec un système de logs
            'documents_by_month' => Document::where('status', 'signed')
                ->selectRaw('YEAR(signed_at) as year, MONTH(signed_at) as month, COUNT(*) as count')
                ->groupBy('year', 'month')
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->limit(12)
                ->get(),
        ];

        return response()->json([
            'message' => 'Statistiques récupérées avec succès.',
            'data' => $stats,
        ], 200);
    }

    /**
     * Formate la taille de fichier en format lisible
     *
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}