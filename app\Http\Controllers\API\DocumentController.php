<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\DocumentUploadRequest;
use App\Http\Resources\DocumentResource;
use App\Models\Document;
use App\Services\OTPService;
use App\Services\PDFService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class DocumentController extends Controller
{
    public function __construct(
        private PDFService $pdfService,
        private OTPService $otpService
    ) {
        $this->middleware('auth:sanctum');
    }

    /**
     * Récupère la liste des documents de l'utilisateur authentifié
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $status = $request->get('status');

        $query = $request->user()->documents()->latest();

        if ($status) {
            $query->where('status', $status);
        }

        $documents = $query->paginate($perPage);

        return response()->json([
            'message' => 'Documents récupérés avec succès',
            'data' => DocumentResource::collection($documents->items()),
            'meta' => [
                'current_page' => $documents->currentPage(),
                'last_page' => $documents->lastPage(),
                'per_page' => $documents->perPage(),
                'total' => $documents->total(),
            ],
        ], 200);
    }

    /**
     * Téléverse un nouveau document PDF
     *
     * @param DocumentUploadRequest $request
     * @return JsonResponse
     */
    public function store(DocumentUploadRequest $request): JsonResponse
    {
        try {
            Log::info('Début du téléversement', ['user_id' => $request->user()->id]);

            $validated = $request->validated();
            $file = $request->file('document'); 

            if (!$file) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun fichier reçu',
                ], 400);
            }

            Log::info('Fichier reçu', [
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
            ]);

            // Vérifier que le fichier est bien un PDF
            if ($file->getMimeType() !== 'application/pdf') {
                return response()->json([
                    'success' => false,
                    'message' => 'Seuls les fichiers PDF sont acceptés',
                ], 400);
            }

            $filename = Str::uuid() . '.pdf';
            Storage::disk('private')->makeDirectory('documents/original');
            $path = $file->storeAs('documents/original', $filename, 'private'); 

            Log::info('Fichier stocké', ['path' => $path]);

        $document = Document::create([
            'user_id' => $request->user()->id,
            'original_filename' => $file->getClientOriginalName(),
            'stored_filename' => $filename,
            'file_path' => $path,
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'pages_count' => $this->pdfService->getPageCount($file->getRealPath()),
            'status' => 'uploaded',
        ]);

            Log::info('Document enregistré', [
                'document_id' => $document->id,
                'document_type' => get_class($document),
                'document_attributes' => $document->getAttributes(),
                'original_filename' => $document->original_filename ?? 'NULL'
            ]);

            // Générer et envoyer l'OTP
            $otpMethod = $validated['otp_method'] ?? 'email';
            try {
                $this->otpService->generateAndSend($request->user(), $otpMethod, $document->id);
                Log::info('OTP généré et envoyé', ['document_id' => $document->id, 'method' => $otpMethod]);
            } catch (\Exception $e) {
                Log::warning('Erreur lors de l\'envoi OTP', ['error' => $e->getMessage()]);
                // Continuer même si l'OTP échoue
            }

            // Recharger le document pour avoir toutes les relations
            $document->refresh();
            
            Log::info('Document après refresh', [
                'document_id' => $document->id,
                'document_type' => get_class($document),
                'original_filename' => $document->original_filename ?? 'NULL',
                'attributes_keys' => array_keys($document->getAttributes())
            ]);

            // Créer le resource avec vérification
            try {
                $documentResource = new DocumentResource($document);
                Log::info('DocumentResource créé avec succès');
            } catch (\Exception $e) {
                Log::error('Erreur lors de la création du DocumentResource', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }

            return response()->json([
                'success' => true,
                'message' => 'Document téléversé avec succès. Code OTP envoyé.',
                'data' => $documentResource,
            ], 201);

        } catch (\Throwable $e) {
            Log::error('Erreur lors du téléversement', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur interne lors du téléversement',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    /**
     * Récupère les détails d'un document spécifique
     *
     * @param Document $document
     * @return JsonResponse
     */
    public function show(Document $document): JsonResponse
    {
        $this->authorize('view', $document);

        return response()->json([
            'message' => 'Document récupéré avec succès',
            'data' => new DocumentResource($document),
        ], 200);
    }

    /**
     * Télécharge le document signé
     *
     * @param Document $document
     * @return Response
     */
public function download(Document $document)
{
    $this->authorize('view', $document);

   $path = $document->signed_file_path ?: $document->file_path;
   if (!Storage::disk('private')->exists($path)) {
        return response()->json([
            'message' => 'Fichier introuvable',
        ], 404);
    }
    $abs = Storage::disk('private')->path($path);
return response()->download($abs, $document->original_filename, [
    'Content-Type' => 'application/pdf',
]);

    return response()->json([
        'message' => 'Document téléchargé avec succès',
    ], 200);
}


    /**
     * Supprime un document
     *
     * @param Document $document
     * @return JsonResponse
     */
    public function destroy(Document $document): JsonResponse
    {
        $this->authorize('delete', $document);

        // Supprimer les fichiers associés
        if (Storage::disk('private')->exists($document->file_path)) {
            Storage::disk('private')->delete($document->file_path);
        }

        if ($document->signed_file_path && Storage::disk('private')->exists($document->signed_file_path)) {
            Storage::disk('private')->delete($document->signed_file_path);
        }

        // Supprimer l'enregistrement
        $document->delete();

        return response()->json([
            'message' => 'Document supprimé avec succès',
        ], 200);
    }

    /**
     * Récupère les statistiques des documents de l'utilisateur
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            $base = $user->documents();

            $totalDocuments = (int) $base->count();
            $signedDocuments = (int) $user->documents()->where('status', 'signed')->count();
            $pendingDocuments = (int) $user->documents()->where('status', 'uploaded')->count();
            $processingDocuments = (int) $user->documents()->where('status', 'processing')->count();

            // Dernière signature et nombre signé sur les 7 derniers jours
            $lastSignedAt = optional(
                $user->documents()->whereNotNull('signed_at')->latest('signed_at')->first()
            )->signed_at;

            $recentSigned7 = (int) $user->documents()
                ->where('status', 'signed')
                ->where('signed_at', '>=', now()->subDays(7))
                ->count();

            // Espace utilisé par les documents (somme des tailles)
            $storageBytes = (int) $user->documents()->sum('file_size');
            $storageHuman = $this->formatBytes($storageBytes);

            // Durée moyenne de signature (upload -> signed)
            $avgSeconds = (int) ($user->documents()
                ->whereNotNull('signed_at')
                ->selectRaw('AVG(strftime("%s", signed_at) - strftime("%s", created_at)) as avg_seconds')
                ->value('avg_seconds') ?? 0);

            // Vérifications effectuées par l'utilisateur
            $verificationsCount = (int) \App\Models\VerificationLog::where('user_id', $user->id)->count();

            // Invitations que l'utilisateur a reçues (d'autres utilisateurs lui demandent de signer)
            $invitationsForMe = (int) \App\Models\DocumentSigner::where(function($q) use ($user) {
                $q->where('user_id', $user->id)
                  ->orWhere('email', $user->email);
            })
            ->where('status', 'pending')
            ->whereNull('signed_at')
            ->count();

            $stats = [
                'total_documents' => $totalDocuments,
                'signed_documents' => $signedDocuments,
                'pending_documents' => $pendingDocuments,
                'processing_documents' => $processingDocuments,
                'recent_signed_last_7_days' => $recentSigned7,
                'last_signed_at' => $lastSignedAt ? $lastSignedAt->toISOString() : null,
                'storage_used_bytes' => $storageBytes,
                'storage_used_human' => $storageHuman,
                'average_time_to_sign_seconds' => $avgSeconds,
                'verifications_count' => $verificationsCount,
                'invitations_for_me_count' => $invitationsForMe,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Statistiques récupérées avec succès',
                'data' => $stats,
            ], 200);
        } catch (\Throwable $e) {
            \Log::error('Erreur stats documents', [
                'user_id' => optional($request->user())->id,
                'error' => $e->getMessage(),
            ]);
            return response()->json([
                'success' => true,
                'message' => 'Statistiques par défaut',
                'data' => [
                    'total_documents' => 0,
                    'signed_documents' => 0,
                    'pending_documents' => 0,
                    'processing_documents' => 0,
                    'recent_signed_last_7_days' => 0,
                    'last_signed_at' => null,
                    'storage_used_bytes' => 0,
                    'storage_used_human' => '0 B',
                    'average_time_to_sign_seconds' => 0,
                    'verifications_count' => 0,
                    'invitations_for_me_count' => 0,
                ],
            ], 200);
        }
    }

    private function formatBytes(int $bytes): string
    {
        if ($bytes <= 0) return '0 B';
        $units = ['B','KB','MB','GB','TB'];
        $power = (int) floor(log($bytes, 1024));
        $power = min($power, count($units) - 1);
        $value = $bytes / pow(1024, $power);
        return number_format($value, $power >= 2 ? 2 : 0) . ' ' . $units[$power];
    }

    /**
     * Récupère le contenu PDF pour prévisualisation
     *
     * @param Document $document
     * @return Response
     */
    public function preview(Document $document)
    {
        $this->authorize('view', $document);

        if (!Storage::disk('private')->exists($document->file_path)) {
            return response()->json([
                'message' => 'Fichier introuvable',
            ], 404);
        }

        $fileContent = Storage::disk('private')->get($document->file_path);

        return response($fileContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $document->original_filename . '"',
        ]);
    }

    /**
     * Met à jour le statut d'un document
     *
     * @param Request $request
     * @param Document $document
     * @return JsonResponse
     */
    public function updateStatus(Request $request, Document $document): JsonResponse
    {
        $this->authorize('update', $document);

        $request->validate([
            'status' => 'required|in:uploaded,processing,signed,failed',
        ]);

        $document->update([
            'status' => $request->status,
        ]);

        return response()->json([
            'message' => 'Statut du document mis à jour avec succès',
            'data' => new DocumentResource($document),
        ], 200);
    }
}

