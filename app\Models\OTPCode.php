<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OTPCode extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */

     protected $table = 'otp_codes';
    protected $fillable = [
        'user_id',
        'document_id',
        'code',
        'code_hashed',
        'type',
        'expires_at',
        'used_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
    ];

    /**
     * Get the user that owns the OTP code.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the OTP code is expired.
     *
     * @return bool
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at < now();
    }

    /**
     * Check if the OTP code is used.
     *
     * @return bool
     */
    public function getIsUsedAttribute(): bool
    {
        return !is_null($this->used_at);
    }

    /**
     * Check if the OTP code is valid (not expired and not used).
     *
     * @return bool
     */
    public function getIsValidAttribute(): bool
    {
        return !$this->is_expired && !$this->is_used;
    }
}