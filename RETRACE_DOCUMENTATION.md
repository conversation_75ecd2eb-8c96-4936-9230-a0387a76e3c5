# 📋 Documentation : Retrace de Document Physique - SignSecure

## 🎯 Vue d'ensemble

La fonctionnalité "Retrace de document physique" permet aux utilisateurs de créer un suivi numérique pour des documents physiques déjà signés. Elle génère un PDF blanc avec un code de suivi unique et les informations du document.

## 🚀 Fonctionnalités

### ✨ Caractéristiques principales
- **Formulaire intuitif** avec validation des champs
- **Double authentification** via OTP avant génération du PDF
- **Génération automatique** d'un PDF avec code de suivi
- **Interface moderne** et responsive
- **Sécurité renforcée** avec vérification OTP

### 📝 Champs du formulaire
- **Date du document** (obligatoire) - Sélecteur de date
- **Référence** (obligatoire) - Identifiant unique du document
- **Objet** (obligatoire) - Description du document
- **Notes** (optionnel) - Observations additionnelles

## 🔐 Sécurité

### Processus d'authentification
1. **Soumission du formulaire** → Stockage temporaire en session
2. **Envoi d'un code OTP** par email
3. **Vérification du code** par l'utilisateur
4. **Génération du PDF** après validation OTP

### Contrôles d'accès
- **Authentification requise** pour toutes les actions
- **Politique d'autorisation** : utilisateur propriétaire uniquement
- **Session sécurisée** pour les données temporaires

## 🛠️ Architecture technique

### Modèles
```php
PhysicalRetrace
├── user_id (relation avec User)
├── tracking_code (unique)
├── date
├── reference
├── subject
├── notes
├── pdf_path
└── timestamps
```

### Contrôleurs
- **PhysicalRetraceController** : Gestion du workflow complet
- **OTPService** : Gestion des codes de vérification
- **PDFService** : Génération des PDFs

### Routes
```
GET  /retrace/create          → Formulaire de création
POST /retrace/request-otp     → Demande de code OTP
GET  /retrace/verify-otp      → Page de vérification OTP
POST /retrace/verify-otp      → Validation OTP + création
GET  /retrace/{id}            → Affichage du retrace
GET  /retrace/{id}/download   → Téléchargement du PDF
```

## 📱 Interface utilisateur

### Design
- **Framework CSS** : Tailwind CSS (classes utilitaires)
- **Icônes** : Font Awesome
- **Responsive** : Adaptation mobile/desktop
- **Animations** : Transitions et effets hover

### Composants visuels
- **Header avec icône** : Identification claire de la fonction
- **Formulaire en carte** : Design moderne avec ombres
- **Validation visuelle** : Messages d'erreur clairs
- **Boutons stylisés** : Dégradés et effets interactifs

## 🔄 Workflow utilisateur

### 1. Accès au formulaire
```
Dashboard → Menu "Retracer un document physique" → /retrace/create
```

### 2. Saisie des informations
- Remplir tous les champs obligatoires
- Ajouter des notes si nécessaire
- Cliquer sur "Demander le code de vérification"

### 3. Vérification OTP
- Recevoir le code par email
- Saisir le code sur la page de vérification
- Valider pour créer le retrace

### 4. Résultat
- Affichage du retrace créé
- Code de suivi en évidence
- Bouton de téléchargement du PDF
- Option pour créer un autre retrace

## 📊 Utilisation du retrace

### Code de suivi
- **Format** : `SS-PR-YYYYMMDD-XXXXX`
- **Préfixe** : `SS-PR` (SignSecure - Physical Retrace)
- **Date** : Date de création (YYYYMMDD)
- **Suffixe** : 5 caractères alphanumériques

### Vérification
- **Page publique** : `/verify/{tracking_code}`
- **API** : `/api/verify` avec le code
- **Résultat** : Informations du retrace (pas de document numérique)

## 🧪 Tests et validation

### Tests automatisés
```bash
# Vérifier le statut des migrations
php artisan migrate:status

# Lister les routes de retrace
php artisan route:list --name=retrace

# Tester la fonctionnalité
php test-retrace.php
```

### Validation manuelle
1. **Créer un retrace** via l'interface
2. **Vérifier l'envoi OTP** dans les logs
3. **Valider le code** et créer le retrace
4. **Télécharger le PDF** généré
5. **Vérifier le code** sur la page publique

## 🚨 Dépannage

### Problèmes courants

#### Erreur "Route non définie"
- Vérifier que les routes sont bien enregistrées
- Exécuter `php artisan route:clear`

#### OTP non reçu
- Vérifier la configuration email dans `.env`
- Consulter les logs Laravel
- Utiliser le driver `log` pour les tests

#### Erreur de génération PDF
- Vérifier les permissions du dossier `storage/app/private/documents/tracked`
- S'assurer que FPDI est installé : `composer require setasign/fpdi`

#### Problème de migration
- Vérifier que la table `physical_retraces` existe
- Exécuter `php artisan migrate:rollback` puis `php artisan migrate`

## 🔧 Configuration

### Variables d'environnement
```env
# Configuration email pour les OTP
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=votre-mot-de-passe-app
MAIL_ENCRYPTION=tls

# Pour les tests, utiliser le driver log
MAIL_MAILER=log
```

### Permissions des dossiers
```bash
# Dossier de stockage des PDFs de retrace
chmod -R 755 storage/app/private/documents/tracked
chown -R www-data:www-data storage/app/private/documents/tracked
```

## 📈 Évolutions futures

### Fonctionnalités prévues
- **Historique des retraces** par utilisateur
- **Recherche et filtres** avancés
- **Export des données** en CSV/Excel
- **Notifications** par email/SMS
- **API REST** complète pour intégration externe

### Améliorations techniques
- **Cache Redis** pour les codes OTP
- **Queue jobs** pour la génération PDF asynchrone
- **Audit trail** complet des actions
- **Backup automatique** des retraces

## 📞 Support

### En cas de problème
1. **Vérifier les logs** : `storage/logs/laravel.log`
2. **Consulter la documentation** Laravel
3. **Tester avec le script** `test-retrace.php`
4. **Vérifier la configuration** email et base de données

### Ressources utiles
- [Documentation Laravel](https://laravel.com/docs)
- [Documentation FPDI](https://www.setasign.com/products/fpdi/about/)
- [Guide Tailwind CSS](https://tailwindcss.com/docs)

---

**Version** : 1.0.0  
**Dernière mise à jour** : 27 août 2025  
**Auteur** : Équipe SignSecure

