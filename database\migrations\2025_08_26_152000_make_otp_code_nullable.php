<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasTable('otp_codes') && Schema::hasColumn('otp_codes', 'code')) {
            // Rendre la colonne nullable pour éviter les erreurs quand on n'utilise que code_hashed
            try {
                DB::statement('ALTER TABLE `otp_codes` MODIFY `code` VARCHAR(255) NULL');
            } catch (\Throwable $e) {
                // silencieux: certains SGBD peuvent déjà être dans le bon état
            }
        }
    }

    public function down(): void
    {
        // Pas de revert strict pour éviter de casser des données existantes
    }
};



