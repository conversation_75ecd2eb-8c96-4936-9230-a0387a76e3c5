<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DocumentSigner extends Model
{
    use HasFactory;

    protected $fillable = [
        'document_id',
        'user_id',
        'email',
        'name',
        'status',
        'signature_order',
        'signed_at',
        'expires_at',
        'signature_position',
    ];

    protected $casts = [
        'signed_at' => 'datetime',
        'expires_at' => 'datetime',
        'signature_position' => 'array',
    ];

    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function getCanSignAttribute(): bool
    {
        return $this->status === 'pending' && !$this->is_expired;
    }

    public function getIsCurrentSignerAttribute(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        // Vérifier si tous les signataires précédents ont signé
        $previousSigners = $this->document->signers()
            ->where('signature_order', '<', $this->signature_order)
            ->where('status', '!=', 'signed');

        return $previousSigners->count() === 0;
    }
}

