<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("documents", function (Blueprint $table) {
            $table->id();
            $table->foreignId("user_id")->constrained()->onDelete("cascade");
            $table->string("original_filename");
            $table->string("stored_filename");
            $table->string("file_path");
            $table->string("signed_file_path")->nullable();
            $table->unsignedBigInteger("file_size");
            $table->string("mime_type");
            $table->integer("pages_count");
            $table->string("status")->default("pending"); // pending, uploaded, signed, rejected
            $table->string("tracking_code")->unique();
            $table->timestamp("signed_at")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("documents");
    }
};