<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Signature;

class SignatureController extends Controller
{
    public function create()
    {
        $user = Auth::user();
        $hasSignature = !empty($user->signature_image);
        
        return view('signatures.create', compact('hasSignature'));
    }

    public function store(Request $request)
    {
        // Cette méthode sera gérée par l'API
        return redirect()->back()->with('success', 'Signature sauvegardée avec succès');
    }

    public function current()
    {
        $user = Auth::user();
        return response()->json([
            'has_signature' => !empty($user->signature_image),
            'signature_data' => $user->signature_image
        ]);
    }

    public function delete()
    {
        // Cette méthode sera gérée par l'API
        return redirect()->back()->with('success', 'Signature supprimée avec succès');
    }
}