<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Signature;
use App\Models\AuditLog;
use Illuminate\Support\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth:sanctum', 'is.admin']);
    }

    public function index()
    {
        $totalUsers = User::count();
        $activeLast7 = User::where('updated_at', '>=', now()->subDays(7))->count();
        $totalSignatures = Signature::count();
        $auditCount = AuditLog::count();

        return view('admin.dashboard', compact('totalUsers','activeLast7','totalSignatures','auditCount'));
    }
}


