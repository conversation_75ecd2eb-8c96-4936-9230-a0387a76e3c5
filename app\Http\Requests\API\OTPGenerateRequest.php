<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class OTPGenerateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'document_id' => ['required', 'exists:documents,id'],
            'type' => ['required', 'string', 'in:email,sms'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'document_id.required' => 'L\'ID du document est obligatoire.',
            'document_id.exists' => 'Le document spécifié n\'existe pas.',
            'type.required' => 'Le type d\'envoi de l\'OTP est obligatoire.',
            'type.in' => 'Le type d\'envoi doit être email ou sms.',
        ];
    }
}