<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Http\Resources\DocumentResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TestController extends Controller
{
    /**
     * Test simple du DocumentResource
     */
    public function testDocumentResource()
    {
        try {
            Log::info('Test du DocumentResource démarré');

            // Créer un utilisateur de test s'il n'existe pas
            $user = \App\Models\User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Test User',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ]
            );

            Log::info('Utilisateur de test', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            // Créer un document de test en base
            $document = Document::create([
                'user_id' => $user->id,
                'original_filename' => 'test_document.pdf',
                'stored_filename' => 'test_stored.pdf',
                'file_path' => 'test/path',
                'file_size' => 1024,
                'mime_type' => 'application/pdf',
                'pages_count' => 1,
                'status' => 'uploaded',
            ]);

            Log::info('Document de test créé', [
                'document_id' => $document->id,
                'class' => get_class($document),
                'attributes' => $document->getAttributes()
            ]);

            // Tester le resource
            $resource = new DocumentResource($document);
            Log::info('DocumentResource créé', [
                'class' => get_class($resource),
                'resource_type' => get_class($resource->resource)
            ]);

            // Convertir en array
            $data = $resource->toArray(request());
            Log::info('DocumentResource converti en array', [
                'data_keys' => array_keys($data),
                'original_filename' => $data['original_filename'] ?? 'NULL'
            ]);

            // Nettoyer le document de test
            $document->delete();

            return response()->json([
                'success' => true,
                'message' => 'Test réussi',
                'user_id' => $user->id,
                'document_id' => $document->id,
                'document_class' => get_class($document),
                'resource_class' => get_class($resource),
                'data' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors du test DocumentResource', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Test avec un document existant
     */
    public function testExistingDocument()
    {
        try {
            // Récupérer le premier document existant
            $document = Document::first();
            
            if (!$document) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun document trouvé en base'
                ], 404);
            }

            Log::info('Test avec document existant', [
                'id' => $document->id,
                'class' => get_class($document),
                'attributes' => $document->getAttributes()
            ]);

            // Tester le resource
            $resource = new DocumentResource($document);
            $data = $resource->toArray(request());

            return response()->json([
                'success' => true,
                'message' => 'Test avec document existant réussi',
                'document_id' => $document->id,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors du test avec document existant', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Test complet du processus de téléversement
     */
    public function testUploadProcess()
    {
        try {
            Log::info('Test du processus de téléversement démarré');

            // Créer un utilisateur de test s'il n'existe pas
            $user = \App\Models\User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Test User',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ]
            );

            Log::info('Utilisateur de test', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            // Simuler la création d'un document (comme dans DocumentController::store)
            $document = Document::create([
                'user_id' => $user->id,
                'original_filename' => 'test_upload.pdf',
                'stored_filename' => 'test_upload_stored.pdf',
                'file_path' => 'test/upload/path',
                'file_size' => 2048,
                'mime_type' => 'application/pdf',
                'pages_count' => 2,
                'status' => 'uploaded',
            ]);

            Log::info('Document de test créé pour téléversement', [
                'document_id' => $document->id,
                'class' => get_class($document),
                'attributes' => $document->getAttributes(),
                'original_filename' => $document->original_filename
            ]);

            // Tester le resource exactement comme dans le contrôleur
            try {
                $documentResource = new DocumentResource($document);
                Log::info('DocumentResource créé avec succès');
                
                // Convertir en array
                $data = $documentResource->toArray(request());
                Log::info('DocumentResource converti en array', [
                    'data_keys' => array_keys($data),
                    'original_filename' => $data['original_filename'] ?? 'NULL',
                    'data_structure' => $data
                ]);

                // Nettoyer le document de test
                $document->delete();

                return response()->json([
                    'success' => true,
                    'message' => 'Test du processus de téléversement réussi',
                    'user_id' => $user->id,
                    'document_id' => $document->id,
                    'document_class' => get_class($document),
                    'resource_class' => get_class($documentResource),
                    'data' => $data
                ]);

            } catch (\Exception $e) {
                Log::error('Erreur lors de la création du DocumentResource', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'document' => $document->toArray()
                ]);
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Erreur lors du test du processus de téléversement', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}
