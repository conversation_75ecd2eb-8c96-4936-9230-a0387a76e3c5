<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use App\Services\TrackingCodeService;

class Document extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'original_filename',
        'stored_filename',
        'file_path',
        'signed_file_path',
        'file_size',
        'mime_type',
        'pages_count',
        'status',
        'tracking_code',
        'signed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'signed_at' => 'datetime',
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($document) {
            $document->tracking_code = TrackingCodeService::generateDocumentCode();
        });
    }

    /**
     * Get the user that owns the document.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the signatures for the document.
     */
    public function signatures(): HasMany
    {
        return $this->hasMany(Signature::class);
    }

    public function signers(): HasMany
    {
        return $this->hasMany(DocumentSigner::class);
    }

    public function getIsFullySignedAttribute(): bool
    {
        return $this->signers()->where('status', '!=', 'signed')->count() === 0;
    }

    public function getCurrentSignerAttribute()
    {
        return $this->signers()
            ->where('status', 'pending')
            ->where('signature_order', $this->signers()->where('status', 'signed')->count() + 1)
            ->first();
    }

    /**
     * Check if the document is signed.
     *
     * @return bool
     */
    public function getIsSignedAttribute(): bool
    {
        return $this->status === 'signed' && !is_null($this->signed_file_path);
    }

    /**
     * Get the file size in human readable format.
     *
     * @return string
     */
    public function getFileSizeHumanAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

       
        return round($bytes, 2) . ' ' . $units[$pow];
    }

     public function lastOtpForUser($user)
    {
        return OTPCode::where('user_id', $user->id)
                      ->where('document_id', $this->id)
                      ->orderBy('created_at', 'desc')
                      ->first();
    }
}
