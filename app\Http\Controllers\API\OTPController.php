<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\OTPGenerateRequest;
use App\Http\Requests\API\OTPVerifyRequest;
use App\Models\Document;
use App\Services\OTPService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\User;

class OTPController extends Controller
{
    public function __construct(private OTPService $otpService)
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Génère et envoie un nouveau code OTP pour un document
     */
    public function generate(OTPGenerateRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $document = Document::findOrFail($validated['document_id']);
        $this->authorize('view', $document);

        $type = $validated['type'] ?? 'email';

        try {
            $otp = $this->otpService->generateAndSend($request->user(), $type, $document->id);

            return response()->json([
                'success' => true,
                'message' => "Code OTP envoyé par {$type} avec succès",
                'data' => [
                    'document_id' => $document->id,
                    'type' => $type,
                    'expires_at' => now()->addMinutes(10)->toIso8601String(),
                ],
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'envoi du code OTP',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Vérifie un code OTP
     */
    public function verify(OTPVerifyRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $document = Document::findOrFail($validated['document_id']);
        $this->authorize('view', $document);

        $isValid = $this->otpService->verify($request->user(), $validated['code'], $document->id);

        if ($isValid) {
            return response()->json([
                'success' => true,
                'message' => 'Code OTP vérifié avec succès',
                'data' => [
                    'document_id' => $document->id,
                    'verified' => true,
                    'valid_until' => now()->addMinutes(30)->toIso8601String(),
                ],
            ], 200);
        }

        return response()->json([
            'success' => false,
            'message' => 'Code OTP invalide ou expiré',
            'data' => [
                'document_id' => $document->id,
                'verified' => false,
            ],
        ], 422);
    }

    /**
     * Vérifie le statut de vérification OTP pour un document
     */
    public function status(Request $request, Document $document): JsonResponse
    {
        $this->authorize('view', $document);

        $hasValidOTP = $request->user()->otpCodes()
            ->where('created_at', '>', now()->subMinutes(30))
            ->whereNotNull('used_at')
            ->exists();

        return response()->json([
            'message' => 'Statut OTP récupéré avec succès',
            'data' => [
                'document_id' => $document->id,
                'otp_verified' => $hasValidOTP,
                'can_proceed_to_signature' => $hasValidOTP,
            ],
        ], 200);
    }

    /**
     * Récupère l'historique des codes OTP de l'utilisateur
     */
    public function history(Request $request): JsonResponse
    {
        $otpCodes = $request->user()->otpCodes()
            ->latest()
            ->limit(10)
            ->get(['id', 'type', 'expires_at', 'used_at', 'created_at']);

        $history = $otpCodes->map(function ($otp) {
            return [
                'id' => $otp->id,
                'type' => $otp->type,
                'status' => $otp->used_at ? 'used' : ($otp->expires_at < now() ? 'expired' : 'active'),
                'created_at' => $otp->created_at->toIso8601String(),
                'expires_at' => $otp->expires_at->toIso8601String(),
                'used_at' => $otp->used_at?->toIso8601String(),
            ];
        });

        return response()->json([
            'message' => 'Historique OTP récupéré avec succès',
            'data' => $history,
        ], 200);
    }

    /**
     * Annule tous les codes OTP actifs de l'utilisateur
     */
    public function cancelAll(Request $request): JsonResponse
    {
        $cancelledCount = $request->user()->otpCodes()
            ->whereNull('used_at')
            ->where('expires_at', '>', now())
            ->update(['used_at' => now()]);

        return response()->json([
            'message' => 'Codes OTP annulés avec succès',
            'data' => [
                'cancelled_count' => $cancelledCount,
            ],
        ], 200);
    }

    /**
     * Renvoie un code OTP
     */
    public function resend(Request $request, Document $document): JsonResponse
    {
        $this->authorize('view', $document);

        $type = $request->input('type', 'email');
        $user = $request->user();

        // Vérifier le dernier OTP pour ce document et cet utilisateur
        $lastOtp = $user->otpCodes()
            ->where('document_id', $document->id)
            ->latest()
            ->first();

        // Si le dernier OTP existe et a été envoyé il y a moins de 10 minutes
        if ($lastOtp && $lastOtp->created_at->diffInSeconds(now()) < 600) {
            return response()->json([
                'success' => false,
                'message' => 'Veuillez attendre avant de renvoyer un nouveau code OTP.',
            ], 429); 
        }

        // Invalider les anciens OTP non utilisés
        $user->otpCodes()
            ->where('document_id', $document->id)
            ->whereNull('used_at')
            ->update(['used_at' => now()]);

        // Générer un nouveau code OTP et l'associer au document
        $otp = $this->otpService->generateAndSend($user, $type, $document->id);

        return response()->json([
            'success' => true,
            'message' => "Code OTP renvoyé par {$type} avec succès",
            'expires_at' => now()->addMinutes(10)->toIso8601String(),
        ]);
    }

    public function showOtpForm(Document $document)
    {
        $this->authorize('view', $document);

        $otp = $document->lastOtpForUser(request()->user());

        return view('documents.verify-otp', [
            'document' => $document,
            'otp_expires_at' => $otp ? $otp->expires_at->toIso8601String() : null,
        ]);
    }
}
