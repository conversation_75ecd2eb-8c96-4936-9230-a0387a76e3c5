<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'signature_image',
        'signature_path',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the documents for the user.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the signatures for the user.
     */
    public function signatures(): HasMany
    {
        return $this->hasMany(Signature::class);
    }

    public function documentSigners(): HasMany
    {
        return $this->hasMany(DocumentSigner::class);
    }

    /**
     * Check if the user has a saved signature.
     *
     * @return bool
     */
    public function hasSignature(): bool
    {
        return !is_null($this->signature_image) && !is_null($this->signature_path);
    }

    /**
     * Get the user's signature image (base64).
     *
     * @return string|null
     */
    public function getSignatureImageAttribute($value): ?string
    {
        return $value;
    }

    /**
     * Get the OTP codes for the user.
     */
    public function otpCodes(): HasMany
    {
        return $this->hasMany(OTPCode::class);
    }

    /**
     * Get the physical retraces for the user.
     */
    public function physicalRetraces(): HasMany
    {
        return $this->hasMany(PhysicalRetrace::class);
    }
}
