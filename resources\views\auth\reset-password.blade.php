@extends('layouts.app')

@section('title', 'Réinitialiser le mot de passe - SignSecure')

@section('content')
<div class="card mx-auto mt-5 p-4" style="max-width: 480px;">
    <h3 class="text-center mb-3">Réinitialiser le mot de passe</h3>

    @if (session('status'))
        <div class="alert alert-success">{{ session('status') }}</div>
    @endif

    <form method="POST" action="{{ route('password.update') }}">
        @csrf
        <input type="hidden" name="token" value="{{ $token }}">

        <div class="mb-3">
            <label for="email" class="form-label">Adresse e-mail</label>
            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="password" class="form-label">Nouveau mot de passe</label>
            <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required minlength="8" pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,}" title="8+ caractères, au moins 1 minuscule, 1 majuscule, 1 chiffre et 1 symbole">
            @error('password')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-4">
            <label for="password_confirmation" class="form-label">Confirmer le mot de passe</label>
            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required minlength="8" pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,}" title="8+ caractères, au moins 1 minuscule, 1 majuscule, 1 chiffre et 1 symbole">
        </div>

        <div class="d-grid">
            <button type="submit" class="btn btn-primary">Mettre à jour le mot de passe</button>
        </div>
    </form>

    <div class="text-center mt-3">
        <a href="{{ route('login') }}" class="text-decoration-none">Retour à la connexion</a>
    </div>
</div>
@endsection


