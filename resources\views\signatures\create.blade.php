@extends("layouts.app")

@section("title", "Créer ma signature - SignSecure")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1"><PERSON><PERSON>er ou utiliser ma signature</h1>
                    <p class="text-muted mb-0">Dessinez votre signature ou utilisez celle enregistrée</p>
                </div>
                <a href="{{ route("documents.preview", $document) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Retour à la prévisualisation
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-body p-5">
                    @if(!$hasSignature)
                        <div class="alert alert-info text-center mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            Ceci est votre première signature. Dessinez-la ci-dessous.
                        </div>
                    @else
                        <div class="alert alert-success text-center mb-4">
                            <i class="fas fa-check-circle me-2"></i>
                            Votre signature est déjà enregistrée. Vous pouvez la redessiner si vous le souhaitez.
                        </div>
                    @endif

                    <form method="POST" action="{{ route("signatures.store", $document) }}" id="signatureForm">
                        @csrf
                        <input type="hidden" name="position_x" value="{{ request("position_x") }}">
                        <input type="hidden" name="position_y" value="{{ request("position_y") }}">
                        <input type="hidden" name="page_number" value="{{ request("page_number") }}">
                        <input type="hidden" name="signature_data" id="signatureData">

                        <div class="mb-4">
                            <label class="form-label fw-semibold mb-3">
                                <i class="fas fa-pencil-alt text-primary me-2"></i>
                                Dessinez votre signature ici
                            </label>
                            <div class="signature-pad-container">
                                <canvas id="signaturePad" class="signature-pad border rounded-3 w-100" height="200"></canvas>
                            </div>
                            <div class="d-flex justify-content-center gap-2 mt-3">
                                <button type="button" class="btn btn-outline-danger btn-sm" id="clearSignature">
                                    <i class="fas fa-eraser me-2"></i>
                                    Effacer
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="undoSignature">
                                    <i class="fas fa-undo me-2"></i>
                                    Annuler
                                </button>
                            </div>
                        </div>

                        <div class="mb-4 text-center">
                            <small class="text-muted">
                                Votre signature sera enregistrée et associée à votre compte pour de futures utilisations.
                            </small>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="applySignatureBtn" disabled>
                                <i class="fas fa-check-circle me-2"></i>
                                Appliquer la signature
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Signature Preview -->
            @if($hasSignature)
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-pen-nib text-success me-2"></i>
                        Votre signature actuelle
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="signature-preview mb-3">
                        <img src="{{ Auth::user()->signature_image }}" alt="Ma signature actuelle" class="img-fluid" style="max-height: 100px;">
                    </div>
                    <small class="text-muted">Ceci est la signature actuellement enregistrée pour votre compte.</small>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

@section("scripts")
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script src="{{ asset("js/config.js") }}"></script>
<script src="{{ asset("js/api.js") }}"></script>
<script src="{{ asset("js/signature-capture.js") }}"></script>
@endsection

@push("styles")
<style>
.signature-pad-container {
    border: 2px dashed var(--ss-border);
    border-radius: 0.75rem;
    background-color: #fafafa;
    cursor: crosshair;
    transition: all 0.3s ease;
    overflow: hidden; /* Ensures signature doesn't overflow */
}

.signature-pad-container:hover {
    border-color: var(--ss-primary);
    background-color: #f0f0ff;
}

.signature-pad {
    touch-action: none; /* Prevent scrolling on touch devices */
}

.signature-preview {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 16px;
    background-color: #fafafa;
}

.signature-preview img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}
</style>
@endpush
