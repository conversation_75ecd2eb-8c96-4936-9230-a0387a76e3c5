@extends("layouts.app")

@section("title", "Tableau de bord - SignSecure")

@section("content")
<div class="container-fluid">
   
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Bonjour, <span id="userName">{{ Auth::user()->name ?? 'Utilisateur' }}</span> !
                    <p class="text-muted mb-0">Bienvenue sur votre espace SignSecure</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#profileModal">
                        <i class="fas fa-user-cog me-2"></i>Mon profil
                    </button>
                    <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Actualiser
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0 shadow-sm bg-gradient-primary text-white">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-upload fa-3x opacity-75"></i>
                    </div>
                    <h5 class="card-title">Nouveau document</h5>
                    <p class="card-text opacity-90">Téléchargez un document à faire signer</p>
                    <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-plus me-2"></i>Commencer
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0 shadow-sm bg-gradient-success text-white">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-shield-alt fa-3x opacity-75"></i>
                    </div>
                    <h5 class="card-title">Vérifier un document</h5>
                    <p class="card-text opacity-90">Vérifiez l'authenticité d'un document</p>
                    <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#verificationModal">
                        <i class="fas fa-search me-2"></i>Vérifier
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0 shadow-sm bg-gradient-info text-white">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-file-signature fa-3x opacity-75"></i>
                    </div>
                    <h5 class="card-title">Retrace physique</h5>
                    <p class="card-text opacity-90">Créer un suivi pour document physique</p>
                    <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#retraceModal">
                        <i class="fas fa-search me-2"></i>Retracer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques utilisateur (agrégées) -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-start border-primary border-4 h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="icon-circle bg-primary text-white">
                                <i class="fas fa-file-signature"></i>
                            </div>
                        </div>
                        <div class="col">
                            <h6 class="text-primary text-uppercase mb-1">Mes documents signés</h6>
                            <div class="h5 mb-1 font-weight-bold" id="signedTotal">0</div>
                            <small class="text-muted d-block">Dernier: <span id="lastSignedFilename">-</span></small>
                            <small class="text-muted">Le <span id="lastSignedDate">-</span></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-start border-success border-4 h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="icon-circle bg-success text-white">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                        </div>
                        <div class="col">
                            <h6 class="text-success text-uppercase mb-1">Mes vérifications</h6>
                            <div class="h5 mb-1 font-weight-bold" id="verificationsTotal">0</div>
                            <small class="text-muted d-block">Dernière: <span id="lastVerificationResult">-</span></small>
                            <small class="text-muted">Code: <span id="lastVerificationCode">-</span></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-start border-info border-4 h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="icon-circle bg-info text-white">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="col">
                            <h6 class="text-info text-uppercase mb-1">Mes retraces</h6>
                            <div class="h5 mb-1 font-weight-bold" id="retracesTotal">0</div>
                            <small class="text-muted d-block">Dernier: <span id="lastRetraceSubject">-</span></small>
                            <small class="text-muted">Le <span id="lastRetraceDate">-</span></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-start border-warning border-4 h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="icon-circle bg-warning text-white">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="col">
                            <h6 class="text-warning text-uppercase mb-1">Documents valides</h6>
                            <div class="h5 mb-1 font-weight-bold">
                                <span id="validDocumentsCount">Vous avez 0 documents valides.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activité récente -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock me-2"></i>Activité récente
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush" id="recentActivityList">
                        <li class="list-group-item text-muted">Chargement de l'activité récente...</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Documents récents -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-file-alt me-2"></i>Mes documents récents
                    </h6>
                    <a href="/documents" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list me-1"></i>Voir tous mes documents
                    </a>
                </div>
                <div class="card-body">
                    <div id="documentsList" class="list-group list-group-flush">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin me-2"></i>Chargement des documents...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de profil utilisateur -->
<div class="modal fade" id="profileModal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="profileModalLabel">
                    <i class="fas fa-user-cog me-2"></i>Mon profil
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Nav tabs -->
                <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="profile-info-tab" data-bs-toggle="tab" data-bs-target="#profile-info" type="button" role="tab">
                            <i class="fas fa-user me-2"></i>Informations
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="profile-password-tab" data-bs-toggle="tab" data-bs-target="#profile-password" type="button" role="tab">
                            <i class="fas fa-lock me-2"></i>Mot de passe
                        </button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content mt-3" id="profileTabContent">
                    <!-- Informations personnelles -->
                    <div class="tab-pane fade show active" id="profile-info" role="tabpanel">
                        <form id="profileInfoForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="profileName" class="form-label">Nom complet</label>
                                        <input type="text" class="form-control" id="profileName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="profileEmail" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="profileEmail" readonly>
                                        <div class="form-text">L'email ne peut pas être modifié</div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="profilePhone" class="form-label">Téléphone (optionnel)</label>
                                <input type="tel" class="form-control" id="profilePhone">
                            </div>
                            
                            <div class="alert alert-danger" id="profileInfoError" style="display: none;"></div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary" id="saveProfileBtn">
                                    <i class="fas fa-save me-2"></i>Sauvegarder
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Changement de mot de passe -->
                    <div class="tab-pane fade" id="profile-password" role="tabpanel">
                        <form id="profilePasswordForm">
                            <div class="mb-3">
                                <label for="currentPassword" class="form-label">Mot de passe actuel</label>
                                <input type="password" class="form-control" id="currentPassword" required>
                            </div>
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                                <input type="password" class="form-control" id="newPassword" required>
                                <div class="form-text">Minimum 8 caractères</div>
                            </div>
                            <div class="mb-3">
                                <label for="confirmNewPassword" class="form-label">Confirmer le nouveau mot de passe</label>
                                <input type="password" class="form-control" id="confirmNewPassword" required>
                            </div>
                            
                            <div class="alert alert-danger" id="profilePasswordError" style="display: none;"></div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-warning" id="changePasswordBtn">
                                    <i class="fas fa-key me-2"></i>Changer le mot de passe
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de vérification de document -->
<div class="modal fade" id="verificationModal" tabindex="-1" aria-labelledby="verificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verificationModalLabel">
                    <i class="fas fa-shield-alt text-success me-2"></i>Vérifier l'authenticité d'un document
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Saisissez le code de suivi du document pour vérifier son authenticité et obtenir toutes les informations de signature.
                </div>
                
                <form id="verificationForm">
                    <div class="mb-3">
                        <label for="trackingCode" class="form-label fw-semibold">Code de suivi du document</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-barcode text-muted"></i>
                            </span>
                            <input 
                                type="text" 
                                class="form-control" 
                                id="trackingCode" 
                                placeholder="Exemple: SS-ABCD-1234"
                                required
                                style="text-transform: uppercase;"
                            >
                            <button class="btn btn-primary" type="submit" id="verifyBtn">
                                <i class="fas fa-search me-2"></i>Vérifier
                            </button>
                        </div>
                        <div class="form-text">
                            Le code de suivi se trouve généralement en bas du document signé. Format: SS-XXXX-XXXX ou SS-PR-XXXX-XXXX
                        </div>
                    </div>
                </form>

                <!-- Résultats de vérification -->
                <div id="verificationResults" style="display: none;">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>Document authentique vérifié</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong id="docNameLabel">Nom du document :</strong> <span id="docName">-</span></p>
                                    <p><strong id="signerNameLabel">Signataire :</strong> <span id="signerName">-</span></p>
                                    <p><strong id="signerEmailLabel">Email du signataire :</strong> <span id="signerEmail">-</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong id="signatureDateLabel">Date de signature :</strong> <span id="signatureDate">-</span></p>
                                    <p><strong id="verifiedTrackingCodeLabel">Code de suivi :</strong> <span id="verifiedTrackingCode">-</span></p>
                                </div>
                            </div>
                            <div class="mt-3 d-flex gap-2">
                                <button class="btn btn-outline-primary" id="downloadAuthenticBtn">
                                    <i class="fas fa-download me-2"></i>Télécharger l'original
                                </button>
                                <button class="btn btn-outline-info" id="generateReportBtn">
                                    <i class="fas fa-file-alt me-2"></i>Générer un rapport
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Erreur de vérification -->
                <div id="verificationError" class="alert alert-danger" style="display: none;">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreur de vérification</h6>
                    <p id="errorMessage">Document non trouvé ou code invalide.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de retrace physique -->
<div class="modal fade" id="retraceModal" tabindex="-1" aria-labelledby="retraceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="retraceModalLabel">
                    <i class="fas fa-file-signature text-primary me-2"></i>Retracer un document physique
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Créez un suivi numérique pour vos documents physiques signés avec un code de suivi unique.
                </div>

                <!-- Étape 1: Formulaire de retrace -->
                <div id="retraceStep1">
                    <form id="retraceForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="retraceDate" class="form-label">Date du document <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="retraceDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="retraceReference" class="form-label">Référence <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="retraceReference" placeholder="Ex: CONT-2024-001" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="retraceSubject" class="form-label">Objet du document <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="retraceSubject" placeholder="Ex: Contrat de travail" required>
                        </div>
                        <div class="mb-3">
                            <label for="retraceNotes" class="form-label">Notes additionnelles</label>
                            <textarea class="form-control" id="retraceNotes" rows="3" placeholder="Observations, commentaires..."></textarea>
                        </div>
                    </form>
                </div>

                <!-- Étape 2: Vérification OTP -->
                <div id="retraceStep2" style="display: none;">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        Un code de vérification a été envoyé à votre adresse email.
                    </div>
                    <form id="retraceOtpForm">
                        <div class="mb-3">
                            <label for="retraceOtpCode" class="form-label">Code de vérification</label>
                            <input type="text" class="form-control text-center" id="retraceOtpCode" placeholder="000000" maxlength="6" required>
                            <div class="form-text">Saisissez le code à 6 chiffres reçu par email</div>
                        </div>
                    </form>
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" id="retraceBackBtn">
                            <i class="fas fa-arrow-left me-2"></i>Retour
                        </button>
                    </div>
                </div>

                <!-- Étape 3: Succès -->
                <div id="retraceStep3" style="display: none;">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>Retrace créé avec succès !</h6>
                        <p class="mb-0">Votre document physique a été retracé et un code de suivi unique lui a été attribué.</p>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <h6>Informations du retrace :</h6>
                            <p><strong>Code de suivi :</strong> <span id="retraceTrackingCode" class="badge bg-primary">-</span></p>
                            <p><strong>Objet :</strong> <span id="retraceCreatedSubject">-</span></p>
                            <p><strong>Référence :</strong> <span id="retraceCreatedReference">-</span></p>
                            <p><strong>Date :</strong> <span id="retraceCreatedDate">-</span></p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-primary" id="retraceDownloadBtn">
                            <i class="fas fa-download me-2"></i>Télécharger le PDF de retrace
                        </button>
                    </div>
                </div>

                <!-- Erreurs -->
                <div id="retraceError" class="alert alert-danger mt-3" style="display: none;">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreur</h6>
                    <p id="retraceErrorMessage">Une erreur est survenue.</p>
                </div>
            </div>
            <div class="modal-footer">
                <div id="retraceFooterStep1">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="retraceSubmitBtn">
                        <i class="fas fa-paper-plane me-2"></i>Demander le code de vérification
                    </button>
                </div>
                <div id="retraceFooterStep2" style="display: none;">
                    <button type="button" class="btn btn-primary" id="retraceOtpSubmitBtn">
                        <i class="fas fa-check me-2"></i>Vérifier et créer le retrace
                    </button>
                </div>
                <div id="retraceFooterStep3" style="display: none;">
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>Terminé
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Téléverser / Signer -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalLabel">
                    <i class="fas fa-file-upload text-primary me-2"></i>Téléverser un document
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>Choisissez un PDF à signer.
                </div>
                <form id="uploadForm">
                    <div class="mb-3">
                        <label for="uploadFile" class="form-label fw-semibold">Fichier PDF</label>
                        <input type="file" class="form-control" id="uploadFile" accept=".pdf" required>
                        <div class="form-text">Seuls les fichiers PDF sont acceptés (taille max: 10MB)</div>
                    </div>
                </form>
                
                <div class="alert alert-danger" id="uploadError" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="uploadBtn">
                    <i class="fas fa-upload me-2"></i>Téléverser
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push("styles")
<style>
.icon-circle {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
@endpush

@push("scripts")
<script>
// Fonction utilitaire pour les toasts sécurisés
function safeToast(message, type = 'info') {
    if (window.SignSecureUI && window.SignSecureUI.showToast) {
        SignSecureUI.showToast(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction utilitaire pour les loaders sécurisés
function safeShowLoader(element) {
    if (window.SignSecureUI && window.SignSecureUI.showLoader) {
        SignSecureUI.showLoader(element);
    }
}

function safeHideLoader(element, originalText) {
    if (window.SignSecureUI && window.SignSecureUI.hideLoader) {
        SignSecureUI.hideLoader(element, originalText);
    }
}

// Script pour la vérification de documents
document.addEventListener('DOMContentLoaded', function() {
    // Laisser Bootstrap gérer l'ouverture des modales via les attributs data-bs-*
    const verificationModalEl = document.getElementById('verificationModal');
    const retraceModalEl = document.getElementById('retraceModal');
    const verificationForm = document.getElementById('verificationForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const verificationResults = document.getElementById('verificationResults');
    const verificationError = document.getElementById('verificationError');
    const originalBtnText = verifyBtn.innerHTML;

    verificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const trackingCode = document.getElementById('trackingCode').value.trim().toUpperCase();
        
        if (!trackingCode) {
            safeToast('Veuillez saisir un code de suivi.', 'error');
            return;
        }

        // Cacher les résultats précédents
        verificationResults.style.display = 'none';
        verificationError.style.display = 'none';
        
        safeShowLoader(verifyBtn);

        try {
            const response = await signSecureAPI.verifyDocument(trackingCode);
            
            if (response.success && response.data) {
                // Vérifier si c'est un document ou un retrace
                if (response.data.document) {
                    // C'est un document signé - Remettre les labels par défaut
                    document.getElementById('docNameLabel').textContent = 'Nom du document :';
                    document.getElementById('signerNameLabel').textContent = 'Signataire :';
                    document.getElementById('signerEmailLabel').textContent = 'Email du signataire :';
                    document.getElementById('signatureDateLabel').textContent = 'Date de signature :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                    
                    // Afficher les données du document
                    document.getElementById('docName').textContent = response.data.document.filename || 'N/A';
                    document.getElementById('signerName').textContent = response.data.document.signer_name || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.document.signer_email || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.document.signed_at ? new Date(response.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.document.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour document
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadDocument(response.data.document.id);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = response.data.document.filename || 'document.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Document téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${response.data.document.tracking_code || trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Document vérifié avec succès !', 'success');
                    
                } else if (response.type === 'physical_retrace') {
                    // C'est un retrace physique - Mettre à jour les labels
                    document.getElementById('docNameLabel').textContent = 'Objet du document :';
                    document.getElementById('signerNameLabel').textContent = 'Référence :';
                    document.getElementById('signerEmailLabel').textContent = 'Date :';
                    document.getElementById('signatureDateLabel').textContent = 'Notes :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                    
                    // Afficher les données du retrace
                    document.getElementById('docName').textContent = response.data.subject || 'N/A';
                    document.getElementById('signerName').textContent = response.data.reference || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.date || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.notes || 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour retrace
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `retrace_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('PDF de retrace téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Retrace physique vérifié avec succès !', 'success');
                }
                
                verificationResults.style.display = 'block';
                
            } else {
                verificationError.style.display = 'block';
                console.error('Réponse invalide:', response);
                document.getElementById('errorMessage').textContent = response.message || 'Document non trouvé ou code invalide.';
            }

        } catch (error) {
            verificationError.style.display = 'block';
            console.error('Erreur vérification:', error);
            document.getElementById('errorMessage').textContent = error.message || 'Erreur lors de la vérification.';
        } finally {
            safeHideLoader(verifyBtn, originalBtnText);
        }
    });

    // Formater automatiquement le code de suivi
    document.getElementById('trackingCode').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Gestion du retrace physique
    const retraceForm = document.getElementById('retraceForm');
    const retraceOtpForm = document.getElementById('retraceOtpForm');
    const retraceStep1 = document.getElementById('retraceStep1');
    const retraceStep2 = document.getElementById('retraceStep2');
    const retraceStep3 = document.getElementById('retraceStep3');
    const retraceError = document.getElementById('retraceError');
    const retraceFooterStep1 = document.getElementById('retraceFooterStep1');
    const retraceFooterStep2 = document.getElementById('retraceFooterStep2');
    const retraceFooterStep3 = document.getElementById('retraceFooterStep3');
    let retracePayload = null;

    document.getElementById('retraceSubmitBtn').addEventListener('click', async function(e) {
        e.preventDefault();
        
        const date = document.getElementById('retraceDate').value;
        const reference = document.getElementById('retraceReference').value.trim();
        const subject = document.getElementById('retraceSubject').value.trim();
        const notes = document.getElementById('retraceNotes').value.trim();
        
        if (!date || !reference || !subject) {
            safeToast('Veuillez remplir tous les champs obligatoires', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceSubmitBtn'));

        const payload = {
            date: date,
            reference: reference,
            subject: subject,
            notes: notes
        };
        
        retracePayload = payload;

        try {
            const response = await signSecureAPI.retraceRequestOtp(payload);
            
            if (response.success) {
                retraceStep1.style.display = 'none';
                retraceStep2.style.display = 'block';
                retraceFooterStep1.style.display = 'none';
                retraceFooterStep2.style.display = 'block';
                safeToast('Code de vérification envoyé !', 'success');
            } else {
                throw new Error(response.message || 'Erreur lors de l\'envoi du code');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de l\'envoi du code', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceSubmitBtn'), '<i class="fas fa-paper-plane me-2"></i>Demander le code de vérification');
        }
    });

    retraceOtpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const code = document.getElementById('retraceOtpCode').value.trim();
        
        if (!code) {
            safeToast('Veuillez saisir le code de vérification', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceOtpSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceVerifyOtp(code, retracePayload);
            
            if (response.success && response.data) {
                // Afficher les informations du retrace créé
                document.getElementById('retraceTrackingCode').textContent = response.data.tracking_code || 'N/A';
                document.getElementById('retraceCreatedSubject').textContent = response.data.subject || 'N/A';
                document.getElementById('retraceCreatedReference').textContent = response.data.reference || 'N/A';
                document.getElementById('retraceCreatedDate').textContent = response.data.date || 'N/A';
                
                retraceStep2.style.display = 'none';
                retraceStep3.style.display = 'block';
                retraceFooterStep2.style.display = 'none';
                retraceFooterStep3.style.display = 'block';
                
                // Configurer le bouton de téléchargement
                document.getElementById('retraceDownloadBtn').onclick = async function() {
                    try {
                        const trackingCode = response.data.tracking_code;
                        if (!trackingCode) {
                            safeToast('Code de suivi manquant', 'error');
                            return;
                        }
                        
                        const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `retrace_${trackingCode}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        safeToast('PDF téléchargé avec succès !', 'success');
                    } catch (error) {
                        safeToast('Erreur lors du téléchargement', 'error');
                    }
                };
                
                safeToast('Retrace créé avec succès !', 'success');
            } else {
                throw new Error(response.message || 'Code de vérification invalide');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de la vérification', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceOtpSubmitBtn'), '<i class="fas fa-check me-2"></i>Vérifier et créer le retrace');
        }
    });

    // Bouton retour
    document.getElementById('retraceBackBtn').addEventListener('click', function() {
        retraceStep2.style.display = 'none';
        retraceStep1.style.display = 'block';
        retraceFooterStep2.style.display = 'none';
        retraceFooterStep1.style.display = 'block';
        retraceError.style.display = 'none';
    });

    // Réinitialiser le modal à la fermeture
    document.getElementById('retraceModal').addEventListener('hidden.bs.modal', function() {
        retraceStep1.style.display = 'block';
        retraceStep2.style.display = 'none';
        retraceStep3.style.display = 'none';
        retraceFooterStep1.style.display = 'block';
        retraceFooterStep2.style.display = 'none';
        retraceFooterStep3.style.display = 'none';
        retraceError.style.display = 'none';
        retraceForm.reset();
        retraceOtpForm.reset();
        retracePayload = null;
    });

    // Téléversement via API
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadFile = document.getElementById('uploadFile');
    const originalUploadBtnText = uploadBtn ? uploadBtn.innerHTML : '';

    if (uploadForm && uploadBtn && uploadFile) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            uploadError.style.display = 'none';
            if (!uploadFile.files || uploadFile.files.length === 0) {
                safeToast('Veuillez choisir un fichier PDF.', 'error');
                return;
            }
            safeShowLoader(uploadBtn);
            try {
                const res = await signSecureAPI.uploadDocument(uploadFile.files[0], 'email');
                safeToast('Document téléversé avec succès! Vérification OTP envoyée.', 'success');
                const docId = res?.data?.id || res?.data?.document?.id;
                if (docId) {
                    window.location.href = `/documents/${docId}/verify-otp`;
                    return;
                }
                const modalEl = document.getElementById('uploadModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
                setTimeout(() => window.location.reload(), 800);
            } catch (err) {
                uploadError.textContent = err.message || 'Erreur lors du téléversement';
                uploadError.style.display = 'block';
            } finally {
                safeHideLoader(uploadBtn, originalUploadBtnText);
            }
        });
    }

    // Global reload control for profile modal
    let shouldReloadAfterProfileModal = false;

    // Profile info update
    const profileInfoForm = document.getElementById('profileInfoForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileInfoError = document.getElementById('profileInfoError');
    if (profileInfoForm && saveProfileBtn) {
        profileInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profileInfoError.style.display = 'none';
            const name = document.getElementById('profileName').value.trim();
            const phone = document.getElementById('profilePhone').value.trim();
            if (!name) { safeToast('Le nom est requis', 'error'); return; }
            const originalSaveText = saveProfileBtn.innerHTML;
            safeShowLoader(saveProfileBtn);
            try {
                await signSecureAPI.updateProfile({ name, phone });
                safeToast('Profil mis à jour', 'success');
                // Ferme le modal puis recharge
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profileInfoError.textContent = err.message || 'Erreur lors de la mise à jour du profil';
                profileInfoError.style.display = 'block';
            } finally {
                safeHideLoader(saveProfileBtn, originalSaveText);
            }
        });
    }

    // Change password
    const profilePasswordForm = document.getElementById('profilePasswordForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const profilePasswordError = document.getElementById('profilePasswordError');
    if (profilePasswordForm && changePasswordBtn) {
        profilePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profilePasswordError.style.display = 'none';
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            if (newPassword !== confirmNewPassword) {
                profilePasswordError.textContent = 'La confirmation ne correspond pas';
                profilePasswordError.style.display = 'block';
                return;
            }
            const originalChangeText = changePasswordBtn.innerHTML;
            safeShowLoader(changePasswordBtn);
            try {
                await signSecureAPI.changePassword(currentPassword, newPassword, confirmNewPassword);
                safeToast('Mot de passe mis à jour', 'success');
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profilePasswordError.textContent = err.message || 'Erreur lors du changement de mot de passe';
                profilePasswordError.style.display = 'block';
            } finally {
                safeHideLoader(changePasswordBtn, originalChangeText);
            }
        });
    }

    // When profile modal actually finishes closing, trigger reload if requested
    const profileModalEl = document.getElementById('profileModal');
    if (profileModalEl) {
        profileModalEl.addEventListener('hidden.bs.modal', function() {
            if (shouldReloadAfterProfileModal) {
                shouldReloadAfterProfileModal = false;
                window.location.reload();
            }
        });
    }
});
</script>
@endpush
    cursor: pointer;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.list-group-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
}

.list-group-item:last-child {
    border-bottom: none;
}

.document-item {
    transition: all 0.2s ease;
}

.document-item:hover {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-signed {
    background-color: #198754;
    color: #fff;
}

.status-uploaded {
    background-color: #0dcaf0;
    color: #000;
}

#trackingCode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}
</style>
@endpush

@push("scripts")
<script>
// Fonction utilitaire pour les toasts sécurisés
function safeToast(message, type = 'info') {
    if (window.SignSecureUI && window.SignSecureUI.showToast) {
        SignSecureUI.showToast(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction utilitaire pour les loaders sécurisés
function safeShowLoader(element) {
    if (window.SignSecureUI && window.SignSecureUI.showLoader) {
        SignSecureUI.showLoader(element);
    }
}

function safeHideLoader(element, originalText) {
    if (window.SignSecureUI && window.SignSecureUI.hideLoader) {
        SignSecureUI.hideLoader(element, originalText);
    }
}

// Script pour la vérification de documents
document.addEventListener('DOMContentLoaded', function() {
    // Laisser Bootstrap gérer l'ouverture des modales via les attributs data-bs-*
    const verificationModalEl = document.getElementById('verificationModal');
    const retraceModalEl = document.getElementById('retraceModal');
    const verificationForm = document.getElementById('verificationForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const verificationResults = document.getElementById('verificationResults');
    const verificationError = document.getElementById('verificationError');
    const originalBtnText = verifyBtn.innerHTML;

    verificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const trackingCode = document.getElementById('trackingCode').value.trim().toUpperCase();
        
        if (!trackingCode) {
            safeToast('Veuillez saisir un code de suivi.', 'error');
            return;
        }

        // Cacher les résultats précédents
        verificationResults.style.display = 'none';
        verificationError.style.display = 'none';
        
        safeShowLoader(verifyBtn);

        try {
            const response = await signSecureAPI.verifyDocument(trackingCode);
            
            if (response.success && response.data) {
                // Vérifier si c'est un document ou un retrace
                if (response.data.document) {
                    // C'est un document signé - Remettre les labels par défaut
                    document.getElementById('docNameLabel').textContent = 'Nom du document :';
                    document.getElementById('signerNameLabel').textContent = 'Signataire :';
                    document.getElementById('signerEmailLabel').textContent = 'Email du signataire :';
                    document.getElementById('signatureDateLabel').textContent = 'Date de signature :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                    
                    // Afficher les données du document
                    document.getElementById('docName').textContent = response.data.document.original_filename || 'N/A';
                    document.getElementById('signerName').textContent = response.data.signer?.name || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.signer?.email || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.document.signed_at ? new Date(response.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.document.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour document
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadAuthenticDocument(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = response.data.document.original_filename || 'document_authentique.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${response.data.document.tracking_code || trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Document vérifié avec succès !', 'success');
                    
                } else if (response.type === 'physical_retrace') {
                    // C'est un retrace physique - Mettre à jour les labels
                    document.getElementById('docNameLabel').textContent = 'Objet du document :';
                    document.getElementById('signerNameLabel').textContent = 'Référence :';
                    document.getElementById('signerEmailLabel').textContent = 'Date :';
                    document.getElementById('signatureDateLabel').textContent = 'Notes :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                    
                    // Afficher les données du retrace
                    document.getElementById('docName').textContent = response.data.subject || 'N/A';
                    document.getElementById('signerName').textContent = response.data.reference || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.date || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.notes || 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour retrace
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `retrace_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('PDF de retrace téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Retrace physique vérifié avec succès !', 'success');
                }
                
                verificationResults.style.display = 'block';
                
            } else {
                verificationError.style.display = 'block';
                console.error('Réponse invalide:', response);
                document.getElementById('errorMessage').textContent = response.message || 'Document non trouvé ou code invalide.';
            }

        } catch (error) {
            verificationError.style.display = 'block';
            console.error('Erreur vérification:', error);
            document.getElementById('errorMessage').textContent = error.message || 'Erreur lors de la vérification.';
        } finally {
            safeHideLoader(verifyBtn, originalBtnText);
        }
    });

    // Formater automatiquement le code de suivi
    document.getElementById('trackingCode').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Script pour le retrace physique
    const retraceForm = document.getElementById('retraceForm');
    const retraceOtpForm = document.getElementById('retraceOtpForm');
    const retraceStep1 = document.getElementById('retraceStep1');
    const retraceStep2 = document.getElementById('retraceStep2');
    const retraceStep3 = document.getElementById('retraceStep3');
    const retraceError = document.getElementById('retraceError');
    let retracePayload = null;

    retraceForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const payload = {
            date: document.getElementById('retraceDate').value,
            reference: document.getElementById('retraceReference').value,
            subject: document.getElementById('retraceSubject').value,
            notes: document.getElementById('retraceNotes').value
        };

        retracePayload = payload;
        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceRequestOtp(payload);
            
            if (response.success) {
                retraceStep1.style.display = 'none';
                retraceStep2.style.display = 'block';
                safeToast('Code de vérification envoyé !', 'success');
            } else {
                throw new Error(response.message || 'Erreur lors de l\'envoi du code');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de l\'envoi du code', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceSubmitBtn'), '<i class="fas fa-paper-plane me-2"></i>Demander le code de vérification');
        }
    });

    retraceOtpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const code = document.getElementById('retraceOtpCode').value.trim();
        
        if (!code) {
            safeToast('Veuillez saisir le code de vérification', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceOtpSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceVerifyOtp(code, retracePayload);
            
            if (response.success && response.data) {
                retraceStep2.style.display = 'none';
                retraceStep3.style.display = 'block';
                
                // Afficher les informations du retrace créé
                document.getElementById('retraceTrackingCode').textContent = response.data.tracking_code || 'N/A';
                document.getElementById('retraceCreatedDate').textContent = response.data.date ? new Date(response.data.date).toLocaleDateString('fr-FR') : 'N/A';
                document.getElementById('retraceCreatedReference').textContent = response.data.reference || 'N/A';
                document.getElementById('retraceCreatedSubject').textContent = response.data.subject || 'N/A';
                
                // Configurer le bouton de téléchargement
                document.getElementById('retraceDownloadBtn').onclick = async function() {
                    try {
                        const trackingCode = response.data.tracking_code;
                        if (!trackingCode) {
                            safeToast('Code de suivi manquant', 'error');
                            return;
                        }
                        
                        const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `retrace_${trackingCode}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        safeToast('PDF téléchargé avec succès !', 'success');
                    } catch (error) {
                        safeToast('Erreur lors du téléchargement', 'error');
                    }
                };
                
                safeToast('Retrace créé avec succès !', 'success');
            } else {
                throw new Error(response.message || 'Code de vérification invalide');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de la vérification', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceOtpSubmitBtn'), '<i class="fas fa-check me-2"></i>Vérifier et créer le retrace');
        }
    });

    // Bouton retour
    document.getElementById('retraceBackBtn').addEventListener('click', function() {
        retraceStep2.style.display = 'none';
        retraceStep1.style.display = 'block';
        retraceError.style.display = 'none';
    });

    // Réinitialiser le modal à la fermeture
    document.getElementById('retraceModal').addEventListener('hidden.bs.modal', function() {
        retraceStep1.style.display = 'block';
        retraceStep2.style.display = 'none';
        retraceStep3.style.display = 'none';
        retraceError.style.display = 'none';
        retraceForm.reset();
        retraceOtpForm.reset();
        retracePayload = null;
    });

    // Téléversement via API
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadFile = document.getElementById('uploadFile');
    const originalUploadBtnText = uploadBtn ? uploadBtn.innerHTML : '';

    if (uploadForm && uploadBtn && uploadFile) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            uploadError.style.display = 'none';
            if (!uploadFile.files || uploadFile.files.length === 0) {
                safeToast('Veuillez choisir un fichier PDF.', 'error');
                return;
            }
            safeShowLoader(uploadBtn);
            try {
                const res = await signSecureAPI.uploadDocument(uploadFile.files[0], 'email');
                safeToast('Document téléversé avec succès! Vérification OTP envoyée.', 'success');
                const docId = res?.data?.id || res?.data?.document?.id;
                if (docId) {
                    window.location.href = `/documents/${docId}/verify-otp`;
                    return;
                }
                const modalEl = document.getElementById('uploadModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
                setTimeout(() => window.location.reload(), 800);
            } catch (err) {
                uploadError.textContent = err.message || 'Erreur lors du téléversement';
                uploadError.style.display = 'block';
            } finally {
                safeHideLoader(uploadBtn, originalUploadBtnText);
            }
        });
    }

    // Global reload control for profile modal
    let shouldReloadAfterProfileModal = false;

    // Profile info update
    const profileInfoForm = document.getElementById('profileInfoForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileInfoError = document.getElementById('profileInfoError');
    if (profileInfoForm && saveProfileBtn) {
        profileInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profileInfoError.style.display = 'none';
            const name = document.getElementById('profileName').value.trim();
            const phone = document.getElementById('profilePhone').value.trim();
            if (!name) { safeToast('Le nom est requis', 'error'); return; }
            const originalSaveText = saveProfileBtn.innerHTML;
            safeShowLoader(saveProfileBtn);
            try {
                await signSecureAPI.updateProfile({ name, phone });
                safeToast('Profil mis à jour', 'success');
                // Ferme le modal puis recharge
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profileInfoError.textContent = err.message || 'Erreur lors de la mise à jour du profil';
                profileInfoError.style.display = 'block';
            } finally {
                safeHideLoader(saveProfileBtn, originalSaveText);
            }
        });
    }

    // Change password
    const profilePasswordForm = document.getElementById('profilePasswordForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const profilePasswordError = document.getElementById('profilePasswordError');
    if (profilePasswordForm && changePasswordBtn) {
        profilePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profilePasswordError.style.display = 'none';
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            if (newPassword !== confirmNewPassword) {
                profilePasswordError.textContent = 'La confirmation ne correspond pas';
                profilePasswordError.style.display = 'block';
                return;
            }
            const originalChangeText = changePasswordBtn.innerHTML;
            safeShowLoader(changePasswordBtn);
            try {
                await signSecureAPI.changePassword(currentPassword, newPassword, confirmNewPassword);
                safeToast('Mot de passe mis à jour', 'success');
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profilePasswordError.textContent = err.message || 'Erreur lors du changement de mot de passe';
                profilePasswordError.style.display = 'block';
            } finally {
                safeHideLoader(changePasswordBtn, originalChangeText);
            }
        });
    }

    // When profile modal actually finishes closing, trigger reload if requested
    const profileModalEl = document.getElementById('profileModal');
    if (profileModalEl) {
        profileModalEl.addEventListener('hidden.bs.modal', function() {
            if (shouldReloadAfterProfileModal) {
                shouldReloadAfterProfileModal = false;
                window.location.reload();
            }
        });
    }
});
</script>
@endpush



    border-radius: 0.375rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-signed {
    background-color: #198754;
    color: #fff;
}

.status-uploaded {
    background-color: #0dcaf0;
    color: #000;
}

#trackingCode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}
</style>
@endpush

@push("scripts")
<script>
// Fonction utilitaire pour les toasts sécurisés
function safeToast(message, type = 'info') {
    if (window.SignSecureUI && window.SignSecureUI.showToast) {
        SignSecureUI.showToast(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction utilitaire pour les loaders sécurisés
function safeShowLoader(element) {
    if (window.SignSecureUI && window.SignSecureUI.showLoader) {
        SignSecureUI.showLoader(element);
    }
}

function safeHideLoader(element, originalText) {
    if (window.SignSecureUI && window.SignSecureUI.hideLoader) {
        SignSecureUI.hideLoader(element, originalText);
    }
}

// Script pour la vérification de documents
document.addEventListener('DOMContentLoaded', function() {
    // Laisser Bootstrap gérer l'ouverture des modales via les attributs data-bs-*
    const verificationModalEl = document.getElementById('verificationModal');
    const retraceModalEl = document.getElementById('retraceModal');
    const verificationForm = document.getElementById('verificationForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const verificationResults = document.getElementById('verificationResults');
    const verificationError = document.getElementById('verificationError');
    const originalBtnText = verifyBtn.innerHTML;

    verificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const trackingCode = document.getElementById('trackingCode').value.trim().toUpperCase();
        
        if (!trackingCode) {
            safeToast('Veuillez saisir un code de suivi.', 'error');
            return;
        }

        // Cacher les résultats précédents
        verificationResults.style.display = 'none';
        verificationError.style.display = 'none';
        
        safeShowLoader(verifyBtn);

        try {
            const response = await signSecureAPI.verifyDocument(trackingCode);
            
            if (response.success && response.data) {
                // Vérifier si c'est un document ou un retrace
                if (response.data.document) {
                    // C'est un document signé - Remettre les labels par défaut
                    document.getElementById('docNameLabel').textContent = 'Nom du document :';
                    document.getElementById('signerNameLabel').textContent = 'Signataire :';
                    document.getElementById('signerEmailLabel').textContent = 'Email du signataire :';
                    document.getElementById('signatureDateLabel').textContent = 'Date de signature :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                    
                    // Afficher les données du document
                    document.getElementById('docName').textContent = response.data.document.original_filename || 'N/A';
                    document.getElementById('signerName').textContent = response.data.signer?.name || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.signer?.email || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.document.signed_at ? new Date(response.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.document.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour document
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadAuthenticDocument(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = response.data.document.original_filename || 'document_authentique.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${response.data.document.tracking_code || trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Document vérifié avec succès !', 'success');
                    
                } else if (response.type === 'physical_retrace') {
                    // C'est un retrace physique - Mettre à jour les labels
                    document.getElementById('docNameLabel').textContent = 'Objet du document :';
                    document.getElementById('signerNameLabel').textContent = 'Référence :';
                    document.getElementById('signerEmailLabel').textContent = 'Date :';
                    document.getElementById('signatureDateLabel').textContent = 'Notes :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                    
                    // Afficher les données du retrace
                    document.getElementById('docName').textContent = response.data.subject || 'N/A';
                    document.getElementById('signerName').textContent = response.data.reference || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.date || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.notes || 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour retrace
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `retrace_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('PDF de retrace téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Retrace physique vérifié avec succès !', 'success');
                }
                
                verificationResults.style.display = 'block';
                
            } else {
                verificationError.style.display = 'block';
                console.error('Réponse invalide:', response);
                document.getElementById('errorMessage').textContent = response.message || 'Document non trouvé ou code invalide.';
            }

        } catch (error) {
            verificationError.style.display = 'block';
            console.error('Erreur vérification:', error);
            document.getElementById('errorMessage').textContent = error.message || 'Erreur lors de la vérification.';
        } finally {
            safeHideLoader(verifyBtn, originalBtnText);
        }
    });

    // Formater automatiquement le code de suivi
    document.getElementById('trackingCode').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Script pour le retrace physique
    const retraceForm = document.getElementById('retraceForm');
    const retraceOtpForm = document.getElementById('retraceOtpForm');
    const retraceStep1 = document.getElementById('retraceStep1');
    const retraceStep2 = document.getElementById('retraceStep2');
    const retraceStep3 = document.getElementById('retraceStep3');
    const retraceError = document.getElementById('retraceError');
    let retracePayload = null;

    retraceForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const payload = {
            date: document.getElementById('retraceDate').value,
            reference: document.getElementById('retraceReference').value,
            subject: document.getElementById('retraceSubject').value,
            notes: document.getElementById('retraceNotes').value
        };

        retracePayload = payload;
        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceRequestOtp(payload);
            
            if (response.success) {
                retraceStep1.style.display = 'none';
                retraceStep2.style.display = 'block';
                safeToast('Code de vérification envoyé !', 'success');
            } else {
                throw new Error(response.message || 'Erreur lors de l\'envoi du code');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de l\'envoi du code', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceSubmitBtn'), '<i class="fas fa-paper-plane me-2"></i>Demander le code de vérification');
        }
    });

    retraceOtpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const code = document.getElementById('retraceOtpCode').value.trim();
        
        if (!code) {
            safeToast('Veuillez saisir le code de vérification', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceOtpSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceVerifyOtp(code, retracePayload);
            
            if (response.success && response.data) {
                retraceStep2.style.display = 'none';
                retraceStep3.style.display = 'block';
                
                // Afficher les informations du retrace créé
                document.getElementById('retraceTrackingCode').textContent = response.data.tracking_code || 'N/A';
                document.getElementById('retraceCreatedDate').textContent = response.data.date ? new Date(response.data.date).toLocaleDateString('fr-FR') : 'N/A';
                document.getElementById('retraceCreatedReference').textContent = response.data.reference || 'N/A';
                document.getElementById('retraceCreatedSubject').textContent = response.data.subject || 'N/A';
                
                // Configurer le bouton de téléchargement
                document.getElementById('retraceDownloadBtn').onclick = async function() {
                    try {
                        const trackingCode = response.data.tracking_code;
                        if (!trackingCode) {
                            safeToast('Code de suivi manquant', 'error');
                            return;
                        }
                        
                        const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `retrace_${trackingCode}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        safeToast('PDF téléchargé avec succès !', 'success');
                    } catch (error) {
                        safeToast('Erreur lors du téléchargement', 'error');
                    }
                };
                
                safeToast('Retrace créé avec succès !', 'success');
            } else {
                throw new Error(response.message || 'Code de vérification invalide');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de la vérification', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceOtpSubmitBtn'), '<i class="fas fa-check me-2"></i>Vérifier et créer le retrace');
        }
    });

    // Bouton retour
    document.getElementById('retraceBackBtn').addEventListener('click', function() {
        retraceStep2.style.display = 'none';
        retraceStep1.style.display = 'block';
        retraceError.style.display = 'none';
    });

    // Réinitialiser le modal à la fermeture
    document.getElementById('retraceModal').addEventListener('hidden.bs.modal', function() {
        retraceStep1.style.display = 'block';
        retraceStep2.style.display = 'none';
        retraceStep3.style.display = 'none';
        retraceError.style.display = 'none';
        retraceForm.reset();
        retraceOtpForm.reset();
        retracePayload = null;
    });

    // Téléversement via API
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadFile = document.getElementById('uploadFile');
    const originalUploadBtnText = uploadBtn ? uploadBtn.innerHTML : '';

    if (uploadForm && uploadBtn && uploadFile) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            uploadError.style.display = 'none';
            if (!uploadFile.files || uploadFile.files.length === 0) {
                safeToast('Veuillez choisir un fichier PDF.', 'error');
                return;
            }
            safeShowLoader(uploadBtn);
            try {
                const res = await signSecureAPI.uploadDocument(uploadFile.files[0], 'email');
                safeToast('Document téléversé avec succès! Vérification OTP envoyée.', 'success');
                const docId = res?.data?.id || res?.data?.document?.id;
                if (docId) {
                    window.location.href = `/documents/${docId}/verify-otp`;
                    return;
                }
                const modalEl = document.getElementById('uploadModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
                setTimeout(() => window.location.reload(), 800);
            } catch (err) {
                uploadError.textContent = err.message || 'Erreur lors du téléversement';
                uploadError.style.display = 'block';
            } finally {
                safeHideLoader(uploadBtn, originalUploadBtnText);
            }
        });
    }

    // Global reload control for profile modal
    let shouldReloadAfterProfileModal = false;

    // Profile info update
    const profileInfoForm = document.getElementById('profileInfoForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileInfoError = document.getElementById('profileInfoError');
    if (profileInfoForm && saveProfileBtn) {
        profileInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profileInfoError.style.display = 'none';
            const name = document.getElementById('profileName').value.trim();
            const phone = document.getElementById('profilePhone').value.trim();
            if (!name) { safeToast('Le nom est requis', 'error'); return; }
            const originalSaveText = saveProfileBtn.innerHTML;
            safeShowLoader(saveProfileBtn);
            try {
                await signSecureAPI.updateProfile({ name, phone });
                safeToast('Profil mis à jour', 'success');
                // Ferme le modal puis recharge
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profileInfoError.textContent = err.message || 'Erreur lors de la mise à jour du profil';
                profileInfoError.style.display = 'block';
            } finally {
                safeHideLoader(saveProfileBtn, originalSaveText);
            }
        });
    }

    // Change password
    const profilePasswordForm = document.getElementById('profilePasswordForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const profilePasswordError = document.getElementById('profilePasswordError');
    if (profilePasswordForm && changePasswordBtn) {
        profilePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profilePasswordError.style.display = 'none';
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            if (newPassword !== confirmNewPassword) {
                profilePasswordError.textContent = 'La confirmation ne correspond pas';
                profilePasswordError.style.display = 'block';
                return;
            }
            const originalChangeText = changePasswordBtn.innerHTML;
            safeShowLoader(changePasswordBtn);
            try {
                await signSecureAPI.changePassword(currentPassword, newPassword, confirmNewPassword);
                safeToast('Mot de passe mis à jour', 'success');
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profilePasswordError.textContent = err.message || 'Erreur lors du changement de mot de passe';
                profilePasswordError.style.display = 'block';
            } finally {
                safeHideLoader(changePasswordBtn, originalChangeText);
            }
        });
    }

    // When profile modal actually finishes closing, trigger reload if requested
    const profileModalEl = document.getElementById('profileModal');
    if (profileModalEl) {
        profileModalEl.addEventListener('hidden.bs.modal', function() {
            if (shouldReloadAfterProfileModal) {
                shouldReloadAfterProfileModal = false;
                window.location.reload();
            }
        });
    }
});
</script>
@endpush



    border-radius: 0.375rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-signed {
    background-color: #198754;
    color: #fff;
}

.status-uploaded {
    background-color: #0dcaf0;
    color: #000;
}

#trackingCode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}
</style>
@endpush

@push("scripts")
<script>
// Fonction utilitaire pour les toasts sécurisés
function safeToast(message, type = 'info') {
    if (window.SignSecureUI && window.SignSecureUI.showToast) {
        SignSecureUI.showToast(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction utilitaire pour les loaders sécurisés
function safeShowLoader(element) {
    if (window.SignSecureUI && window.SignSecureUI.showLoader) {
        SignSecureUI.showLoader(element);
    }
}

function safeHideLoader(element, originalText) {
    if (window.SignSecureUI && window.SignSecureUI.hideLoader) {
        SignSecureUI.hideLoader(element, originalText);
    }
}

// Script pour la vérification de documents
document.addEventListener('DOMContentLoaded', function() {
    // Laisser Bootstrap gérer l'ouverture des modales via les attributs data-bs-*
    const verificationModalEl = document.getElementById('verificationModal');
    const retraceModalEl = document.getElementById('retraceModal');
    const verificationForm = document.getElementById('verificationForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const verificationResults = document.getElementById('verificationResults');
    const verificationError = document.getElementById('verificationError');
    const originalBtnText = verifyBtn.innerHTML;

    verificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const trackingCode = document.getElementById('trackingCode').value.trim().toUpperCase();
        
        if (!trackingCode) {
            safeToast('Veuillez saisir un code de suivi.', 'error');
            return;
        }

        // Cacher les résultats précédents
        verificationResults.style.display = 'none';
        verificationError.style.display = 'none';
        
        safeShowLoader(verifyBtn);

        try {
            const response = await signSecureAPI.verifyDocument(trackingCode);
            
            if (response.success && response.data) {
                // Vérifier si c'est un document ou un retrace
                if (response.data.document) {
                    // C'est un document signé - Remettre les labels par défaut
                    document.getElementById('docNameLabel').textContent = 'Nom du document :';
                    document.getElementById('signerNameLabel').textContent = 'Signataire :';
                    document.getElementById('signerEmailLabel').textContent = 'Email du signataire :';
                    document.getElementById('signatureDateLabel').textContent = 'Date de signature :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                    
                    // Afficher les données du document
                    document.getElementById('docName').textContent = response.data.document.original_filename || 'N/A';
                    document.getElementById('signerName').textContent = response.data.signer?.name || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.signer?.email || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.document.signed_at ? new Date(response.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.document.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour document
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadAuthenticDocument(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = response.data.document.original_filename || 'document_authentique.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${response.data.document.tracking_code || trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Document vérifié avec succès !', 'success');
                    
                } else if (response.type === 'physical_retrace') {
                    // C'est un retrace physique - Mettre à jour les labels
                    document.getElementById('docNameLabel').textContent = 'Objet du document :';
                    document.getElementById('signerNameLabel').textContent = 'Référence :';
                    document.getElementById('signerEmailLabel').textContent = 'Date :';
                    document.getElementById('signatureDateLabel').textContent = 'Notes :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                    
                    // Afficher les données du retrace
                    document.getElementById('docName').textContent = response.data.subject || 'N/A';
                    document.getElementById('signerName').textContent = response.data.reference || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.date || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.notes || 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour retrace
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `retrace_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('PDF de retrace téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Retrace physique vérifié avec succès !', 'success');
                }
                
                verificationResults.style.display = 'block';
                
            } else {
                verificationError.style.display = 'block';
                console.error('Réponse invalide:', response);
                document.getElementById('errorMessage').textContent = response.message || 'Document non trouvé ou code invalide.';
            }

        } catch (error) {
            verificationError.style.display = 'block';
            console.error('Erreur vérification:', error);
            document.getElementById('errorMessage').textContent = error.message || 'Erreur lors de la vérification.';
        } finally {
            safeHideLoader(verifyBtn, originalBtnText);
        }
    });

    // Formater automatiquement le code de suivi
    document.getElementById('trackingCode').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Script pour le retrace physique
    const retraceForm = document.getElementById('retraceForm');
    const retraceOtpForm = document.getElementById('retraceOtpForm');
    const retraceStep1 = document.getElementById('retraceStep1');
    const retraceStep2 = document.getElementById('retraceStep2');
    const retraceStep3 = document.getElementById('retraceStep3');
    const retraceError = document.getElementById('retraceError');
    let retracePayload = null;

    retraceForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const payload = {
            date: document.getElementById('retraceDate').value,
            reference: document.getElementById('retraceReference').value,
            subject: document.getElementById('retraceSubject').value,
            notes: document.getElementById('retraceNotes').value
        };

        retracePayload = payload;
        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceRequestOtp(payload);
            
            if (response.success) {
                retraceStep1.style.display = 'none';
                retraceStep2.style.display = 'block';
                safeToast('Code de vérification envoyé !', 'success');
            } else {
                throw new Error(response.message || 'Erreur lors de l\'envoi du code');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de l\'envoi du code', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceSubmitBtn'), '<i class="fas fa-paper-plane me-2"></i>Demander le code de vérification');
        }
    });

    retraceOtpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const code = document.getElementById('retraceOtpCode').value.trim();
        
        if (!code) {
            safeToast('Veuillez saisir le code de vérification', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceOtpSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceVerifyOtp(code, retracePayload);
            
            if (response.success && response.data) {
                retraceStep2.style.display = 'none';
                retraceStep3.style.display = 'block';
                
                // Afficher les informations du retrace créé
                document.getElementById('retraceTrackingCode').textContent = response.data.tracking_code || 'N/A';
                document.getElementById('retraceCreatedDate').textContent = response.data.date ? new Date(response.data.date).toLocaleDateString('fr-FR') : 'N/A';
                document.getElementById('retraceCreatedReference').textContent = response.data.reference || 'N/A';
                document.getElementById('retraceCreatedSubject').textContent = response.data.subject || 'N/A';
                
                // Configurer le bouton de téléchargement
                document.getElementById('retraceDownloadBtn').onclick = async function() {
                    try {
                        const trackingCode = response.data.tracking_code;
                        if (!trackingCode) {
                            safeToast('Code de suivi manquant', 'error');
                            return;
                        }
                        
                        const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `retrace_${trackingCode}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        safeToast('PDF téléchargé avec succès !', 'success');
                    } catch (error) {
                        safeToast('Erreur lors du téléchargement', 'error');
                    }
                };
                
                safeToast('Retrace créé avec succès !', 'success');
            } else {
                throw new Error(response.message || 'Code de vérification invalide');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de la vérification', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceOtpSubmitBtn'), '<i class="fas fa-check me-2"></i>Vérifier et créer le retrace');
        }
    });

    // Bouton retour
    document.getElementById('retraceBackBtn').addEventListener('click', function() {
        retraceStep2.style.display = 'none';
        retraceStep1.style.display = 'block';
        retraceError.style.display = 'none';
    });

    // Réinitialiser le modal à la fermeture
    document.getElementById('retraceModal').addEventListener('hidden.bs.modal', function() {
        retraceStep1.style.display = 'block';
        retraceStep2.style.display = 'none';
        retraceStep3.style.display = 'none';
        retraceError.style.display = 'none';
        retraceForm.reset();
        retraceOtpForm.reset();
        retracePayload = null;
    });

    // Téléversement via API
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadFile = document.getElementById('uploadFile');
    const originalUploadBtnText = uploadBtn ? uploadBtn.innerHTML : '';

    if (uploadForm && uploadBtn && uploadFile) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            uploadError.style.display = 'none';
            if (!uploadFile.files || uploadFile.files.length === 0) {
                safeToast('Veuillez choisir un fichier PDF.', 'error');
                return;
            }
            safeShowLoader(uploadBtn);
            try {
                const res = await signSecureAPI.uploadDocument(uploadFile.files[0], 'email');
                safeToast('Document téléversé avec succès! Vérification OTP envoyée.', 'success');
                const docId = res?.data?.id || res?.data?.document?.id;
                if (docId) {
                    window.location.href = `/documents/${docId}/verify-otp`;
                    return;
                }
                const modalEl = document.getElementById('uploadModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
                setTimeout(() => window.location.reload(), 800);
            } catch (err) {
                uploadError.textContent = err.message || 'Erreur lors du téléversement';
                uploadError.style.display = 'block';
            } finally {
                safeHideLoader(uploadBtn, originalUploadBtnText);
            }
        });
    }

    // Global reload control for profile modal
    let shouldReloadAfterProfileModal = false;

    // Profile info update
    const profileInfoForm = document.getElementById('profileInfoForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileInfoError = document.getElementById('profileInfoError');
    if (profileInfoForm && saveProfileBtn) {
        profileInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profileInfoError.style.display = 'none';
            const name = document.getElementById('profileName').value.trim();
            const phone = document.getElementById('profilePhone').value.trim();
            if (!name) { safeToast('Le nom est requis', 'error'); return; }
            const originalSaveText = saveProfileBtn.innerHTML;
            safeShowLoader(saveProfileBtn);
            try {
                await signSecureAPI.updateProfile({ name, phone });
                safeToast('Profil mis à jour', 'success');
                // Ferme le modal puis recharge
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profileInfoError.textContent = err.message || 'Erreur lors de la mise à jour du profil';
                profileInfoError.style.display = 'block';
            } finally {
                safeHideLoader(saveProfileBtn, originalSaveText);
            }
        });
    }

    // Change password
    const profilePasswordForm = document.getElementById('profilePasswordForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const profilePasswordError = document.getElementById('profilePasswordError');
    if (profilePasswordForm && changePasswordBtn) {
        profilePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profilePasswordError.style.display = 'none';
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            if (newPassword !== confirmNewPassword) {
                profilePasswordError.textContent = 'La confirmation ne correspond pas';
                profilePasswordError.style.display = 'block';
                return;
            }
            const originalChangeText = changePasswordBtn.innerHTML;
            safeShowLoader(changePasswordBtn);
            try {
                await signSecureAPI.changePassword(currentPassword, newPassword, confirmNewPassword);
                safeToast('Mot de passe mis à jour', 'success');
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profilePasswordError.textContent = err.message || 'Erreur lors du changement de mot de passe';
                profilePasswordError.style.display = 'block';
            } finally {
                safeHideLoader(changePasswordBtn, originalChangeText);
            }
        });
    }

    // When profile modal actually finishes closing, trigger reload if requested
    const profileModalEl = document.getElementById('profileModal');
    if (profileModalEl) {
        profileModalEl.addEventListener('hidden.bs.modal', function() {
            if (shouldReloadAfterProfileModal) {
                shouldReloadAfterProfileModal = false;
                window.location.reload();
            }
        });
    }
});
</script>
@endpush



    border-radius: 0.375rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-signed {
    background-color: #198754;
    color: #fff;
}

.status-uploaded {
    background-color: #0dcaf0;
    color: #000;
}

#trackingCode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}
</style>
@endpush

@push("scripts")
<script>
// Fonction utilitaire pour les toasts sécurisés
function safeToast(message, type = 'info') {
    if (window.SignSecureUI && window.SignSecureUI.showToast) {
        SignSecureUI.showToast(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction utilitaire pour les loaders sécurisés
function safeShowLoader(element) {
    if (window.SignSecureUI && window.SignSecureUI.showLoader) {
        SignSecureUI.showLoader(element);
    }
}

function safeHideLoader(element, originalText) {
    if (window.SignSecureUI && window.SignSecureUI.hideLoader) {
        SignSecureUI.hideLoader(element, originalText);
    }
}

// Script pour la vérification de documents
document.addEventListener('DOMContentLoaded', function() {
    // Laisser Bootstrap gérer l'ouverture des modales via les attributs data-bs-*
    const verificationModalEl = document.getElementById('verificationModal');
    const retraceModalEl = document.getElementById('retraceModal');
    const verificationForm = document.getElementById('verificationForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const verificationResults = document.getElementById('verificationResults');
    const verificationError = document.getElementById('verificationError');
    const originalBtnText = verifyBtn.innerHTML;

    verificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const trackingCode = document.getElementById('trackingCode').value.trim().toUpperCase();
        
        if (!trackingCode) {
            safeToast('Veuillez saisir un code de suivi.', 'error');
            return;
        }

        // Cacher les résultats précédents
        verificationResults.style.display = 'none';
        verificationError.style.display = 'none';
        
        safeShowLoader(verifyBtn);

        try {
            const response = await signSecureAPI.verifyDocument(trackingCode);
            
            if (response.success && response.data) {
                // Vérifier si c'est un document ou un retrace
                if (response.data.document) {
                    // C'est un document signé - Remettre les labels par défaut
                    document.getElementById('docNameLabel').textContent = 'Nom du document :';
                    document.getElementById('signerNameLabel').textContent = 'Signataire :';
                    document.getElementById('signerEmailLabel').textContent = 'Email du signataire :';
                    document.getElementById('signatureDateLabel').textContent = 'Date de signature :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                    
                    // Afficher les données du document
                    document.getElementById('docName').textContent = response.data.document.original_filename || 'N/A';
                    document.getElementById('signerName').textContent = response.data.signer?.name || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.signer?.email || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.document.signed_at ? new Date(response.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.document.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour document
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadAuthenticDocument(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = response.data.document.original_filename || 'document_authentique.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${response.data.document.tracking_code || trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Document vérifié avec succès !', 'success');
                    
                } else if (response.type === 'physical_retrace') {
                    // C'est un retrace physique - Mettre à jour les labels
                    document.getElementById('docNameLabel').textContent = 'Objet du document :';
                    document.getElementById('signerNameLabel').textContent = 'Référence :';
                    document.getElementById('signerEmailLabel').textContent = 'Date :';
                    document.getElementById('signatureDateLabel').textContent = 'Notes :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                    
                    // Afficher les données du retrace
                    document.getElementById('docName').textContent = response.data.subject || 'N/A';
                    document.getElementById('signerName').textContent = response.data.reference || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.date || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.notes || 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour retrace
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `retrace_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('PDF de retrace téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Retrace physique vérifié avec succès !', 'success');
                }
                
                verificationResults.style.display = 'block';
                
            } else {
                verificationError.style.display = 'block';
                console.error('Réponse invalide:', response);
                document.getElementById('errorMessage').textContent = response.message || 'Document non trouvé ou code invalide.';
            }

        } catch (error) {
            verificationError.style.display = 'block';
            console.error('Erreur vérification:', error);
            document.getElementById('errorMessage').textContent = error.message || 'Erreur lors de la vérification.';
        } finally {
            safeHideLoader(verifyBtn, originalBtnText);
        }
    });

    // Formater automatiquement le code de suivi
    document.getElementById('trackingCode').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Script pour le retrace physique
    const retraceForm = document.getElementById('retraceForm');
    const retraceOtpForm = document.getElementById('retraceOtpForm');
    const retraceStep1 = document.getElementById('retraceStep1');
    const retraceStep2 = document.getElementById('retraceStep2');
    const retraceStep3 = document.getElementById('retraceStep3');
    const retraceError = document.getElementById('retraceError');
    let retracePayload = null;

    retraceForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const payload = {
            date: document.getElementById('retraceDate').value,
            reference: document.getElementById('retraceReference').value,
            subject: document.getElementById('retraceSubject').value,
            notes: document.getElementById('retraceNotes').value
        };

        retracePayload = payload;
        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceRequestOtp(payload);
            
            if (response.success) {
                retraceStep1.style.display = 'none';
                retraceStep2.style.display = 'block';
                safeToast('Code de vérification envoyé !', 'success');
            } else {
                throw new Error(response.message || 'Erreur lors de l\'envoi du code');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de l\'envoi du code', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceSubmitBtn'), '<i class="fas fa-paper-plane me-2"></i>Demander le code de vérification');
        }
    });

    retraceOtpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const code = document.getElementById('retraceOtpCode').value.trim();
        
        if (!code) {
            safeToast('Veuillez saisir le code de vérification', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceOtpSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceVerifyOtp(code, retracePayload);
            
            if (response.success && response.data) {
                retraceStep2.style.display = 'none';
                retraceStep3.style.display = 'block';
                
                // Afficher les informations du retrace créé
                document.getElementById('retraceTrackingCode').textContent = response.data.tracking_code || 'N/A';
                document.getElementById('retraceCreatedDate').textContent = response.data.date ? new Date(response.data.date).toLocaleDateString('fr-FR') : 'N/A';
                document.getElementById('retraceCreatedReference').textContent = response.data.reference || 'N/A';
                document.getElementById('retraceCreatedSubject').textContent = response.data.subject || 'N/A';
                
                // Configurer le bouton de téléchargement
                document.getElementById('retraceDownloadBtn').onclick = async function() {
                    try {
                        const trackingCode = response.data.tracking_code;
                        if (!trackingCode) {
                            safeToast('Code de suivi manquant', 'error');
                            return;
                        }
                        
                        const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `retrace_${trackingCode}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        safeToast('PDF téléchargé avec succès !', 'success');
                    } catch (error) {
                        safeToast('Erreur lors du téléchargement', 'error');
                    }
                };
                
                safeToast('Retrace créé avec succès !', 'success');
            } else {
                throw new Error(response.message || 'Code de vérification invalide');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de la vérification', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceOtpSubmitBtn'), '<i class="fas fa-check me-2"></i>Vérifier et créer le retrace');
        }
    });

    // Bouton retour
    document.getElementById('retraceBackBtn').addEventListener('click', function() {
        retraceStep2.style.display = 'none';
        retraceStep1.style.display = 'block';
        retraceError.style.display = 'none';
    });

    // Réinitialiser le modal à la fermeture
    document.getElementById('retraceModal').addEventListener('hidden.bs.modal', function() {
        retraceStep1.style.display = 'block';
        retraceStep2.style.display = 'none';
        retraceStep3.style.display = 'none';
        retraceError.style.display = 'none';
        retraceForm.reset();
        retraceOtpForm.reset();
        retracePayload = null;
    });

    // Téléversement via API
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadFile = document.getElementById('uploadFile');
    const originalUploadBtnText = uploadBtn ? uploadBtn.innerHTML : '';

    if (uploadForm && uploadBtn && uploadFile) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            uploadError.style.display = 'none';
            if (!uploadFile.files || uploadFile.files.length === 0) {
                safeToast('Veuillez choisir un fichier PDF.', 'error');
                return;
            }
            safeShowLoader(uploadBtn);
            try {
                const res = await signSecureAPI.uploadDocument(uploadFile.files[0], 'email');
                safeToast('Document téléversé avec succès! Vérification OTP envoyée.', 'success');
                const docId = res?.data?.id || res?.data?.document?.id;
                if (docId) {
                    window.location.href = `/documents/${docId}/verify-otp`;
                    return;
                }
                const modalEl = document.getElementById('uploadModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
                setTimeout(() => window.location.reload(), 800);
            } catch (err) {
                uploadError.textContent = err.message || 'Erreur lors du téléversement';
                uploadError.style.display = 'block';
            } finally {
                safeHideLoader(uploadBtn, originalUploadBtnText);
            }
        });
    }

    // Global reload control for profile modal
    let shouldReloadAfterProfileModal = false;

    // Profile info update
    const profileInfoForm = document.getElementById('profileInfoForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileInfoError = document.getElementById('profileInfoError');
    if (profileInfoForm && saveProfileBtn) {
        profileInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profileInfoError.style.display = 'none';
            const name = document.getElementById('profileName').value.trim();
            const phone = document.getElementById('profilePhone').value.trim();
            if (!name) { safeToast('Le nom est requis', 'error'); return; }
            const originalSaveText = saveProfileBtn.innerHTML;
            safeShowLoader(saveProfileBtn);
            try {
                await signSecureAPI.updateProfile({ name, phone });
                safeToast('Profil mis à jour', 'success');
                // Ferme le modal puis recharge
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profileInfoError.textContent = err.message || 'Erreur lors de la mise à jour du profil';
                profileInfoError.style.display = 'block';
            } finally {
                safeHideLoader(saveProfileBtn, originalSaveText);
            }
        });
    }

    // Change password
    const profilePasswordForm = document.getElementById('profilePasswordForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const profilePasswordError = document.getElementById('profilePasswordError');
    if (profilePasswordForm && changePasswordBtn) {
        profilePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profilePasswordError.style.display = 'none';
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            if (newPassword !== confirmNewPassword) {
                profilePasswordError.textContent = 'La confirmation ne correspond pas';
                profilePasswordError.style.display = 'block';
                return;
            }
            const originalChangeText = changePasswordBtn.innerHTML;
            safeShowLoader(changePasswordBtn);
            try {
                await signSecureAPI.changePassword(currentPassword, newPassword, confirmNewPassword);
                safeToast('Mot de passe mis à jour', 'success');
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profilePasswordError.textContent = err.message || 'Erreur lors du changement de mot de passe';
                profilePasswordError.style.display = 'block';
            } finally {
                safeHideLoader(changePasswordBtn, originalChangeText);
            }
        });
    }

    // When profile modal actually finishes closing, trigger reload if requested
    const profileModalEl = document.getElementById('profileModal');
    if (profileModalEl) {
        profileModalEl.addEventListener('hidden.bs.modal', function() {
            if (shouldReloadAfterProfileModal) {
                shouldReloadAfterProfileModal = false;
                window.location.reload();
            }
        });
    }
});
</script>
@endpush



    border-radius: 0.375rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-signed {
    background-color: #198754;
    color: #fff;
}

.status-uploaded {
    background-color: #0dcaf0;
    color: #000;
}

#trackingCode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}
</style>
@endpush

@push("scripts")
<script>
// Fonction utilitaire pour les toasts sécurisés
function safeToast(message, type = 'info') {
    if (window.SignSecureUI && window.SignSecureUI.showToast) {
        SignSecureUI.showToast(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction utilitaire pour les loaders sécurisés
function safeShowLoader(element) {
    if (window.SignSecureUI && window.SignSecureUI.showLoader) {
        SignSecureUI.showLoader(element);
    }
}

function safeHideLoader(element, originalText) {
    if (window.SignSecureUI && window.SignSecureUI.hideLoader) {
        SignSecureUI.hideLoader(element, originalText);
    }
}

// Script pour la vérification de documents
document.addEventListener('DOMContentLoaded', function() {
    // Laisser Bootstrap gérer l'ouverture des modales via les attributs data-bs-*
    const verificationModalEl = document.getElementById('verificationModal');
    const retraceModalEl = document.getElementById('retraceModal');
    const verificationForm = document.getElementById('verificationForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const verificationResults = document.getElementById('verificationResults');
    const verificationError = document.getElementById('verificationError');
    const originalBtnText = verifyBtn.innerHTML;

    verificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const trackingCode = document.getElementById('trackingCode').value.trim().toUpperCase();
        
        if (!trackingCode) {
            safeToast('Veuillez saisir un code de suivi.', 'error');
            return;
        }

        // Cacher les résultats précédents
        verificationResults.style.display = 'none';
        verificationError.style.display = 'none';
        
        safeShowLoader(verifyBtn);

        try {
            const response = await signSecureAPI.verifyDocument(trackingCode);
            
            if (response.success && response.data) {
                // Vérifier si c'est un document ou un retrace
                if (response.data.document) {
                    // C'est un document signé - Remettre les labels par défaut
                    document.getElementById('docNameLabel').textContent = 'Nom du document :';
                    document.getElementById('signerNameLabel').textContent = 'Signataire :';
                    document.getElementById('signerEmailLabel').textContent = 'Email du signataire :';
                    document.getElementById('signatureDateLabel').textContent = 'Date de signature :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                    
                    // Afficher les données du document
                    document.getElementById('docName').textContent = response.data.document.original_filename || 'N/A';
                    document.getElementById('signerName').textContent = response.data.signer?.name || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.signer?.email || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.document.signed_at ? new Date(response.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.document.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour document
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadAuthenticDocument(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = response.data.document.original_filename || 'document_authentique.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${response.data.document.tracking_code || trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Document vérifié avec succès !', 'success');
                    
                } else if (response.type === 'physical_retrace') {
                    // C'est un retrace physique - Mettre à jour les labels
                    document.getElementById('docNameLabel').textContent = 'Objet du document :';
                    document.getElementById('signerNameLabel').textContent = 'Référence :';
                    document.getElementById('signerEmailLabel').textContent = 'Date :';
                    document.getElementById('signatureDateLabel').textContent = 'Notes :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                    
                    // Afficher les données du retrace
                    document.getElementById('docName').textContent = response.data.subject || 'N/A';
                    document.getElementById('signerName').textContent = response.data.reference || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.date || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.notes || 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour retrace
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `retrace_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('PDF de retrace téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Retrace physique vérifié avec succès !', 'success');
                }
                
                verificationResults.style.display = 'block';
                
            } else {
                verificationError.style.display = 'block';
                console.error('Réponse invalide:', response);
                document.getElementById('errorMessage').textContent = response.message || 'Document non trouvé ou code invalide.';
            }

        } catch (error) {
            verificationError.style.display = 'block';
            console.error('Erreur vérification:', error);
            document.getElementById('errorMessage').textContent = error.message || 'Erreur lors de la vérification.';
        } finally {
            safeHideLoader(verifyBtn, originalBtnText);
        }
    });

    // Formater automatiquement le code de suivi
    document.getElementById('trackingCode').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Script pour le retrace physique
    const retraceForm = document.getElementById('retraceForm');
    const retraceOtpForm = document.getElementById('retraceOtpForm');
    const retraceStep1 = document.getElementById('retraceStep1');
    const retraceStep2 = document.getElementById('retraceStep2');
    const retraceStep3 = document.getElementById('retraceStep3');
    const retraceError = document.getElementById('retraceError');
    let retracePayload = null;

    retraceForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const payload = {
            date: document.getElementById('retraceDate').value,
            reference: document.getElementById('retraceReference').value,
            subject: document.getElementById('retraceSubject').value,
            notes: document.getElementById('retraceNotes').value
        };

        retracePayload = payload;
        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceRequestOtp(payload);
            
            if (response.success) {
                retraceStep1.style.display = 'none';
                retraceStep2.style.display = 'block';
                safeToast('Code de vérification envoyé !', 'success');
            } else {
                throw new Error(response.message || 'Erreur lors de l\'envoi du code');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de l\'envoi du code', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceSubmitBtn'), '<i class="fas fa-paper-plane me-2"></i>Demander le code de vérification');
        }
    });

    retraceOtpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const code = document.getElementById('retraceOtpCode').value.trim();
        
        if (!code) {
            safeToast('Veuillez saisir le code de vérification', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceOtpSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceVerifyOtp(code, retracePayload);
            
            if (response.success && response.data) {
                retraceStep2.style.display = 'none';
                retraceStep3.style.display = 'block';
                
                // Afficher les informations du retrace créé
                document.getElementById('retraceTrackingCode').textContent = response.data.tracking_code || 'N/A';
                document.getElementById('retraceCreatedDate').textContent = response.data.date ? new Date(response.data.date).toLocaleDateString('fr-FR') : 'N/A';
                document.getElementById('retraceCreatedReference').textContent = response.data.reference || 'N/A';
                document.getElementById('retraceCreatedSubject').textContent = response.data.subject || 'N/A';
                
                // Configurer le bouton de téléchargement
                document.getElementById('retraceDownloadBtn').onclick = async function() {
                    try {
                        const trackingCode = response.data.tracking_code;
                        if (!trackingCode) {
                            safeToast('Code de suivi manquant', 'error');
                            return;
                        }
                        
                        const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `retrace_${trackingCode}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        safeToast('PDF téléchargé avec succès !', 'success');
                    } catch (error) {
                        safeToast('Erreur lors du téléchargement', 'error');
                    }
                };
                
                safeToast('Retrace créé avec succès !', 'success');
            } else {
                throw new Error(response.message || 'Code de vérification invalide');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de la vérification', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceOtpSubmitBtn'), '<i class="fas fa-check me-2"></i>Vérifier et créer le retrace');
        }
    });

    // Bouton retour
    document.getElementById('retraceBackBtn').addEventListener('click', function() {
        retraceStep2.style.display = 'none';
        retraceStep1.style.display = 'block';
        retraceError.style.display = 'none';
    });

    // Réinitialiser le modal à la fermeture
    document.getElementById('retraceModal').addEventListener('hidden.bs.modal', function() {
        retraceStep1.style.display = 'block';
        retraceStep2.style.display = 'none';
        retraceStep3.style.display = 'none';
        retraceError.style.display = 'none';
        retraceForm.reset();
        retraceOtpForm.reset();
        retracePayload = null;
    });

    // Téléversement via API
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadFile = document.getElementById('uploadFile');
    const originalUploadBtnText = uploadBtn ? uploadBtn.innerHTML : '';

    if (uploadForm && uploadBtn && uploadFile) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            uploadError.style.display = 'none';
            if (!uploadFile.files || uploadFile.files.length === 0) {
                safeToast('Veuillez choisir un fichier PDF.', 'error');
                return;
            }
            safeShowLoader(uploadBtn);
            try {
                const res = await signSecureAPI.uploadDocument(uploadFile.files[0], 'email');
                safeToast('Document téléversé avec succès! Vérification OTP envoyée.', 'success');
                const docId = res?.data?.id || res?.data?.document?.id;
                if (docId) {
                    window.location.href = `/documents/${docId}/verify-otp`;
                    return;
                }
                const modalEl = document.getElementById('uploadModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
                setTimeout(() => window.location.reload(), 800);
            } catch (err) {
                uploadError.textContent = err.message || 'Erreur lors du téléversement';
                uploadError.style.display = 'block';
            } finally {
                safeHideLoader(uploadBtn, originalUploadBtnText);
            }
        });
    }

    // Global reload control for profile modal
    let shouldReloadAfterProfileModal = false;

    // Profile info update
    const profileInfoForm = document.getElementById('profileInfoForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileInfoError = document.getElementById('profileInfoError');
    if (profileInfoForm && saveProfileBtn) {
        profileInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profileInfoError.style.display = 'none';
            const name = document.getElementById('profileName').value.trim();
            const phone = document.getElementById('profilePhone').value.trim();
            if (!name) { safeToast('Le nom est requis', 'error'); return; }
            const originalSaveText = saveProfileBtn.innerHTML;
            safeShowLoader(saveProfileBtn);
            try {
                await signSecureAPI.updateProfile({ name, phone });
                safeToast('Profil mis à jour', 'success');
                // Ferme le modal puis recharge
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profileInfoError.textContent = err.message || 'Erreur lors de la mise à jour du profil';
                profileInfoError.style.display = 'block';
            } finally {
                safeHideLoader(saveProfileBtn, originalSaveText);
            }
        });
    }

    // Change password
    const profilePasswordForm = document.getElementById('profilePasswordForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const profilePasswordError = document.getElementById('profilePasswordError');
    if (profilePasswordForm && changePasswordBtn) {
        profilePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profilePasswordError.style.display = 'none';
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            if (newPassword !== confirmNewPassword) {
                profilePasswordError.textContent = 'La confirmation ne correspond pas';
                profilePasswordError.style.display = 'block';
                return;
            }
            const originalChangeText = changePasswordBtn.innerHTML;
            safeShowLoader(changePasswordBtn);
            try {
                await signSecureAPI.changePassword(currentPassword, newPassword, confirmNewPassword);
                safeToast('Mot de passe mis à jour', 'success');
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profilePasswordError.textContent = err.message || 'Erreur lors du changement de mot de passe';
                profilePasswordError.style.display = 'block';
            } finally {
                safeHideLoader(changePasswordBtn, originalChangeText);
            }
        });
    }

    // When profile modal actually finishes closing, trigger reload if requested
    const profileModalEl = document.getElementById('profileModal');
    if (profileModalEl) {
        profileModalEl.addEventListener('hidden.bs.modal', function() {
            if (shouldReloadAfterProfileModal) {
                shouldReloadAfterProfileModal = false;
                window.location.reload();
            }
        });
    }
});
</script>
@endpush



    border-radius: 0.375rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-signed {
    background-color: #198754;
    color: #fff;
}

.status-uploaded {
    background-color: #0dcaf0;
    color: #000;
}

#trackingCode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}
</style>
@endpush

@push("scripts")
<script>
// Fonction utilitaire pour les toasts sécurisés
function safeToast(message, type = 'info') {
    if (window.SignSecureUI && window.SignSecureUI.showToast) {
        SignSecureUI.showToast(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction utilitaire pour les loaders sécurisés
function safeShowLoader(element) {
    if (window.SignSecureUI && window.SignSecureUI.showLoader) {
        SignSecureUI.showLoader(element);
    }
}

function safeHideLoader(element, originalText) {
    if (window.SignSecureUI && window.SignSecureUI.hideLoader) {
        SignSecureUI.hideLoader(element, originalText);
    }
}

// Script pour la vérification de documents
document.addEventListener('DOMContentLoaded', function() {
    // Laisser Bootstrap gérer l'ouverture des modales via les attributs data-bs-*
    const verificationModalEl = document.getElementById('verificationModal');
    const retraceModalEl = document.getElementById('retraceModal');
    const verificationForm = document.getElementById('verificationForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const verificationResults = document.getElementById('verificationResults');
    const verificationError = document.getElementById('verificationError');
    const originalBtnText = verifyBtn.innerHTML;

    verificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const trackingCode = document.getElementById('trackingCode').value.trim().toUpperCase();
        
        if (!trackingCode) {
            safeToast('Veuillez saisir un code de suivi.', 'error');
            return;
        }

        // Cacher les résultats précédents
        verificationResults.style.display = 'none';
        verificationError.style.display = 'none';
        
        safeShowLoader(verifyBtn);

        try {
            const response = await signSecureAPI.verifyDocument(trackingCode);
            
            if (response.success && response.data) {
                // Vérifier si c'est un document ou un retrace
                if (response.data.document) {
                    // C'est un document signé - Remettre les labels par défaut
                    document.getElementById('docNameLabel').textContent = 'Nom du document :';
                    document.getElementById('signerNameLabel').textContent = 'Signataire :';
                    document.getElementById('signerEmailLabel').textContent = 'Email du signataire :';
                    document.getElementById('signatureDateLabel').textContent = 'Date de signature :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                    
                    // Afficher les données du document
                    document.getElementById('docName').textContent = response.data.document.original_filename || 'N/A';
                    document.getElementById('signerName').textContent = response.data.signer?.name || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.signer?.email || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.document.signed_at ? new Date(response.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.document.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour document
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadAuthenticDocument(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = response.data.document.original_filename || 'document_authentique.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${response.data.document.tracking_code || trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Document vérifié avec succès !', 'success');
                    
                } else if (response.type === 'physical_retrace') {
                    // C'est un retrace physique - Mettre à jour les labels
                    document.getElementById('docNameLabel').textContent = 'Objet du document :';
                    document.getElementById('signerNameLabel').textContent = 'Référence :';
                    document.getElementById('signerEmailLabel').textContent = 'Date :';
                    document.getElementById('signatureDateLabel').textContent = 'Notes :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                    
                    // Afficher les données du retrace
                    document.getElementById('docName').textContent = response.data.subject || 'N/A';
                    document.getElementById('signerName').textContent = response.data.reference || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.date || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.notes || 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour retrace
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `retrace_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('PDF de retrace téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Retrace physique vérifié avec succès !', 'success');
                }
                
                verificationResults.style.display = 'block';
                
            } else {
                verificationError.style.display = 'block';
                console.error('Réponse invalide:', response);
                document.getElementById('errorMessage').textContent = response.message || 'Document non trouvé ou code invalide.';
            }

        } catch (error) {
            verificationError.style.display = 'block';
            console.error('Erreur vérification:', error);
            document.getElementById('errorMessage').textContent = error.message || 'Erreur lors de la vérification.';
        } finally {
            safeHideLoader(verifyBtn, originalBtnText);
        }
    });

    // Formater automatiquement le code de suivi
    document.getElementById('trackingCode').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Script pour le retrace physique
    const retraceForm = document.getElementById('retraceForm');
    const retraceOtpForm = document.getElementById('retraceOtpForm');
    const retraceStep1 = document.getElementById('retraceStep1');
    const retraceStep2 = document.getElementById('retraceStep2');
    const retraceStep3 = document.getElementById('retraceStep3');
    const retraceError = document.getElementById('retraceError');
    let retracePayload = null;

    retraceForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const payload = {
            date: document.getElementById('retraceDate').value,
            reference: document.getElementById('retraceReference').value,
            subject: document.getElementById('retraceSubject').value,
            notes: document.getElementById('retraceNotes').value
        };

        retracePayload = payload;
        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceRequestOtp(payload);
            
            if (response.success) {
                retraceStep1.style.display = 'none';
                retraceStep2.style.display = 'block';
                safeToast('Code de vérification envoyé !', 'success');
            } else {
                throw new Error(response.message || 'Erreur lors de l\'envoi du code');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de l\'envoi du code', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceSubmitBtn'), '<i class="fas fa-paper-plane me-2"></i>Demander le code de vérification');
        }
    });

    retraceOtpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const code = document.getElementById('retraceOtpCode').value.trim();
        
        if (!code) {
            safeToast('Veuillez saisir le code de vérification', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceOtpSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceVerifyOtp(code, retracePayload);
            
            if (response.success && response.data) {
                retraceStep2.style.display = 'none';
                retraceStep3.style.display = 'block';
                
                // Afficher les informations du retrace créé
                document.getElementById('retraceTrackingCode').textContent = response.data.tracking_code || 'N/A';
                document.getElementById('retraceCreatedDate').textContent = response.data.date ? new Date(response.data.date).toLocaleDateString('fr-FR') : 'N/A';
                document.getElementById('retraceCreatedReference').textContent = response.data.reference || 'N/A';
                document.getElementById('retraceCreatedSubject').textContent = response.data.subject || 'N/A';
                
                // Configurer le bouton de téléchargement
                document.getElementById('retraceDownloadBtn').onclick = async function() {
                    try {
                        const trackingCode = response.data.tracking_code;
                        if (!trackingCode) {
                            safeToast('Code de suivi manquant', 'error');
                            return;
                        }
                        
                        const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `retrace_${trackingCode}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        safeToast('PDF téléchargé avec succès !', 'success');
                    } catch (error) {
                        safeToast('Erreur lors du téléchargement', 'error');
                    }
                };
                
                safeToast('Retrace créé avec succès !', 'success');
            } else {
                throw new Error(response.message || 'Code de vérification invalide');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de la vérification', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceOtpSubmitBtn'), '<i class="fas fa-check me-2"></i>Vérifier et créer le retrace');
        }
    });

    // Bouton retour
    document.getElementById('retraceBackBtn').addEventListener('click', function() {
        retraceStep2.style.display = 'none';
        retraceStep1.style.display = 'block';
        retraceError.style.display = 'none';
    });

    // Réinitialiser le modal à la fermeture
    document.getElementById('retraceModal').addEventListener('hidden.bs.modal', function() {
        retraceStep1.style.display = 'block';
        retraceStep2.style.display = 'none';
        retraceStep3.style.display = 'none';
        retraceError.style.display = 'none';
        retraceForm.reset();
        retraceOtpForm.reset();
        retracePayload = null;
    });

    // Téléversement via API
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadFile = document.getElementById('uploadFile');
    const originalUploadBtnText = uploadBtn ? uploadBtn.innerHTML : '';

    if (uploadForm && uploadBtn && uploadFile) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            uploadError.style.display = 'none';
            if (!uploadFile.files || uploadFile.files.length === 0) {
                safeToast('Veuillez choisir un fichier PDF.', 'error');
                return;
            }
            safeShowLoader(uploadBtn);
            try {
                const res = await signSecureAPI.uploadDocument(uploadFile.files[0], 'email');
                safeToast('Document téléversé avec succès! Vérification OTP envoyée.', 'success');
                const docId = res?.data?.id || res?.data?.document?.id;
                if (docId) {
                    window.location.href = `/documents/${docId}/verify-otp`;
                    return;
                }
                const modalEl = document.getElementById('uploadModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
                setTimeout(() => window.location.reload(), 800);
            } catch (err) {
                uploadError.textContent = err.message || 'Erreur lors du téléversement';
                uploadError.style.display = 'block';
            } finally {
                safeHideLoader(uploadBtn, originalUploadBtnText);
            }
        });
    }

    // Global reload control for profile modal
    let shouldReloadAfterProfileModal = false;

    // Profile info update
    const profileInfoForm = document.getElementById('profileInfoForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileInfoError = document.getElementById('profileInfoError');
    if (profileInfoForm && saveProfileBtn) {
        profileInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profileInfoError.style.display = 'none';
            const name = document.getElementById('profileName').value.trim();
            const phone = document.getElementById('profilePhone').value.trim();
            if (!name) { safeToast('Le nom est requis', 'error'); return; }
            const originalSaveText = saveProfileBtn.innerHTML;
            safeShowLoader(saveProfileBtn);
            try {
                await signSecureAPI.updateProfile({ name, phone });
                safeToast('Profil mis à jour', 'success');
                // Ferme le modal puis recharge
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profileInfoError.textContent = err.message || 'Erreur lors de la mise à jour du profil';
                profileInfoError.style.display = 'block';
            } finally {
                safeHideLoader(saveProfileBtn, originalSaveText);
            }
        });
    }

    // Change password
    const profilePasswordForm = document.getElementById('profilePasswordForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const profilePasswordError = document.getElementById('profilePasswordError');
    if (profilePasswordForm && changePasswordBtn) {
        profilePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profilePasswordError.style.display = 'none';
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            if (newPassword !== confirmNewPassword) {
                profilePasswordError.textContent = 'La confirmation ne correspond pas';
                profilePasswordError.style.display = 'block';
                return;
            }
            const originalChangeText = changePasswordBtn.innerHTML;
            safeShowLoader(changePasswordBtn);
            try {
                await signSecureAPI.changePassword(currentPassword, newPassword, confirmNewPassword);
                safeToast('Mot de passe mis à jour', 'success');
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profilePasswordError.textContent = err.message || 'Erreur lors du changement de mot de passe';
                profilePasswordError.style.display = 'block';
            } finally {
                safeHideLoader(changePasswordBtn, originalChangeText);
            }
        });
    }

    // When profile modal actually finishes closing, trigger reload if requested
    const profileModalEl = document.getElementById('profileModal');
    if (profileModalEl) {
        profileModalEl.addEventListener('hidden.bs.modal', function() {
            if (shouldReloadAfterProfileModal) {
                shouldReloadAfterProfileModal = false;
                window.location.reload();
            }
        });
    }
});
</script>
@endpush



    border-radius: 0.375rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-signed {
    background-color: #198754;
    color: #fff;
}

.status-uploaded {
    background-color: #0dcaf0;
    color: #000;
}

#trackingCode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}
</style>
@endpush

@push("scripts")
<script>
// Fonction utilitaire pour les toasts sécurisés
function safeToast(message, type = 'info') {
    if (window.SignSecureUI && window.SignSecureUI.showToast) {
        SignSecureUI.showToast(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction utilitaire pour les loaders sécurisés
function safeShowLoader(element) {
    if (window.SignSecureUI && window.SignSecureUI.showLoader) {
        SignSecureUI.showLoader(element);
    }
}

function safeHideLoader(element, originalText) {
    if (window.SignSecureUI && window.SignSecureUI.hideLoader) {
        SignSecureUI.hideLoader(element, originalText);
    }
}

// Script pour la vérification de documents
document.addEventListener('DOMContentLoaded', function() {
    // Laisser Bootstrap gérer l'ouverture des modales via les attributs data-bs-*
    const verificationModalEl = document.getElementById('verificationModal');
    const retraceModalEl = document.getElementById('retraceModal');
    const verificationForm = document.getElementById('verificationForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const verificationResults = document.getElementById('verificationResults');
    const verificationError = document.getElementById('verificationError');
    const originalBtnText = verifyBtn.innerHTML;

    verificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const trackingCode = document.getElementById('trackingCode').value.trim().toUpperCase();
        
        if (!trackingCode) {
            safeToast('Veuillez saisir un code de suivi.', 'error');
            return;
        }

        // Cacher les résultats précédents
        verificationResults.style.display = 'none';
        verificationError.style.display = 'none';
        
        safeShowLoader(verifyBtn);

        try {
            const response = await signSecureAPI.verifyDocument(trackingCode);
            
            if (response.success && response.data) {
                // Vérifier si c'est un document ou un retrace
                if (response.data.document) {
                    // C'est un document signé - Remettre les labels par défaut
                    document.getElementById('docNameLabel').textContent = 'Nom du document :';
                    document.getElementById('signerNameLabel').textContent = 'Signataire :';
                    document.getElementById('signerEmailLabel').textContent = 'Email du signataire :';
                    document.getElementById('signatureDateLabel').textContent = 'Date de signature :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                    
                    // Afficher les données du document
                    document.getElementById('docName').textContent = response.data.document.original_filename || 'N/A';
                    document.getElementById('signerName').textContent = response.data.signer?.name || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.signer?.email || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.document.signed_at ? new Date(response.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.document.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour document
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadAuthenticDocument(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = response.data.document.original_filename || 'document_authentique.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${response.data.document.tracking_code || trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Document vérifié avec succès !', 'success');
                    
                } else if (response.type === 'physical_retrace') {
                    // C'est un retrace physique - Mettre à jour les labels
                    document.getElementById('docNameLabel').textContent = 'Objet du document :';
                    document.getElementById('signerNameLabel').textContent = 'Référence :';
                    document.getElementById('signerEmailLabel').textContent = 'Date :';
                    document.getElementById('signatureDateLabel').textContent = 'Notes :';
                    document.getElementById('verifiedTrackingCodeLabel').textContent = 'Code de suivi :';
                    
                    // Mettre à jour le titre du modal
                    document.querySelector('#verificationResults .card-header h6').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                    
                    // Afficher les données du retrace
                    document.getElementById('docName').textContent = response.data.subject || 'N/A';
                    document.getElementById('signerName').textContent = response.data.reference || 'N/A';
                    document.getElementById('signerEmail').textContent = response.data.date || 'N/A';
                    document.getElementById('signatureDate').textContent = response.data.notes || 'N/A';
                    document.getElementById('verifiedTrackingCode').textContent = response.data.tracking_code || 'N/A';
                    
                    // Configurer les boutons de téléchargement pour retrace
                    document.getElementById('downloadAuthenticBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `retrace_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('PDF de retrace téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors du téléchargement', 'error');
                        }
                    };

                    document.getElementById('generateReportBtn').onclick = async function() {
                        try {
                            const blob = await signSecureAPI.generateVerificationReport(trackingCode);
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `verification_report_${trackingCode}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                            safeToast('Rapport de vérification téléchargé !', 'success');
                        } catch (error) {
                            safeToast('Erreur lors de la génération du rapport', 'error');
                        }
                    };
                    
                    safeToast('Retrace physique vérifié avec succès !', 'success');
                }
                
                verificationResults.style.display = 'block';
                
            } else {
                verificationError.style.display = 'block';
                console.error('Réponse invalide:', response);
                document.getElementById('errorMessage').textContent = response.message || 'Document non trouvé ou code invalide.';
            }

        } catch (error) {
            verificationError.style.display = 'block';
            console.error('Erreur vérification:', error);
            document.getElementById('errorMessage').textContent = error.message || 'Erreur lors de la vérification.';
        } finally {
            safeHideLoader(verifyBtn, originalBtnText);
        }
    });

    // Formater automatiquement le code de suivi
    document.getElementById('trackingCode').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Script pour le retrace physique
    const retraceForm = document.getElementById('retraceForm');
    const retraceOtpForm = document.getElementById('retraceOtpForm');
    const retraceStep1 = document.getElementById('retraceStep1');
    const retraceStep2 = document.getElementById('retraceStep2');
    const retraceStep3 = document.getElementById('retraceStep3');
    const retraceError = document.getElementById('retraceError');
    let retracePayload = null;

    retraceForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const payload = {
            date: document.getElementById('retraceDate').value,
            reference: document.getElementById('retraceReference').value,
            subject: document.getElementById('retraceSubject').value,
            notes: document.getElementById('retraceNotes').value
        };

        retracePayload = payload;
        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceRequestOtp(payload);
            
            if (response.success) {
                retraceStep1.style.display = 'none';
                retraceStep2.style.display = 'block';
                safeToast('Code de vérification envoyé !', 'success');
            } else {
                throw new Error(response.message || 'Erreur lors de l\'envoi du code');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de l\'envoi du code', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceSubmitBtn'), '<i class="fas fa-paper-plane me-2"></i>Demander le code de vérification');
        }
    });

    retraceOtpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const code = document.getElementById('retraceOtpCode').value.trim();
        
        if (!code) {
            safeToast('Veuillez saisir le code de vérification', 'error');
            return;
        }

        retraceError.style.display = 'none';
        safeShowLoader(document.getElementById('retraceOtpSubmitBtn'));

        try {
            const response = await signSecureAPI.retraceVerifyOtp(code, retracePayload);
            
            if (response.success && response.data) {
                retraceStep2.style.display = 'none';
                retraceStep3.style.display = 'block';
                
                // Afficher les informations du retrace créé
                document.getElementById('retraceTrackingCode').textContent = response.data.tracking_code || 'N/A';
                document.getElementById('retraceCreatedDate').textContent = response.data.date ? new Date(response.data.date).toLocaleDateString('fr-FR') : 'N/A';
                document.getElementById('retraceCreatedReference').textContent = response.data.reference || 'N/A';
                document.getElementById('retraceCreatedSubject').textContent = response.data.subject || 'N/A';
                
                // Configurer le bouton de téléchargement
                document.getElementById('retraceDownloadBtn').onclick = async function() {
                    try {
                        const trackingCode = response.data.tracking_code;
                        if (!trackingCode) {
                            safeToast('Code de suivi manquant', 'error');
                            return;
                        }
                        
                        const blob = await signSecureAPI.downloadRetracePdf(trackingCode);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `retrace_${trackingCode}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        safeToast('PDF téléchargé avec succès !', 'success');
                    } catch (error) {
                        safeToast('Erreur lors du téléchargement', 'error');
                    }
                };
                
                safeToast('Retrace créé avec succès !', 'success');
            } else {
                throw new Error(response.message || 'Code de vérification invalide');
            }
        } catch (error) {
            retraceError.style.display = 'block';
            document.getElementById('retraceErrorMessage').textContent = error.message;
            safeToast('Erreur lors de la vérification', 'error');
        } finally {
            safeHideLoader(document.getElementById('retraceOtpSubmitBtn'), '<i class="fas fa-check me-2"></i>Vérifier et créer le retrace');
        }
    });

    // Bouton retour
    document.getElementById('retraceBackBtn').addEventListener('click', function() {
        retraceStep2.style.display = 'none';
        retraceStep1.style.display = 'block';
        retraceError.style.display = 'none';
    });

    // Réinitialiser le modal à la fermeture
    document.getElementById('retraceModal').addEventListener('hidden.bs.modal', function() {
        retraceStep1.style.display = 'block';
        retraceStep2.style.display = 'none';
        retraceStep3.style.display = 'none';
        retraceError.style.display = 'none';
        retraceForm.reset();
        retraceOtpForm.reset();
        retracePayload = null;
    });

    // Téléversement via API
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadFile = document.getElementById('uploadFile');
    const originalUploadBtnText = uploadBtn ? uploadBtn.innerHTML : '';

    if (uploadForm && uploadBtn && uploadFile) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            uploadError.style.display = 'none';
            if (!uploadFile.files || uploadFile.files.length === 0) {
                safeToast('Veuillez choisir un fichier PDF.', 'error');
                return;
            }
            safeShowLoader(uploadBtn);
            try {
                const res = await signSecureAPI.uploadDocument(uploadFile.files[0], 'email');
                safeToast('Document téléversé avec succès! Vérification OTP envoyée.', 'success');
                const docId = res?.data?.id || res?.data?.document?.id;
                if (docId) {
                    window.location.href = `/documents/${docId}/verify-otp`;
                    return;
                }
                const modalEl = document.getElementById('uploadModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
                setTimeout(() => window.location.reload(), 800);
            } catch (err) {
                uploadError.textContent = err.message || 'Erreur lors du téléversement';
                uploadError.style.display = 'block';
            } finally {
                safeHideLoader(uploadBtn, originalUploadBtnText);
            }
        });
    }

    // Global reload control for profile modal
    let shouldReloadAfterProfileModal = false;

    // Profile info update
    const profileInfoForm = document.getElementById('profileInfoForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileInfoError = document.getElementById('profileInfoError');
    if (profileInfoForm && saveProfileBtn) {
        profileInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profileInfoError.style.display = 'none';
            const name = document.getElementById('profileName').value.trim();
            const phone = document.getElementById('profilePhone').value.trim();
            if (!name) { safeToast('Le nom est requis', 'error'); return; }
            const originalSaveText = saveProfileBtn.innerHTML;
            safeShowLoader(saveProfileBtn);
            try {
                await signSecureAPI.updateProfile({ name, phone });
                safeToast('Profil mis à jour', 'success');
                // Ferme le modal puis recharge
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profileInfoError.textContent = err.message || 'Erreur lors de la mise à jour du profil';
                profileInfoError.style.display = 'block';
            } finally {
                safeHideLoader(saveProfileBtn, originalSaveText);
            }
        });
    }

    // Change password
    const profilePasswordForm = document.getElementById('profilePasswordForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const profilePasswordError = document.getElementById('profilePasswordError');
    if (profilePasswordForm && changePasswordBtn) {
        profilePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            profilePasswordError.style.display = 'none';
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            if (newPassword !== confirmNewPassword) {
                profilePasswordError.textContent = 'La confirmation ne correspond pas';
                profilePasswordError.style.display = 'block';
                return;
            }
            const originalChangeText = changePasswordBtn.innerHTML;
            safeShowLoader(changePasswordBtn);
            try {
                await signSecureAPI.changePassword(currentPassword, newPassword, confirmNewPassword);
                safeToast('Mot de passe mis à jour', 'success');
                const modalEl = document.getElementById('profileModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                shouldReloadAfterProfileModal = true;
                if (modal) modal.hide(); else window.location.reload();
            } catch (err) {
                profilePasswordError.textContent = err.message || 'Erreur lors du changement de mot de passe';
                profilePasswordError.style.display = 'block';
            } finally {
                safeHideLoader(changePasswordBtn, originalChangeText);
            }
        });
    }

    // When profile modal actually finishes closing, trigger reload if requested
    const profileModalEl = document.getElementById('profileModal');
    if (profileModalEl) {
        profileModalEl.addEventListener('hidden.bs.modal', function() {
            if (shouldReloadAfterProfileModal) {
                shouldReloadAfterProfileModal = false;
                window.location.reload();
            }
        });
    }
});
</script>
@endpush



