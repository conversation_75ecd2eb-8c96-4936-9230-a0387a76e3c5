<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Document;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\API\DocumentUploadRequest;
use App\Http\Resources\DocumentResource;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Services\OTPService;
use App\Services\PDFService;
use Illuminate\Http\UploadedFile;
use setasign\Fpdi\Fpdi;
use setasign\Fpdf\Fpdf;
use setasign\Fpdi\PdfReader;

class DocumentController extends Controller

{     public function __construct(
        private PDFService $pdfService,
        private OTPService $otpService
    ) {
        $this->middleware('auth');
    }
    public function index()
    {
        $documents = Document::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('documents.index', compact('documents'));
    }

    public function showUploadForm()
    {
        return view('documents.upload');
    }

/*public function upload(Request $request)
{
    try {
        Log::info('Début du téléversement (web)');

        $request->validate([
            'document' => 'required|file|mimes:pdf|max:10240', 
            'otp_method' => 'nullable|in:sms,email',
        ]);

        $file = $request->file('document');

        // Appel à l'API
        $response = Http::withToken($request->user()->api_token ?? 'YOUR_FAKE_API_TOKEN') // Utiliser le token Sanctum
            ->attach(
                'document',
                file_get_contents($file),
                $file->getClientOriginalName()
            )
            /*->post(route('api.documents.store', absolute: false), [
                'otp_method' => $request->input('otp_method', 'email')
            ]);

        if ($response->successful()) {
            Log::info('Succès du téléversement via API');

            return redirect()->back()->with('success', 'Document téléversé avec succès. Un code OTP a été envoyé.');
        }

        // Si erreur côté API
        Log::warning('Erreur API upload', ['status' => $response->status(), 'body' => $response->body()]);

        return redirect()->back()->with('error', 'Échec du téléversement via l\'API.');
    } catch (\Throwable $e) {
        Log::error('Erreur dans le contrôleur web upload', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return redirect()->back()->with('error', 'Erreur interne lors du téléversement.');
    }
}*/

public function store(DocumentUploadRequest $request): JsonResponse
{
    try {
        Log::info('Début du téléversement', ['user_id' => $request->user()->id]);

        $validated = $request->validated();

        // S'assurer que le fichier est bien un UploadedFile
        if (!$request->hasFile('document')) {
            return response()->json([
                'success' => false,
                'message' => 'Aucun fichier reçu',
            ], 400);
        }

        $file = $request->file('document');
        if (!$file instanceof UploadedFile) {
            return response()->json([
                'success' => false,
                'message' => 'Le fichier envoyé est invalide',
            ], 422);
        }

        // Vérifier le mimetype PDF
        if ($file->getMimeType() !== 'application/pdf') {
            return response()->json([
                'success' => false,
                'message' => 'Seuls les fichiers PDF sont acceptés',
            ], 400);
        }

        Log::info('Fichier reçu', [
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
        ]);

        $filename = Str::uuid() . '.pdf';
        Storage::disk('private')->makeDirectory('documents/original');
        $path = $file->storeAs('documents/original', $filename, 'private'); 

        Log::info('Fichier stocké', ['path' => $path]);

        $document = Document::create([
            'user_id' => $request->user()->id,
            'original_filename' => $file->getClientOriginalName(),
            'stored_filename' => $filename,
            'file_path' => $path,
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'pages_count' => $this->pdfService->getPageCount($file->getRealPath()),
            'status' => 'uploaded',
        ]);

        Log::info('Document enregistré', ['document_id' => $document->id]);

        

                       $otpMethod = $validated['otp_method'] ?? 'email';
               // Associer l'OTP au document en cours
               $this->otpService->generateAndSend($request->user(), $otpMethod, $document->id);

               // Créer automatiquement le premier signataire (l'utilisateur qui upload)
               \App\Models\DocumentSigner::create([
                   'document_id' => $document->id,
                   'user_id' => $request->user()->id,
                   'email' => $request->user()->email,
                   'name' => $request->user()->name,
                   'signature_order' => 1,
                   'status' => 'pending',
                   'expires_at' => now()->addDays(30), // Expire dans 30 jours
               ]);

        return response()->json([
            'success' => true,
            'message' => 'Document téléversé avec succès. Code OTP envoyé.',
            'id' => $document->id,
            'data' => new DocumentResource($document),
        ], 201);

    } catch (\Throwable $e) {
        Log::error('Erreur lors du téléversement', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return response()->json([
            'message' => 'Erreur interne lors du téléversement',
        ], 500);
    }
}



    public function show(Document $document)
    {
        $this->authorize('view', $document);
        return view('documents.show', compact('document'));
    }

    public function preview(Document $document)
    {
        $this->authorize('view', $document);
        return view('documents.preview', compact('document'));
    }

    public function showOtpForm(Document $document)
    {
        $this->authorize('view', $document);
        $user = auth()->user();
        // Chercher un OTP actif (non utilisé et non expiré) pour ce document
        $otp = $user?->otpCodes()
            ->where('document_id', $document->id)
            ->whereNull('used_at')
            ->where('expires_at', '>', now())
            ->latest()
            ->first();

        // Si aucun OTP actif, en générer un immédiatement (par défaut email)
        if (!$otp) {
            try {
                $this->otpService->generateAndSend($user, 'email', $document->id);
                $otp = $user->otpCodes()
                    ->where('document_id', $document->id)
                    ->whereNull('used_at')
                    ->where('expires_at', '>', now())
                    ->latest()
                    ->first();
            } catch (\Throwable $e) {
                \Log::warning('Impossible de générer un OTP lors de showOtpForm', [
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $otpExpiresAt = $otp?->expires_at?->toIso8601String();

        return view('documents.verify-otp', [
            'document' => $document,
            'otp_expires_at' => $otpExpiresAt,
        ]);
    }

    public function verifyOtp(Request $request, Document $document)
    {
        $this->authorize('view', $document);
        
        $request->validate([
            'code' => ['required', 'string', 'size:6', 'regex:/^[0-9]{6}$/'],
        ], [
            'code.required' => 'Le code OTP est obligatoire.',
            'code.size' => 'Le code OTP doit contenir exactement 6 chiffres.',
            'code.regex' => 'Le code OTP doit contenir uniquement des chiffres.',
        ]);

        $user = Auth::user();
        $code = $request->input('code');
        
        try {
            $isValid = $this->otpService->verify($user, $code, $document->id);
            
            if ($isValid) {
                return response()->json([
                    'success' => true,
                    'message' => 'Code OTP vérifié avec succès',
                    'data' => [
                        'document_id' => $document->id,
                        'verified' => true,
                        'valid_until' => now()->addMinutes(30)->toIso8601String(),
                    ],
                ], 200);
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Code OTP invalide ou expiré',
                'data' => [
                    'document_id' => $document->id,
                    'verified' => false,
                ],
            ], 422);
            
        } catch (\Exception $e) {
            Log::error('Erreur vérification OTP', [
                'user_id' => $user->id,
                'document_id' => $document->id,
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la vérification du code OTP',
            ], 500);
        }
    }

    public function generateOtp(Request $request, Document $document)
    {
        $this->authorize('view', $document);
        
        $request->validate([
            'type' => ['required', 'string', 'in:email,sms'],
        ], [
            'type.required' => 'Le type d\'envoi est obligatoire.',
            'type.in' => 'Le type doit être email ou sms.',
        ]);

        $user = Auth::user();
        $type = $request->input('type', 'email');
        
        try {
            $code = $this->otpService->generateAndSend($user, $type, $document->id);
            
            return response()->json([
                'success' => true,
                'message' => "Code OTP envoyé par {$type} avec succès",
                'data' => [
                    'document_id' => $document->id,
                    'type' => $type,
                    'expires_at' => now()->addMinutes(10)->toIso8601String(),
                ],
            ], 200);
            
        } catch (\Exception $e) {
            Log::error('Erreur génération OTP', [
                'user_id' => $user->id,
                'document_id' => $document->id,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'envoi du code OTP',
            ], 500);
        }
    }

    public function download(Document $document)
    {
        $this->authorize('view', $document);
        // Préférer le PDF signé s'il existe, sinon l'original (depuis le disque privé)
        $path = $document->signed_file_path ?: $document->file_path;
        if (!\Storage::disk('private')->exists($path)) {
            abort(404, 'Fichier introuvable');
        }

        $absolutePath = \Storage::disk('private')->path($path);
        return response()->download($absolutePath, $document->original_filename, [
            'Content-Type' => 'application/pdf',
        ]);
    }

    public function getPdf(Document $document)
    {
        $this->authorize('view', $document);
        
        // Préférer le PDF signé s'il existe
        $path = $document->signed_file_path ?: $document->file_path;
        if (!Storage::disk('private')->exists($path)) {
            abort(404, 'Fichier PDF introuvable');
        }

        $fileContent = Storage::disk('private')->get($path);
        
        return response($fileContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $document->original_filename . '"',
        ]);
    }
}
