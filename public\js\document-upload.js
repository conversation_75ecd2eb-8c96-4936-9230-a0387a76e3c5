/**
 * Script pour la page de téléversement de documents
 */

document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('documentFile');
    const uploadPrompt = document.getElementById('uploadPrompt');
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileInfo = document.getElementById('fileInfo');
    const otpMethod = document.getElementById('otpMethod');
    const submitBtn = document.getElementById('submitBtn');
    const uploadForm = document.getElementById('uploadForm');
    const browseBtn = document.getElementById('browseButton');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const infoFileName = document.getElementById('infoFileName');
    const infoFileSize = document.getElementById('infoFileSize');

    // Vérifier que tous les éléments nécessaires existent
    if (!uploadArea || !fileInput || !uploadForm) {
        console.error('Éléments de téléversement manquants');
        return;
    }

    // Sélection via bouton
    if (browseBtn && fileInput) {
        browseBtn.addEventListener('click', () => {
            fileInput.click();
        });
    }

    // Drag & Drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('border-primary', 'bg-light');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary', 'bg-light');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary', 'bg-light');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        } else {
            showToast('Aucun fichier déposé.', 'error');
        }
    });

    // Sélection via input
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        } else {
            showToast('Aucun fichier sélectionné.', 'error');
        }
    });

    function handleFileSelection(file) {
        if (file.type !== 'application/pdf') {
            showToast('Seuls les fichiers PDF sont acceptés.', 'error');
            return;
        }
        
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            showToast('Le fichier est trop volumineux. Taille maximum : 10 MB.', 'error');
            return;
        }

        if (fileName) fileName.textContent = file.name;
        if (fileSize) fileSize.textContent = formatFileSize(file.size);
        if (infoFileName) infoFileName.textContent = file.name;
        if (infoFileSize) infoFileSize.textContent = formatFileSize(file.size);

        if (uploadPrompt) uploadPrompt.classList.add('d-none');
        if (filePreview) filePreview.classList.remove('d-none');
        if (fileInfo) fileInfo.style.display = 'block';
        if (otpMethod) otpMethod.style.display = 'block';
        if (submitBtn) submitBtn.disabled = false;

        uploadArea.classList.add('border-success');
        setTimeout(() => {
            uploadArea.classList.remove('border-success');
        }, 2000);

        showToast('Fichier sélectionné avec succès !', 'success');
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    window.removeFile = function() {
        if (fileInput) fileInput.value = '';
        if (uploadPrompt) uploadPrompt.classList.remove('d-none');
        if (filePreview) filePreview.classList.add('d-none');
        if (fileInfo) fileInfo.style.display = 'none';
        if (otpMethod) otpMethod.style.display = 'none';
        if (submitBtn) submitBtn.disabled = true;
        uploadArea.classList.remove('border-success', 'border-primary', 'bg-light');
        showToast('Fichier retiré.', 'info');
    };

    // Téléversement via API
    uploadForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        if (!fileInput.files.length) {
            showToast('Veuillez sélectionner un fichier.', 'error');
            return;
        }

        const file = fileInput.files[0];
        const selectedOtpInput = document.querySelector('input[name="otp_method"]:checked');
        const otpMethodValue = selectedOtpInput ? selectedOtpInput.value : 'email';

        try {
            // Afficher le progrès
            if (uploadProgress) uploadProgress.classList.remove('d-none');
            if (progressBar) progressBar.style.width = '0%';
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Téléversement...';
            }

            // Simuler le progrès
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                if (progressBar) progressBar.style.width = progress + '%';
            }, 200);

            // Téléverser via l'API
            const response = await signSecureAPI.uploadDocument(file, otpMethodValue);

            clearInterval(progressInterval);
            if (progressBar) progressBar.style.width = '100%';

            if (response.success && response.data) {
                showToast("Document téléversé avec succès !", 'success');
                
                // Rediriger vers la vérification OTP
                if (response.data.id) {
                    setTimeout(() => {
                        window.location.href = `/documents/${response.data.id}/verify-otp`;
                    }, 1000);
                } else {
                    console.warn('ID du document manquant dans la réponse:', response);
                    showToast("Document téléversé mais redirection impossible", 'warning');
                }
            } else {
                throw new Error(response.message || 'Réponse API invalide');
            }

        } catch (error) {
            console.error('Erreur de téléversement:', error);
            
            // Afficher l'erreur appropriée
            let errorMessage = "Une erreur est survenue lors du téléversement.";
            
            if (error.message) {
                errorMessage = error.message;
            } else if (error.data && error.data.message) {
                errorMessage = error.data.message;
            }
            
            showToast(errorMessage, 'error');
        } finally {
            // Masquer le progrès
            setTimeout(() => {
                if (uploadProgress) uploadProgress.classList.add('d-none');
                if (progressBar) progressBar.style.width = '0%';
            }, 1000);
            
            // Réactiver le bouton
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Téléverser et continuer';
            }
        }
    });

    // Helper pour afficher les toasts
    function showToast(message, type) {
        if (window.SignSecureUI && window.SignSecureUI.showToast) {
            SignSecureUI.showToast(message, type);
        } else if (window.toastr) {
            if (type === 'success') toastr.success(message);
            else if (type === 'error') toastr.error(message);
            else if (type === 'info') toastr.info(message);
            else toastr.warning(message);
        } else {
            alert(message);
        }
    }
});