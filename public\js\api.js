/**
 * SignSecure API Client
 * Gère toutes les communications avec l'API Laravel
 */
class SignSecureAPI {
     constructor() {
        this.baseURL = window.APP_CONFIG.API_URL;
        this.token = localStorage.getItem('auth_token') || null;
    }

    async request(endpoint, options = {}) {
        const url = endpoint.startsWith('http') ? endpoint : `${this.baseURL}${endpoint}`;
        
        // Récupération du token CSRF dans le DOM
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const headers = {
            'Accept': 'application/json',
            ...options.headers
        };

        // Ajout du Content-Type si ce n'est pas un FormData
        if (!(options.body instanceof FormData)) {
            headers['Content-Type'] = 'application/json';
        }

        // Ajout du token CSRF
        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken;
        }

        // Ajout du token d'authentification
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        const config = {
            method: options.method || 'GET',
            headers,
            credentials: 'same-origin', 
            body: options.body instanceof FormData ? options.body : (options.body ? JSON.stringify(options.body) : undefined)
        };

        try {
            const response = await fetch(url, config);
            let data;
            
            try {
                data = await response.json();
            } catch {
                data = null;
            }

            if (!response.ok) {
                const error = new Error(data?.message || 'Erreur réseau');
                error.status = response.status;
                error.data = data;
                error.success = data?.success || false;
                throw error;
            }

            return data;
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('Erreur de connexion au serveur. Vérifiez votre connexion internet.');
            }
            throw error;
        }
    }

    // ==================== AUTHENTIFICATION ====================
    async register(userData) {
        return await this.request('/auth/register', { 
            method: 'POST', 
            body: userData 
        });
    }
    
    async login(credentials) {
        return await this.request('/auth/login', { 
            method: 'POST', 
            body: credentials 
        });
    }
    
    async logout() {
        return await this.request('/auth/logout', { method: 'POST' });
    }
    
    async logoutAll() {
        return await this.request('/auth/logout-all', { method: 'POST' });
    }
    
    async refresh() {
        return await this.request('/auth/refresh', { method: 'POST' });
    }
    
    async getUser() {
        return await this.request('/user');
    }

    // ==================== PROFIL ====================
    async getProfile() {
        return await this.request('/profile');
    }

    async updateProfile(payload) {
        return await this.request('/profile', { method: 'PUT', body: payload });
    }

    async changePassword(currentPassword, newPassword, confirmPassword) {
        return await this.request('/profile/change-password', {
            method: 'POST',
            body: {
                current_password: currentPassword,
                password: newPassword,
                password_confirmation: confirmPassword
            }
        });
    }

    async createWebSession() {
        return await this.request('/auth/web-session', { method: 'POST' });
    }

    setToken(token) {
        this.token = token;
        localStorage.setItem('auth_token', token);
    }

    clearToken() {
        this.token = null;
        localStorage.removeItem('auth_token');
    }

    // ==================== DOCUMENTS ====================
    async uploadDocument(file, otpMethod = 'email') {
        const formData = new FormData();
        formData.append('document', file);
        formData.append('otp_method', otpMethod);

        return await this.request('/documents/upload', { 
            method: 'POST', 
            body: formData 
        });
    }

    async getDocuments(page = 1) { 
        return await this.request(`/documents?page=${page}`); 
    }
    
    async getDocument(documentId) { 
        return await this.request(`/documents/${documentId}`); 
    }

    // ==================== INVITATIONS ====================
    async createInvitation(documentId, email, name = null, signatureOrder = null) {
        return await this.request('/invitations', {
            method: 'POST',
            body: { document_id: documentId, email, name, signature_order: signatureOrder }
        });
    }

    async getMyInvitations() {
        return await this.request('/invitations/for-me');
    }

    async downloadDocument(documentId) {
        const response = await fetch(`${this.baseURL}/documents/${documentId}/download`, {
            headers: { 'Authorization': `Bearer ${this.token}` }
        });
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || 'Erreur lors du téléchargement');
        }
        
        return response.blob();
    }

    async getDocumentPreview(documentId) {
        const response = await fetch(`${this.baseURL}/documents/${documentId}/preview`, {
            headers: { 'Authorization': `Bearer ${this.token}` }
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || "Erreur lors de la prévisualisation");
        }

        return response.blob();
    }

    // ==================== OTP ====================
    async generateOTP(documentId, type = 'email') {
        return await this.request('/otp/generate', { 
            method: 'POST', 
            body: { document_id: documentId, type } 
        });
    }

    async verifyOTP(documentId, code) {
        return await this.request('/otp/verify', { 
            method: 'POST', 
            body: { document_id: documentId, code } 
        });
    }

    async getOTPStatus(documentId) { 
        return await this.request(`/otp/${documentId}/status`); 
    }
    
    async resendOTP(documentId, type = 'email') { 
        return await this.request(`/otp/resend/${documentId}`, { 
            method: 'POST', 
            body: { type } 
        }); 
    }

    // ==================== SIGNATURES ====================
    async captureSignature(signatureData) { 
        return await this.request('/signatures/capture', { 
            method: 'POST', 
            body: { signature_data: signatureData } 
        }); 
    }
    
    async applySignature(documentId, xPct, yPct, pageNumber) {
        return await this.request('/signatures/apply', { 
            method: 'POST', 
            body: { 
                document_id: documentId, 
                x_pct: xPct, 
                y_pct: yPct, 
                page_number: pageNumber 
            } 
        });
    }
    
    async getCurrentSignature() { 
        return await this.request('/signatures/current'); 
    }
    
    async deleteSignature() { 
        return await this.request('/signatures/delete', { method: 'DELETE' }); 
    }

    // ==================== VÉRIFICATION D'AUTHENTICITÉ ====================
    async verifyDocument(trackingCode) { 
        const url = `${window.SignSecure?.baseUrl || ''}/verification/verify-document`;
        const csrf = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf || ''
            },
            body: JSON.stringify({ tracking_code: trackingCode }),
            credentials: 'same-origin'

        });

        const data = await response.json().catch(() => ({}));
        if (!response.ok) {
            const error = new Error(data?.message || 'Erreur lors de la vérification');
            error.data = data;
            throw error;
        }
        return data;
    }
    
    async downloadAuthenticDocument(trackingCode) {
        const url = `${window.SignSecure?.baseUrl || ''}/verification/download-document`;
        const csrf = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf || ''
            },
            body: JSON.stringify({ tracking_code: trackingCode }),
           credentials: 'same-origin'

        });
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || 'Erreur lors du téléchargement du document authentique');
        }
        
        return response.blob();
    }

    async downloadRetracePdf(trackingCode) {
        const url = `${window.SignSecure?.baseUrl || ''}/retrace/download/${encodeURIComponent(trackingCode)}`;
        const response = await fetch(url, { method: 'GET', credentials: 'same-origin' });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || 'Erreur lors du téléchargement du PDF de retrace');
        }

        return response.blob();
    }
    
    async generateVerificationReport(trackingCode) {
        const url = `${window.SignSecure?.baseUrl || ''}/verification/generate-report`;
        const csrf = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf || ''
            },
            body: JSON.stringify({ tracking_code: trackingCode }),
            credentials: 'same-origin'

        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || 'Erreur lors de la génération du rapport');
        }

        return response.blob();
    }

    // ==================== RETRACE PHYSIQUE ====================
    async retraceRequestOtp(payload) {
        // Utilise les routes web via session + JSON pour rester cohérent avec le contrôleur
        const url = `${window.SignSecure?.baseUrl || ''}/retrace/request-otp`;
        const csrf = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const resp = await fetch(url, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf || ''
            },
            body: JSON.stringify(payload),
           credentials: 'same-origin'

        });

        const data = await resp.json().catch(() => ({}));
        if (!resp.ok) {
            const error = new Error(data?.message || 'Erreur lors de la demande OTP');
            error.data = data;
            throw error;
        }
        return data;
    }

    async retraceVerifyOtp(code, payload) {
        const url = `${window.SignSecure?.baseUrl || ''}/retrace/verify-otp`;
        const csrf = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const resp = await fetch(url, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf || ''
            },
            // On renvoie le code, et si la session s'est perdue, on renvoie le payload pour que le serveur puisse reconstituer (sécurité: serveur peut l'ignorer)
            body: JSON.stringify(Object.assign({ code }, payload ? { _payload: payload } : {})),
            credentials: 'same-origin',
            redirect: 'follow'
        });

        // Peut être une redirection Laravel classique; tenter JSON d'abord
        let data = null;
        try { data = await resp.json(); } catch {}

        if (!resp.ok) {
            const error = new Error(data?.message || 'Erreur de vérification OTP');
            error.data = data;
            throw error;
        }
        return data || { success: true };
    }
}

// Instance globale
window.signSecureAPI = new SignSecureAPI();

// ==================== UTILITAIRES UI ====================
window.SignSecureUI = {
    showToast(message, type = 'info') { 
        if(typeof toastr !== 'undefined') {
            toastr[type](message); 
        } else {
            // Fallback vers des alertes natives
            const alertClass = type === 'error' ? 'alert-danger' : 
                             type === 'success' ? 'alert-success' : 
                             type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // Auto-dismiss après 5 secondes
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    },
    
    showLoader(element) { 
        if(element){ 
            element.innerHTML='<i class="fas fa-spinner fa-spin"></i> Chargement...'; 
            element.disabled=true; 
        } 
    },
    
    hideLoader(element, originalText) { 
        if(element){ 
            element.innerHTML=originalText; 
            element.disabled=false; 
        } 
    },
    
    displayValidationErrors(errors) {
        // Nettoyer les erreurs précédentes
        document.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        
        // Afficher les nouvelles erreurs
        Object.keys(errors).forEach(field => {
            const input = document.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('is-invalid');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = Array.isArray(errors[field]) ? errors[field][0] : errors[field];
                input.parentNode.appendChild(errorDiv);
            }
        });
    }
};

// ==================== UTILITAIRES FORM ====================
window.SignSecureForm = {
    validateForm(form) {
        const errors = {};
        form.querySelectorAll('input, select, textarea').forEach(input => {
            if(input.type === 'hidden') return;
            if(!input.value.trim()) {
                errors[input.name] = ['Ce champ est obligatoire.'];
            }
        });
        return errors;
    }
};
