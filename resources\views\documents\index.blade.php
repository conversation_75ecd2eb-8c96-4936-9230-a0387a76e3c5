@extends('layouts.app')

@section('content')
    <div class="container">
        <h1 class="mb-4">Mes documents</h1>

        @if(($documents->total() ?? 0) === 0)
            <div class="alert alert-info">Aucun document pour le moment.</div>
        @else
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Statut</th>
                                    <th>Pages</th>
                                    <th>Taille</th>
                                    <th>Créé le</th>
                                    <th class="text-end">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($documents as $doc)
                                    <tr>
                                        <td>
                                            <i class="fas fa-file-pdf text-danger me-2"></i>
                                            {{ $doc->original_filename }}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $doc->status === 'signed' ? 'success' : ($doc->status === 'processing' ? 'info' : 'warning') }}">
                                                {{ $doc->status === 'signed' ? 'Signé' : ($doc->status === 'processing' ? 'En cours' : 'En attente') }}
                                            </span>
                                        </td>
                                        <td>{{ $doc->pages_count }}</td>
                                        <td>{{ $doc->file_size_human ?? '' }}</td>
                                        <td>{{ $doc->created_at->format('d/m/Y H:i') }}</td>
                                        <td class="text-end">
                                            <a href="/documents/{{ $doc->id }}/preview" class="btn btn-sm btn-outline-primary" title="Prévisualiser">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($doc->status !== 'signed')
                                                <a href="/signatures/{{ $doc->id }}/preview" class="btn btn-sm btn-outline-success ms-1" title="Continuer">
                                                    <i class="fas fa-play"></i>
                                                </a>
                                            @endif
                                            @if($doc->status === 'signed')
                                                <a href="/documents/{{ $doc->id }}/download" class="btn btn-sm btn-outline-secondary ms-1" title="Télécharger">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    {{ $documents->links() }}
                </div>
            </div>
        @endif
    </div>
@endsection
