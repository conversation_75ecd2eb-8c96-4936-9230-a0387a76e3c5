<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class DashboardController extends Controller
{
    private $apiBaseUrl;

    public function __construct()
    {
        $this->apiBaseUrl = config('signsecure.api.url');
    }

    public function index(Request $request)
    {
        $token = $request->session()->get('api_token');
        
        if (!$token) {
            return redirect()->route('login');
        }

        // Vue simple, JavaScript gère tout
        return view('dashboard');
    }
}



