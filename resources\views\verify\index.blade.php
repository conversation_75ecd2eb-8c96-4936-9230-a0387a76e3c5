@extends("layouts.app")

@section("title", "Vérifier un document - SignSecure")

@section("content")
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0"><i class="fas fa-shield-alt me-2 text-success"></i>Vérifier l'authenticité d'un document</h5>
                    <a href="{{ url('/') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        Saisissez le code de suivi du document (ex: SS-ABCD-1234 ou SS-PR-ABCD-1234).
                    </div>

                    <form id="verifyForm" class="mb-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                            <input type="text" id="verifyCode" class="form-control" placeholder="Code de suivi" required style="text-transform: uppercase;">
                            <button type="submit" class="btn btn-success" id="verifySubmit">
                                <i class="fas fa-search me-1"></i>Vérifier
                            </button>
                        </div>
                    </form>

                    <div id="verifyError" class="alert alert-danger d-none"></div>

                    <div id="verifyResults" class="d-none">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <strong id="resultTitle"><i class="fas fa-check-circle me-2"></i>Résultat</strong>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong id="labelA">Nom du document :</strong> <span id="valueA">-</span></p>
                                        <p class="mb-1"><strong id="labelB">Signataire :</strong> <span id="valueB">-</span></p>
                                        <p class="mb-1"><strong id="labelC">Email :</strong> <span id="valueC">-</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong id="labelD">Date de signature :</strong> <span id="valueD">-</span></p>
                                        <p class="mb-1"><strong>Code de suivi :</strong> <span class="badge bg-primary" id="valueCode">-</span></p>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a class="btn btn-outline-primary me-2" id="btnDownload" href="#" target="_blank">
                                        <i class="fas fa-download me-1"></i>Télécharger l'original
                                    </a>
                                    <button class="btn btn-outline-info" id="btnReport" type="button">
                                        <i class="fas fa-file-alt me-1"></i>Générer un rapport
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push("scripts")
<script>
document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('verifyForm');
    const codeInput = document.getElementById('verifyCode');
    const submitBtn = document.getElementById('verifySubmit');
    const errorBox = document.getElementById('verifyError');
    const results = document.getElementById('verifyResults');
    const btnDownload = document.getElementById('btnDownload');
    const btnReport = document.getElementById('btnReport');

    const el = (id) => document.getElementById(id);

    codeInput.addEventListener('input', (e) => { e.target.value = e.target.value.toUpperCase(); });

    async function postJSON(url, data) {
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
            body: JSON.stringify(data),
        });
        if (!res.ok) {
            let msg = 'Erreur serveur';
            try { const j = await res.json(); msg = j.message || msg; } catch (_) {}
            throw new Error(msg);
        }
        return res.json();
    }

    form.addEventListener('submit', async function (e) {
        e.preventDefault();
        const trackingCode = codeInput.value.trim();
        if (!trackingCode) return;
        errorBox.classList.add('d-none');
        results.classList.add('d-none');

        const original = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Vérification...';

        try {
            const data = await postJSON('{{ route('verify-document') }}', { tracking_code: trackingCode });

            // Document signé
            if (data.data && data.data.document) {
                el('resultTitle').innerHTML = '<i class="fas fa-check-circle me-2"></i>Document authentique vérifié';
                el('labelA').textContent = 'Nom du document :';
                el('labelB').textContent = 'Signataire :';
                el('labelC').textContent = 'Email :';
                el('labelD').textContent = 'Date de signature :';

                el('valueA').textContent = data.data.document.original_filename || 'N/A';
                el('valueB').textContent = (data.data.signer && data.data.signer.name) || 'N/A';
                el('valueC').textContent = (data.data.signer && data.data.signer.email) || 'N/A';
                el('valueD').textContent = data.data.document.signed_at ? new Date(data.data.document.signed_at).toLocaleString('fr-FR') : 'N/A';
                el('valueCode').textContent = data.data.document.tracking_code || trackingCode;

                btnDownload.href = '{{ route('download-document.get') }}' + '?tracking_code=' + encodeURIComponent(trackingCode);
                btnDownload.classList.remove('disabled');
                btnDownload.target = '_blank';

                btnReport.onclick = async function () {
                    const blobRes = await fetch('{{ route('generate-report') }}', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: new URLSearchParams({ tracking_code: trackingCode })
                    });
                    if (!blobRes.ok) throw new Error('Erreur génération rapport');
                    const blob = await blobRes.blob();
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'verification_report_' + trackingCode + '.pdf';
                    document.body.appendChild(a);
                    a.click();
                    URL.revokeObjectURL(url);
                    a.remove();
                };
            } else if (data.type === 'physical_retrace') { // Retrace physique
                el('resultTitle').innerHTML = '<i class="fas fa-check-circle me-2"></i>Retrace physique vérifié';
                el('labelA').textContent = 'Objet du document :';
                el('labelB').textContent = 'Référence :';
                el('labelC').textContent = 'Date :';
                el('labelD').textContent = 'Notes :';

                el('valueA').textContent = data.data.subject || 'N/A';
                el('valueB').textContent = data.data.reference || 'N/A';
                el('valueC').textContent = data.data.date || 'N/A';
                el('valueD').textContent = data.data.notes || 'N/A';
                el('valueCode').textContent = data.data.tracking_code || trackingCode;

                btnDownload.href = '{{ route('download-by-code.get') }}' + '?tracking_code=' + encodeURIComponent(trackingCode);
                btnDownload.classList.remove('disabled');
                btnDownload.target = '_blank';

                btnReport.onclick = async function () {
                    const blobRes = await fetch('{{ route('generate-report') }}', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: new URLSearchParams({ tracking_code: trackingCode })
                    });
                    if (!blobRes.ok) throw new Error('Erreur génération rapport');
                    const blob = await blobRes.blob();
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'verification_report_' + trackingCode + '.pdf';
                    document.body.appendChild(a);
                    a.click();
                    URL.revokeObjectURL(url);
                    a.remove();
                };
            }

            results.classList.remove('d-none');
        } catch (err) {
            errorBox.textContent = err.message || 'Code de suivi invalide ou non trouvé.';
            errorBox.classList.remove('d-none');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = original;
        }
    });
});
</script>
@endpush


