/**
 * Script pour la page de création/capture de signature
 */

document.addEventListener("DOMContentLoaded", function() {
    const signaturePadCanvas = document.getElementById("signaturePad");
    const clearSignatureBtn = document.getElementById("clearSignatureBtn");
    const saveSignatureBtn = document.getElementById("saveSignatureBtn");
    const signaturePreviewImg = document.getElementById("signaturePreviewImg");
    const originalSaveBtnText = saveSignatureBtn.innerHTML;

    let signaturePad = new SignaturePad(signaturePadCanvas, {
        backgroundColor: 'rgb(255, 255, 255)' // important pour le fond blanc
    });

    // Charger la signature existante de l'utilisateur
    async function loadExistingSignature() {
        try {
            const response = await signSecureAPI.getCurrentSignature();
            if (response.data && response.data.signature_image) {
                signaturePad.fromDataURL(response.data.signature_image);
                signaturePreviewImg.src = response.data.signature_image;
                signaturePreviewImg.style.display = 'block';
                SignSecureUI.showToast("Votre signature précédente a été chargée.", "info");
            }
        } catch (error) {
            // Si aucune signature n'existe, c'est normal, ne rien faire
            console.log("Aucune signature existante ou erreur de chargement:", error.message);
        }
    }

    // Redimensionner le canvas
    function resizeCanvas() {
        const ratio = Math.max(window.devicePixelRatio || 1, 1);
        signaturePadCanvas.width = signaturePadCanvas.offsetWidth * ratio;
        signaturePadCanvas.height = signaturePadCanvas.offsetHeight * ratio;
        signaturePadCanvas.getContext("2d").scale(ratio, ratio);
        signaturePad.clear(); // Efface le contenu après redimensionnement
        // Recharger la signature si elle existait
        if (signaturePreviewImg.src && signaturePreviewImg.style.display !== 'none') {
            signaturePad.fromDataURL(signaturePreviewImg.src);
        }
    }

    window.addEventListener("resize", resizeCanvas);
    resizeCanvas(); // Appel initial

    // Effacer la signature
    clearSignatureBtn.addEventListener("click", function() {
        signaturePad.clear();
        signaturePreviewImg.style.display = 'none';
        signaturePreviewImg.src = '';
        SignSecureUI.showToast("Signature effacée.", "info");
    });

    // Sauvegarder la signature
    saveSignatureBtn.addEventListener("click", async function() {
        if (signaturePad.isEmpty()) {
            SignSecureUI.showToast("Veuillez dessiner votre signature d'abord.", "error");
            return;
        }

        SignSecureUI.showLoader(saveSignatureBtn);

        try {
            const signatureData = signaturePad.toDataURL(); // Par défaut en PNG
            const response = await signSecureAPI.captureSignature(signatureData);
            
            signaturePreviewImg.src = signatureData;
            signaturePreviewImg.style.display = 'block';

            SignSecureUI.showToast("Signature enregistrée avec succès !", "success");
            // Optionnel: Rediriger ou fermer la modale
            // setTimeout(() => { window.location.href = '/dashboard'; }, 1000);

        } catch (error) {
            SignSecureUI.showToast(error.message || "Erreur lors de l'enregistrement de la signature.", "error");
        } finally {
            SignSecureUI.hideLoader(saveSignatureBtn, originalSaveBtnText);
        }
    });

    // Initialisation
    loadExistingSignature();
});
