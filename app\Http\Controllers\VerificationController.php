<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Document;

class VerificationController extends Controller
{
    public function index()
    {
        return view('verification.index');
    }

    public function verify(Request $request)
    {
        $request->validate([
            'tracking_code' => 'required|string'
        ]);

        // Cette méthode sera gérée par l'API
        return redirect()->back();
    }

    public function show($trackingCode)
    {
        $document = Document::where('tracking_code', $trackingCode)->first();
        
        if (!$document) {
            abort(404, 'Document non trouvé');
        }

        return view('verification.show', compact('document'));
    }

    public function download($trackingCode)
    {
        $document = Document::where('tracking_code', $trackingCode)->first();
        
        if (!$document) {
            abort(404, 'Document non trouvé');
        }

        return response()->download($document->signed_file_path);
    }
}
