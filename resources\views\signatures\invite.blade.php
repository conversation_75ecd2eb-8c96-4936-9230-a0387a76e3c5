@extends('layouts.app')

@section('title', 'Inviter des signataires - SignSecure')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-users me-3" style="font-size: 1.5rem;"></i>
                        <div>
                            <h5 class="mb-0">Inviter des signataires</h5>
                            <small>{{ $document->original_filename }}</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- Document Info -->
                    <div class="bg-light rounded-3 p-3 mb-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-file-pdf text-danger fs-2"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">{{ $document->original_filename }}</h6>
                                <small class="text-muted">
                                    {{ $document->pages_count }} page(s) • {{ $document->file_size_human }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Current Signers -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-list text-primary me-2"></i>
                            Signataires actuels
                        </h6>
                        <div class="signers-list">
                            @foreach($document->signers as $signer)
                            <div class="signer-item d-flex align-items-center p-3 border rounded-3 mb-2 {{ $signer->is_current_signer ? 'border-primary bg-primary bg-opacity-10' : '' }}">
                                <div class="signer-avatar me-3">
                                    <div class="avatar-circle {{ $signer->status === 'signed' ? 'bg-success' : ($signer->is_current_signer ? 'bg-primary' : 'bg-secondary') }}">
                                        @if($signer->status === 'signed')
                                            <i class="fas fa-check text-white"></i>
                                        @else
                                            <span class="text-white">{{ $signer->signature_order }}</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="signer-info flex-grow-1">
                                    <div class="signer-name fw-semibold">{{ $signer->name }}</div>
                                    <div class="signer-email text-muted small">{{ $signer->email }}</div>
                                    <div class="signer-status text-muted small">
                                        @if($signer->status === 'signed')
                                            Signé le {{ $signer->signed_at->format('d/m/Y H:i') }}
                                        @elseif($signer->is_current_signer)
                                            <span class="text-primary">En cours de signature</span>
                                        @else
                                            En attente (ordre {{ $signer->signature_order }})
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Invite Form -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-plus text-success me-2"></i>
                            Ajouter des signataires
                        </h6>
                        
                        <form id="inviteForm">
                            <div id="signersContainer">
                                <div class="signer-input-group mb-3">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label class="form-label">Nom complet *</label>
                                            <input type="text" class="form-control signer-name" name="signers[0][name]" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Email *</label>
                                            <input type="email" class="form-control signer-email" name="signers[0][email]" required>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Ordre de signature *</label>
                                            <input type="number" class="form-control signer-order" name="signers[0][signature_order]" min="1" value="{{ $document->signers->count() + 1 }}" required>
                                        </div>
                                        <div class="col-md-1 d-flex align-items-end">
                                            <button type="button" class="btn btn-outline-danger btn-sm remove-signer" style="display: none;">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addSignerBtn">
                                    <i class="fas fa-plus me-1"></i>
                                    Ajouter un signataire
                                </button>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        Envoyer les invitations
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Instructions -->
                    <div class="alert alert-info">
                        <div class="d-flex">
                            <i class="fas fa-info-circle me-2 mt-1"></i>
                            <div>
                                <strong>Comment fonctionne l'ordre de signature :</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Les signataires signent dans l'ordre numérique (1, 2, 3...)</li>
                                    <li>Chaque signataire doit attendre que le précédent ait signé</li>
                                    <li>Vous pouvez modifier l'ordre en changeant les numéros</li>
                                    <li>Les invitations expirent automatiquement après 7 jours</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const inviteForm = document.getElementById('inviteForm');
    const signersContainer = document.getElementById('signersContainer');
    const addSignerBtn = document.getElementById('addSignerBtn');
    let signerIndex = 1;

    // Ajouter un signataire
    addSignerBtn.addEventListener('click', function() {
        const signerGroup = document.createElement('div');
        signerGroup.className = 'signer-input-group mb-3';
        signerGroup.innerHTML = `
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Nom complet *</label>
                    <input type="text" class="form-control signer-name" name="signers[${signerIndex}][name]" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Email *</label>
                    <input type="email" class="form-control signer-email" name="signers[${signerIndex}][email]" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Ordre de signature *</label>
                    <input type="number" class="form-control signer-order" name="signers[${signerIndex}][signature_order]" min="1" value="${signerIndex + {{ $document->signers->count() }}}" required>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-danger btn-sm remove-signer">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        
        signersContainer.appendChild(signerGroup);
        signerIndex++;
        
        // Afficher le bouton de suppression pour le premier groupe
        if (signersContainer.children.length > 1) {
            document.querySelector('.remove-signer').style.display = 'block';
        }
    });

    // Supprimer un signataire
    signersContainer.addEventListener('click', function(e) {
        if (e.target.closest('.remove-signer')) {
            const signerGroup = e.target.closest('.signer-input-group');
            signerGroup.remove();
            
            // Masquer le bouton de suppression s'il ne reste qu'un groupe
            if (signersContainer.children.length === 1) {
                document.querySelector('.remove-signer').style.display = 'none';
            }
        }
    });

    // Soumettre le formulaire
    inviteForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const submitBtn = inviteForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Envoi...';

        try {
            // Collecter les données du formulaire
            const formData = new FormData(inviteForm);
            const signers = [];
            
            // Parser les données du formulaire
            for (let [key, value] of formData.entries()) {
                const match = key.match(/signers\[(\d+)\]\[(\w+)\]/);
                if (match) {
                    const index = parseInt(match[1]);
                    const field = match[2];
                    
                    if (!signers[index]) {
                        signers[index] = {};
                    }
                    signers[index][field] = value;
                }
            }

            // Valider les données
            if (signers.length === 0) {
                throw new Error('Veuillez ajouter au moins un signataire');
            }

            // Vérifier les doublons d'email
            const emails = signers.map(s => s.email);
            const uniqueEmails = [...new Set(emails)];
            if (emails.length !== uniqueEmails.length) {
                throw new Error('Des adresses email en double ont été détectées');
            }

            // Envoyer les invitations
            const response = await fetch(`/signatures/{{ $document->id }}/invite`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    signers: signers
                })
            });

            const result = await response.json();

            if (result.success) {
                toastr.success(result.message);
                setTimeout(() => {
                    window.location.href = '{{ route("documents.index") }}';
                }, 1500);
            } else {
                throw new Error(result.message || 'Erreur lors de l\'envoi des invitations');
            }

        } catch (error) {
            console.error('Erreur:', error);
            toastr.error(error.message || 'Erreur lors de l\'envoi des invitations');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    });
});
</script>
@endpush

@push('styles')
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.signer-item {
    transition: all 0.3s ease;
}

.signer-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.signer-input-group {
    transition: all 0.3s ease;
}

.signer-input-group:hover {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.5rem;
    margin: -0.5rem;
}
</style>
@endpush
@endsection


