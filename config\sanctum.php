<?php

use <PERSON><PERSON>\Sanctum\Sanctum;

return [

    /*
    |--------------------------------------------------------------------------
    | Stateful Domains
    |--------------------------------------------------------------------------
    |
    | These are the domains that should be considered "stateful" when
    | authenticating API requests. This includes your local development
    | domains as well as production domains that you authenticate from.
    |
    | They should be the URL of your frontend application which calls the API.
    |
    */

    'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
        '%s%s', 
        'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
        env('APP_URL') ? ','.parse_url(env('APP_URL'), PHP_URL_HOST) : ''
    ))),

    /*
    |--------------------------------------------------------------------------
    | Sanctum Guard
    |--------------------------------------------------------------------------
    |
    | This is the authentication guard that will be used by Laravel Sanctum
    | to authenticate incoming requests. You may use any guard that is
    | defined in your authentication configuration file.
    |
    */

    'guard' => ['web'],

    /*
    |--------------------------------------------------------------------------
    | Expiration Minutes
    |--------------------------------------------------------------------------
    |
    | This value controls the number of minutes until an issued token will
    | be considered expired. If this value is null, personal access tokens will
    | never expire. You should set to a reasonable amount of time.
    |
    */

    'expiration' => 60 * 24 * 7, // 7 jours

    /*
    |--------------------------------------------------------------------------
    | Sanctum Middleware
    |--------------------------------------------------------------------------
    |
    | When authenticating your first-party SPA requests, Sanctum will need
    | to authenticate the session and fetch the CSRF token. You may enable
    | a custom middleware for this purpose below.
    |
    */

    'middleware' => [
        'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
        'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
    ],

];