<?php

namespace App\Services;

use setasign\Fpdi\Fpdi;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PDFService
{
    /**
     * Récupère le nombre de pages d\un fichier PDF.
     *
     * @param string $filePath Chemin absolu du fichier PDF.
     * @return int Nombre de pages.
     */
    public function getPageCount(string $filePath): int
    {
        try {
            $pdf = new Fpdi();
            $pageCount = $pdf->setSourceFile($filePath);
            return $pageCount;
        } catch (\Exception $e) {
            // Gérer l\erreur, par exemple, logguer l\exception
            throw new \Exception("Impossible de lire le nombre de pages du PDF: " . $e->getMessage());
        }
    }

    /**
     * Applique une signature (image) sur un document PDF.
     *
     * @param string $pdfPath Chemin du fichier PDF original (dans le storage).
     * @param string $signatureImagePath Chemin de l\image de signature (dans le storage).
     * @param float $positionX Coordonnée X de la signature sur la page.
     * @param float $positionY Coordonnée Y de la signature sur la page.
     * @param int $pageNumber Numéro de la page où appliquer la signature.
     * @param float $signatureWidth Largeur de la signature en mm (par défaut 50mm).
     * @param float $signatureHeight Hauteur de la signature en mm (par défaut 25mm).
     * @return string Chemin du fichier PDF signé (dans le storage).
     * @throws \Exception
     */
    public function applySignature(
    string $pdfPath,
    string $signaturePath,
    float $positionX,
    float $positionY,
    int $pageNumber,
    float $signatureWidth = 50,
    float $signatureHeight = 25
): string
{
    $pdf = new Fpdi();

    // Chemins complets depuis le storage privé
    $fullPdfPath = Storage::disk("private")->path($pdfPath);
    $fullSignaturePath = Storage::disk("private")->path($signaturePath);

    if (!file_exists($fullPdfPath)) {
        throw new \Exception("Fichier PDF original introuvable: {$fullPdfPath}");
    }

    if (!file_exists($fullSignaturePath)) {
        throw new \Exception("Fichier signature introuvable: {$fullSignaturePath}");
    }

    try {
        $pageCount = $pdf->setSourceFile($fullPdfPath);

        if ($pageNumber < 1 || $pageNumber > $pageCount) {
            throw new \Exception("Numéro de page invalide pour la signature.");
        }

        for ($i = 1; $i <= $pageCount; $i++) {
            $tplId = $pdf->importPage($i);
            $size = $pdf->getTemplateSize($tplId);
            $pdf->AddPage($size["orientation"], [$size["width"], $size["height"]]);
            $pdf->useTemplate($tplId);

            // Appliquer la signature sur la page spécifiée
            if ($i === $pageNumber) {
                // Conversion pixels → mm (résolution 96 DPI)
                $pixelToMm = 25.4 / 96;
                $xMm = $positionX * $pixelToMm;
                $yMm = $positionY * $pixelToMm;

                $pdf->Image($fullSignaturePath, $xMm, $yMm, $signatureWidth, $signatureHeight);

                // Ajouter mention textuelle sous la signature
                $userName = \Illuminate\Support\Facades\Auth::user()?->name ?? 'Utilisateur';
                $now = now()->format('d/m/Y H:i');
                $footerText = iconv('UTF-8', 'ISO-8859-1//TRANSLIT', 'Document numériquement signé par ' . $userName . ' le ' . $now);
                $pdf->SetFont('Helvetica', '', 9);
                $pdf->SetTextColor(60, 60, 60);
                // placer le texte juste en dessous de l'image
                $textY = $yMm + $signatureHeight + 4;
                $pdf->SetXY(max(10, $xMm), $textY);
                $pdf->Cell(0, 5, $footerText, 0, 0, 'L');
            }
        }

        // Nom unique pour le PDF signé
        $signedFilename = 'signed_' . Str::uuid() . '.pdf';
        $signedFilePath = 'documents/signed/' . $signedFilename;
        $fullSignedFilePath = Storage::disk('private')->path($signedFilePath);

        // Sauvegarder le PDF signé
        $pdf->Output($fullSignedFilePath, 'F');

        return $signedFilePath;

    } catch (\Exception $e) {
        throw new \Exception("Erreur lors de l'application de la signature: " . $e->getMessage());
    }
}

    /**
     * Applique une signature en recevant des coordonnées en pourcentage (0..1) de la page
     * et centre l'image de signature sur ce point. Gère correctement l'axe Y.
     */
    public function applySignatureUsingPercent(
        string $pdfPath,
        string $signaturePath,
        float $xPct,
        float $yPct,
        int $pageNumber,
        float $signatureWidth = 50,
        float $signatureHeight = 25
    ): string {
        $pdf = new Fpdi();
        $fullPdfPath = Storage::disk('private')->path($pdfPath);
        $fullSignaturePath = Storage::disk('private')->path($signaturePath);

        if (!file_exists($fullPdfPath)) {
            throw new \Exception("Fichier PDF original introuvable: {$fullPdfPath}");
        }
        if (!file_exists($fullSignaturePath)) {
            throw new \Exception("Fichier signature introuvable: {$fullSignaturePath}");
        }

        try {
            $pageCount = $pdf->setSourceFile($fullPdfPath);
            if ($pageNumber < 1 || $pageNumber > $pageCount) {
                throw new \Exception('Numéro de page invalide pour la signature.');
            }

            for ($i = 1; $i <= $pageCount; $i++) {
                $tplId = $pdf->importPage($i);
                $size = $pdf->getTemplateSize($tplId);
                $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                $pdf->useTemplate($tplId);

                if ($i === $pageNumber) {
                    // Convertit xPct/yPct en coordonnées mm relative à la page
                    $xCenter = $xPct * $size['width'];
                    $yCenterFromTop = $yPct * $size['height'];
                    // FPDI place Y depuis le haut par défaut quand on a importé via useTemplate (mm)
                    // On centre l'image sur le point choisi
                    $xMm = $xCenter - ($signatureWidth / 2.0);
                    $yMm = $yCenterFromTop - ($signatureHeight / 2.0);

                    $pdf->Image($fullSignaturePath, $xMm, $yMm, $signatureWidth, $signatureHeight);

                    // Ajouter mention textuelle sous la signature
                    $userName = \Illuminate\Support\Facades\Auth::user()?->name ?? 'Utilisateur';
                    $now = now()->format('d/m/Y H:i');
                    $footerText = iconv('UTF-8', 'ISO-8859-1//TRANSLIT', 'Document numériquement signé par ' . $userName . ' le ' . $now);
                    $pdf->SetFont('Helvetica', '', 9);
                    $pdf->SetTextColor(60, 60, 60);
                    $textY = $yMm + $signatureHeight + 4;
                    $pdf->SetXY(max(10, $xMm), $textY);
                    $pdf->Cell(0, 5, $footerText, 0, 0, 'L');
                }
            }

            $signedFilename = 'signed_' . Str::uuid() . '.pdf';
            $signedFilePath = 'documents/signed/' . $signedFilename;
            $fullSignedFilePath = Storage::disk('private')->path($signedFilePath);
            $pdf->Output($fullSignedFilePath, 'F');
            return $signedFilePath;
        } catch (\Exception $e) {
            throw new \Exception("Erreur lors de l'application de la signature (percent): " . $e->getMessage());
        }
    }

    /**
     * Ajoute un code de suivi (tracking code) à un document PDF.
     *
     * @param string $pdfPath Chemin du fichier PDF (dans le storage).
     * @param string $trackingCode Le code de suivi à ajouter.
     * @return string Chemin du fichier PDF mis à jour (dans le storage).
     * @throws \Exception
     */
    public function addTrackingCode(string $pdfPath, string $trackingCode): string
    {
        $pdf = new Fpdi();
        $fullPdfPath = Storage::disk("private")->path($pdfPath);

        if (!file_exists($fullPdfPath)) {
            throw new \Exception("Fichier PDF introuvable: " . $fullPdfPath);
        }

        try {
            // Désactiver les sauts de page automatiques pour garder le texte au bas de la page
            $pdf->SetAutoPageBreak(false);

            $pageCount = $pdf->setSourceFile($fullPdfPath);

            for ($i = 1; $i <= $pageCount; $i++) {
                $tplId = $pdf->importPage($i);
                $size = $pdf->getTemplateSize($tplId);
                $pdf->AddPage($size["orientation"], [$size["width"], $size["height"]]);
                $pdf->useTemplate($tplId);

                // Ajouter le code uniquement sur la DERNIÈRE page
                if ($i === $pageCount) {
                    $pdf->SetFont('Helvetica', '', 8);
                    $pdf->SetTextColor(150, 150, 150); // Gris
                    // Marge intérieure et position bas-droite
                    $margin = 10;
                    // Se placer 10mm au-dessus du bas de page, largeur auto, aligné à droite
                    $pdf->SetXY($margin, $size['height'] - $margin - 5);
                    $pdf->Cell(0, 5, 'Code de suivi: ' . strtoupper($trackingCode), 0, 0, 'R');
                }
            }

            $updatedFilename = 'tracked_' . Str::uuid() . '.pdf';
            $updatedFilePath = 'documents/tracked/' . $updatedFilename;
            \Illuminate\Support\Facades\Storage::disk('private')->makeDirectory('documents/tracked');
            $fullUpdatedFilePath = Storage::disk('private')->path($updatedFilePath);

            $pdf->Output($fullUpdatedFilePath, 'F');

            return $updatedFilePath;

        } catch (\Exception $e) {
            throw new \Exception("Erreur lors de l\ajout du code de suivi au PDF: " . $e->getMessage());
        }
    }

    /**
     * Génère un PDF blanc avec une zone d'en-tête et le code de suivi en bas.
     */
    public function generateBlankWithTracking(string $trackingCode, string $title, array $fields = []): string
    {
        $pdf = new Fpdi();
        // Créer une page A4 portrait (mm)
        $width = 210; $height = 297;
        $pdf->AddPage('P', [$width, $height]);

        // Titre
        $pdf->SetFont('Helvetica', 'B', 16);
        $pdf->SetXY(15, 20);
        $pdf->Cell(0, 10, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', $title), 0, 1, 'L');

        // Champs
        $pdf->SetFont('Helvetica', '', 11);
        $y = 40;
        foreach ($fields as $label => $value) {
            $pdf->SetXY(15, $y);
            $pdf->Cell(40, 8, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', $label . ':'), 0, 0, 'L');
            $pdf->SetXY(60, $y);
            $pdf->MultiCell(130, 8, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', (string) $value));
            $y += 10;
            if ($y > 250) break;
        }

        // Code de suivi en bas
        $pdf->SetFont('Helvetica', '', 8);
        $pdf->SetTextColor(150, 150, 150);
        $margin = 10;
        $pdf->SetXY($margin, $height - $margin);
        $pdf->Cell(0, 5, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', 'Code de suivi: ') . strtoupper($trackingCode), 0, 0, 'R');

        // Sauvegarde
        $updatedFilename = 'retrace_' . \Illuminate\Support\Str::uuid() . '.pdf';
        $updatedFilePath = 'documents/tracked/' . $updatedFilename;
        \Illuminate\Support\Facades\Storage::disk('private')->makeDirectory('documents/tracked');
        $fullUpdatedFilePath = \Illuminate\Support\Facades\Storage::disk('private')->path($updatedFilePath);
        $pdf->Output($fullUpdatedFilePath, 'F');

        return $updatedFilePath;
    }

    /**
     * Génère un PDF blanc qui ne contient QUE le code de suivi en bas de page.
     * Aucun titre ni champ n'est affiché.
     */
    public function generateBlankWithTrackingCodeOnly(string $trackingCode): string
    {
        $pdf = new Fpdi();
        // Page A4 portrait
        $width = 210; $height = 297;
        $pdf->AddPage('P', [$width, $height]);
        // Désactiver les sauts de page automatiques pour éviter une nouvelle page
        $pdf->SetAutoPageBreak(false);

        // Code de suivi discret en bas à droite
        $pdf->SetFont('Helvetica', '', 8);
        $pdf->SetTextColor(150, 150, 150);
        $margin = 10;
        // Se placer 10mm au-dessus du bas de page, largeur auto, aligné à droite
        $pdf->SetXY($margin, $height - $margin - 5);
        $pdf->Cell(0, 5, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', 'Code de suivi: ') . strtoupper($trackingCode), 0, 0, 'R');

        // Sauvegarde
        $filename = 'retrace_' . Str::uuid() . '.pdf';
        $filePath = 'documents/tracked/' . $filename;
        \Illuminate\Support\Facades\Storage::disk('private')->makeDirectory('documents/tracked');
        $fullPath = \Illuminate\Support\Facades\Storage::disk('private')->path($filePath);
        $pdf->Output($fullPath, 'F');

        return $filePath;
    }

    /**
     * Génère un PDF de rapport simple avec un titre et des sections clé/valeur.
     * Retourne le chemin relatif dans le disque privé.
     */
    public function generateVerificationReportPdf(string $title, array $sections): string
    {
        $pdf = new Fpdi();
        $width = 210; $height = 297; // A4
        $pdf->AddPage('P', [$width, $height]);
        $pdf->SetAutoPageBreak(true, 20);

        // Titre
        $pdf->SetFont('Helvetica', 'B', 16);
        $pdf->SetXY(15, 20);
        $pdf->Cell(0, 10, $title, 0, 1, 'L');

        // Contenu
        $pdf->SetFont('Helvetica', '', 11);
        $y = 35;
        foreach ($sections as $sectionTitle => $pairs) {
            // Section header
            $pdf->SetFont('Helvetica', 'B', 12);
            $pdf->SetXY(15, $y);
            $pdf->Cell(0, 8, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', (string) $sectionTitle), 0, 1, 'L');
            $y += 8;

            $pdf->SetFont('Helvetica', '', 11);
            foreach ($pairs as $label => $value) {
                if ($y > 270) {
                    $pdf->AddPage('P', [$width, $height]);
                    $y = 20;
                }
                $pdf->SetXY(15, $y);
                $pdf->Cell(50, 6, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', (string) $label . ':'), 0, 0, 'L');
                $pdf->SetXY(65, $y);
                $pdf->MultiCell(130, 6, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', (string) ($value ?? '-')));
                $y += 8;
            }
            $y += 3;
        }

        // Pied de page léger (tracking si présent)
        $pdf->SetFont('Helvetica', '', 8);
        $pdf->SetTextColor(150, 150, 150);
        $pdf->SetXY(10, $height - 10);
        $pdf->Cell(0, 5, iconv('UTF-8', 'ISO-8859-1//TRANSLIT', 'Rapport SignSecure'), 0, 0, 'R');

        // Sauvegarde
        $filename = 'verification_report_' . Str::uuid() . '.pdf';
        $filePath = 'reports/' . $filename;
        \Illuminate\Support\Facades\Storage::disk('private')->makeDirectory('reports');
        $fullPath = \Illuminate\Support\Facades\Storage::disk('private')->path($filePath);
        $pdf->Output($fullPath, 'F');

        return $filePath;
    }
}
