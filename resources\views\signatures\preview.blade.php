@extends('layouts.app')

@section('title', 'Positionnement de signature - SignSecure')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8">
            <!-- PDF Viewer -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-pdf text-danger me-2"></i>
                            <h6 class="mb-0">{{ $document->original_filename }}</h6>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <span class="badge bg-info">{{ $document->pages_count }} page(s)</span>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-secondary" onclick="previousPage()" id="prevPageBtn">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <span class="btn btn-outline-secondary" id="pageInfo">
                                    Page <span id="currentPage">1</span> sur <span id="totalPages">{{ $document->pages_count }}</span>
                                </span>
                                <button class="btn btn-outline-secondary" onclick="nextPage()" id="nextPageBtn">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="pdf-viewer-container position-relative">
                        <div id="pdfViewer" class="text-center">
                            <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement du PDF...</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Signature Position Marker -->
                        <div id="signatureMarker" class="signature-marker d-none">
                            <div class="signature-placeholder">
                                <i class="fas fa-signature"></i>
                                <span>Signature ici</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Signature Progress -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-users text-primary me-2"></i>
                        Progression des signatures
                    </h6>
                </div>
                <div class="card-body">
                    <div class="signers-progress">
                        @foreach($document->signers as $signer)
                        <div class="signer-item d-flex align-items-center mb-3 {{ $signer->is_current_signer ? 'current-signer' : '' }}">
                            <div class="signer-avatar me-3">
                                <div class="avatar-circle {{ $signer->status === 'signed' ? 'bg-success' : ($signer->is_current_signer ? 'bg-primary' : 'bg-secondary') }}">
                                    @if($signer->status === 'signed')
                                        <i class="fas fa-check text-white"></i>
                                    @else
                                        <span class="text-white">{{ $signer->signature_order }}</span>
                                    @endif
                                </div>
                            </div>
                            <div class="signer-info flex-grow-1">
                                <div class="signer-name fw-semibold">{{ $signer->name }}</div>
                                <div class="signer-status text-muted small">
                                    @if($signer->status === 'signed')
                                        Signé le {{ $signer->signed_at->format('d/m/Y H:i') }}
                                    @elseif($signer->is_current_signer)
                                        <span class="text-primary">En cours de signature</span>
                                    @else
                                        En attente
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Your Signature -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-pen-nib text-primary me-2"></i>
                        Votre signature
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="signature-preview mb-3">
                        <img src="{{ Auth::user()->signature_image }}" alt="Ma signature" class="img-fluid" style="max-height: 80px;">
                    </div>
                    <small class="text-muted">Cette signature sera appliquée sur le document</small>
                </div>
            </div>

            <!-- Signature Position -->
            <div class="card shadow-sm mb-4" id="positionCard" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-crosshairs text-success me-2"></i>
                        Position sélectionnée
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2 mb-3">
                        <div class="col-6">
                            <small class="text-muted">Page :</small>
                            <div class="fw-semibold" id="selectedPage">-</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Position :</small>
                            <div class="fw-semibold" id="selectedPosition">-</div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="applySignature()" id="applyBtn">
                            <i class="fas fa-check me-2"></i>
                            Appliquer la signature
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearPosition()">
                            <i class="fas fa-times me-1"></i>
                            Effacer la position
                        </button>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        Instructions
                    </h6>
                </div>
                <div class="card-body">
                    <ol class="mb-0 ps-3">
                        <li class="mb-2">Naviguez entre les pages si nécessaire</li>
                        <li class="mb-2">Cliquez à l'endroit exact où vous souhaitez placer votre signature</li>
                        <li class="mb-0">Validez la position en cliquant sur "Appliquer la signature"</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Form for signature position -->
<form id="signatureForm" style="display: none;">
    <input type="hidden" name="position_x" id="positionX">
    <input type="hidden" name="position_y" id="positionY">
    <input type="hidden" name="page_number" id="pageNumber" value="1">
</form>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script>
// PDF.js configuration
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

let pdfDoc = null;
let pageNum = 1;
let pageRendering = false;
let pageNumPending = null;
let scale = 1.0;
let canvas = null;
let ctx = null;

// Signature positions (allow multiple)
let signaturePositions = [];

const documentId = {{ $document->id }};

document.addEventListener('DOMContentLoaded', function() {
    loadPDF();
});

async function loadPDF() {
    try {
        const loadingTask = pdfjsLib.getDocument('{{ route("documents.pdf", $document) }}');
        pdfDoc = await loadingTask.promise;
        
        document.getElementById('totalPages').textContent = pdfDoc.numPages;
        renderPage(pageNum);
    } catch (error) {
        console.error('Error loading PDF:', error);
        document.getElementById('pdfViewer').innerHTML = 
            '<div class="alert alert-danger">Erreur lors du chargement du PDF</div>';
    }
}

async function renderPage(num) {
    pageRendering = true;
    
    try {
        const page = await pdfDoc.getPage(num);
        const viewport = page.getViewport({ scale: scale });
        
        // Create canvas if it doesn't exist
        if (!canvas) {
            canvas = document.createElement('canvas');
            canvas.className = 'pdf-canvas';
            canvas.style.cursor = 'crosshair';
            canvas.addEventListener('click', handleCanvasClick);
            
            const viewer = document.getElementById('pdfViewer');
            viewer.innerHTML = '';
            viewer.appendChild(canvas);
        }
        
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        ctx = canvas.getContext('2d');
        
        const renderContext = {
            canvasContext: ctx,
            viewport: viewport
        };
        
        await page.render(renderContext).promise;
        
        pageRendering = false;
        
        if (pageNumPending !== null) {
            renderPage(pageNumPending);
            pageNumPending = null;
        }
        
        // Update UI
        document.getElementById('currentPage').textContent = num;
        updateNavigationButtons();
        
    } catch (error) {
        console.error('Error rendering page:', error);
        pageRendering = false;
    }
}

function queueRenderPage(num) {
    if (pageRendering) {
        pageNumPending = num;
    } else {
        renderPage(num);
    }
}

function previousPage() {
    if (pageNum <= 1) return;
    pageNum--;
    queueRenderPage(pageNum);
}

function nextPage() {
    if (pageNum >= pdfDoc.numPages) return;
    pageNum++;
    queueRenderPage(pageNum);
}

function updateNavigationButtons() {
    document.getElementById('prevPageBtn').disabled = pageNum <= 1;
    document.getElementById('nextPageBtn').disabled = pageNum >= pdfDoc.numPages;
}

function handleCanvasClick(event) {
    const rect = canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left) / scale;
    const y = (event.clientY - rect.top) / scale;
    
    // Store signature position with canvas dimensions for accurate conversion server-side
    signaturePositions.push({ x: x, y: y, page: pageNum, canvas_w: canvas.width, canvas_h: canvas.height });
    
    // Show signature marker
    showSignatureMarker(event.clientX - rect.left, event.clientY - rect.top);
    
    // Update UI
    updatePositionInfo();
    
    toastr.success('Position de signature sélectionnée !');
}

function showSignatureMarker(x, y) {
    const marker = document.getElementById('signatureMarker');
    const container = document.querySelector('.pdf-viewer-container');
    
    marker.style.left = (x - 75) + 'px';
    marker.style.top = (y - 25) + 'px';
    marker.classList.remove('d-none');
    
    // Add animation
    marker.style.animation = 'none';
    setTimeout(() => {
        marker.style.animation = 'pulse 1s ease-in-out';
    }, 10);
}

function updatePositionInfo() {
    if (signaturePositions.length === 0) {
        document.getElementById('selectedPage').textContent = '-';
        document.getElementById('selectedPosition').textContent = '-';
        document.getElementById('positionCard').style.display = 'none';
        return;
    }
    const last = signaturePositions[signaturePositions.length - 1];
    document.getElementById('selectedPage').textContent = last.page;
    document.getElementById('selectedPosition').textContent = 
        `X: ${Math.round(last.x)}, Y: ${Math.round(last.y)} (total: ${signaturePositions.length})`;
    
    document.getElementById('positionCard').style.display = 'block';
}

function clearPosition() {
    signaturePositions = [];
    document.getElementById('signatureMarker').classList.add('d-none');
    document.getElementById('positionCard').style.display = 'none';
}

async function applySignature() {
    if (signaturePositions.length === 0) {
        toastr.error('Veuillez sélectionner au moins une position pour la signature.');
        return;
    }
    
    const btn = document.getElementById('applyBtn');
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Application...';
    
    try {
        // ✅ Inclure les dimensions réelles du canvas pour une conversion précise
        const data = {
            position_x: signaturePositions[0].x,
            position_y: signaturePositions[0].y,
            page_number: signaturePositions[0].page,
            canvas_width: canvas?.width || 800,
            canvas_height: canvas?.height || 600
        };

        const response = await fetch(`/signatures/${documentId}/apply`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            toastr.success(result.message);
            setTimeout(() => {
                window.location.href = result.redirect_url;
            }, 1000);
        } else {
            throw new Error(result.message || 'Erreur lors de l\'application');
        }

    } catch (error) {
        console.error('Erreur:', error);
        toastr.error(error.message || 'Erreur lors de l\'application de la signature');
    } finally {
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
    
    if (e.key === 'ArrowLeft') {
        previousPage();
    } else if (e.key === 'ArrowRight') {
        nextPage();
    } else if (e.key === 'Escape') {
        clearPosition();
    }
});
</script>
@endpush

@push('styles')
<style>
.pdf-canvas {
    max-width: 100%;
    height: auto;
}

.signature-marker {
    position: absolute;
    z-index: 1000;
    pointer-events: none;
}

.signature-placeholder {
    background: rgba(0, 123, 255, 0.1);
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #007bff;
    font-weight: 500;
    min-width: 150px;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.current-signer {
    background: rgba(0, 123, 255, 0.1);
    border-radius: 8px;
    padding: 8px;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@media (max-width: 768px) {
    .pdf-canvas {
        max-width: 100%;
        height: auto;
    }
}
</style>
@endpush
@endsection

