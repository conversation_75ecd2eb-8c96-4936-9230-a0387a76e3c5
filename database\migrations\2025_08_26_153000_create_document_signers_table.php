<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('document_signers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('document_id');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('email')->nullable(); // Pour les signataires externes
            $table->string('name')->nullable(); // Pour les signataires externes
            $table->enum('status', ['pending', 'signed', 'declined'])->default('pending');
            $table->integer('signature_order')->default(1); // Ordre de signature
            $table->timestamp('signed_at')->nullable();
            $table->timestamp('expires_at')->nullable(); // Expiration de l'invitation
            $table->string('invitation_token')->unique(); // Token pour l'invitation
            $table->json('signature_position')->nullable(); // Position de signature {x, y, page}
            $table->timestamps();

            $table->foreign('document_id')->references('id')->on('documents')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique(['document_id', 'user_id']);
            $table->index(['document_id', 'status']);
            $table->index(['invitation_token']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('document_signers');
    }
};

