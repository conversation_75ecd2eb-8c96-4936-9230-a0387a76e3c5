<?php

/**
 * Script de test pour la conversion des coordonnées
 * Teste le service CoordinateConversionService
 */

require_once 'vendor/autoload.php';

use App\Services\CoordinateConversionService;

echo "🧪 Test de conversion des coordonnées\n";
echo "====================================\n\n";

// Test 1: Conversion pixels vers millimètres (A4)
echo "=== Test 1: Pixels vers Millimètres (A4) ===\n";
$canvasWidth = 800;
$canvasHeight = 600;
$pdfWidth = 210.0; // A4 largeur
$pdfHeight = 297.0; // A4 hauteur

$testCases = [
    ['x' => 400, 'y' => 300, 'description' => 'Centre du canvas'],
    ['x' => 100, 'y' => 100, 'description' => 'Haut-gauche'],
    ['x' => 700, 'y' => 500, 'description' => 'Bas-droite'],
    ['x' => 0, 'y' => 0, 'description' => 'Origine (0,0)'],
];

foreach ($testCases as $testCase) {
    $result = CoordinateConversionService::pixelsToMillimeters(
        $testCase['x'], 
        $testCase['y'], 
        $canvasWidth, 
        $canvasHeight, 
        $pdfWidth, 
        $pdfHeight
    );
    
    echo "{$testCase['description']}:\n";
    echo "  Pixels: X={$testCase['x']}, Y={$testCase['y']}\n";
    echo "  Millimètres: X={$result['x']}mm, Y={$result['y']}mm\n";
    echo "  Position relative: X=" . round($testCase['x']/$canvasWidth, 3) . ", Y=" . round($testCase['y']/$canvasHeight, 3) . "\n\n";
}

// Test 2: Conversion millimètres vers pixels
echo "=== Test 2: Millimètres vers Pixels ===\n";
$mmCoords = ['x' => 105.0, 'y' => 148.5]; // Centre A4
$result = CoordinateConversionService::millimetersToPixels(
    $mmCoords['x'], 
    $mmCoords['y'], 
    $canvasWidth, 
    $canvasHeight, 
    $pdfWidth, 
    $pdfHeight
);

echo "Centre A4 (105mm, 148.5mm):\n";
echo "  Millimètres: X={$mmCoords['x']}mm, Y={$mmCoords['y']}mm\n";
echo "  Pixels: X={$result['x']}, Y={$result['y']}\n";
echo "  Position relative: X=" . round($mmCoords['x']/$pdfWidth, 3) . ", Y=" . round($mmCoords['y']/$pdfHeight, 3) . "\n\n";

// Test 3: Vérification de la réversibilité
echo "=== Test 3: Vérification Réversibilité ===\n";
$originalPixels = ['x' => 200, 'y' => 150];
$convertedMm = CoordinateConversionService::pixelsToMillimeters(
    $originalPixels['x'], 
    $originalPixels['y'], 
    $canvasWidth, 
    $canvasHeight, 
    $pdfWidth, 
    $pdfHeight
);
$backToPixels = CoordinateConversionService::millimetersToPixels(
    $convertedMm['x'], 
    $convertedMm['y'], 
    $canvasWidth, 
    $canvasHeight, 
    $pdfWidth, 
    $pdfHeight
);

echo "Test de réversibilité:\n";
echo "  Original pixels: X={$originalPixels['x']}, Y={$originalPixels['y']}\n";
echo "  Converti en mm: X={$convertedMm['x']}mm, Y={$convertedMm['y']}mm\n";
echo "  Retour en pixels: X={$backToPixels['x']}, Y={$backToPixels['y']}\n";
echo "  Différence: X=" . abs($originalPixels['x'] - $backToPixels['x']) . ", Y=" . abs($originalPixels['y'] - $backToPixels['y']) . "\n\n";

// Test 4: Positions de signature typiques
echo "=== Test 4: Positions de Signature Typiques ===\n";
$signaturePositions = [
    ['x' => 100, 'y' => 100, 'description' => 'Haut-gauche (signature)'],
    ['x' => 700, 'y' => 100, 'description' => 'Haut-droite (signature)'],
    ['x' => 400, 'y' => 500, 'description' => 'Bas-centre (signature)'],
];

foreach ($signaturePositions as $pos) {
    $result = CoordinateConversionService::pixelsToMillimeters(
        $pos['x'], 
        $pos['y'], 
        $canvasWidth, 
        $canvasHeight, 
        $pdfWidth, 
        $pdfHeight
    );
    
    echo "{$pos['description']}:\n";
    echo "  Canvas: X={$pos['x']}, Y={$pos['y']}\n";
    echo "  PDF: X={$result['x']}mm, Y={$result['y']}mm\n";
    echo "  Position: " . ($result['x'] < $pdfWidth/2 ? 'Gauche' : 'Droite') . ", " . ($result['y'] < $pdfHeight/2 ? 'Haut' : 'Bas') . "\n\n";
}

echo "✅ Tests terminés !\n\n";
echo "📝 Analyse:\n";
echo "- Les coordonnées sont converties de pixels (canvas) vers millimètres (PDF)\n";
echo "- L'axe Y est inversé car PDF commence en haut, canvas en haut\n";
echo "- Les positions relatives (0-1) sont calculées puis converties en mm\n";
echo "- La taille A4 standard est utilisée (210mm x 297mm)\n\n";

echo "🔍 Pour déboguer votre signature:\n";
echo "1. Vérifiez les logs Laravel pour voir la conversion\n";
echo "2. Comparez les coordonnées converties avec la position attendue\n";
echo "3. Ajustez les tailles de canvas si nécessaire\n";
