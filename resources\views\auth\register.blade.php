@extends('layouts.app')

@section('title', 'Inscription - SignSecure')

@section('content')
@push('styles')
<style>
    /* Désactive les effets hover/transition qui peuvent donner un effet de clignotement sur cette page */
    .card:hover { box-shadow: var(--ss-shadow); transform: none; }
    .btn:hover { box-shadow: none; transform: none; }
    .modal, .modal.fade { transition: none !important; }
    .modal-backdrop { transition: none !important; }
    /* Évite les réajustements visuels pendant la lecture des CGU/Privacy */
    body { backface-visibility: hidden; }
    .form-check-label a, .form-text a, .btn-link { text-decoration: underline; }
    /* Empêche toute animation au focus */
    *:focus { transition: none !important; }
    /* Stabilise la scrollbar sur ouverture de modale */
    html { overflow-y: scroll; }
    /* Evite repaint liés au gradient derrière */
    .min-vh-100[style*="linear-gradient"] { will-change: auto; }
}</style>
@endpush
<form method="POST" action="{{ route('register') }}" id="registerForm" class="needs-validation" novalidate>
    @csrf
    
    <div class="mb-3">
        <label for="name" class="form-label fw-semibold">Nom complet</label>
        <div class="input-group">
            <span class="input-group-text bg-light border-end-0">
                <i class="fas fa-user text-muted"></i>
            </span>
            <input 
                type="text" 
                class="form-control border-start-0 @error('name') is-invalid @enderror" 
                id="name" 
                name="name" 
                value="{{ old('name') }}" 
                required 
                autocomplete="name" 
                autofocus
                placeholder="Votre nom complet"
                minlength="2"
                maxlength="255"
            >
            @error('name')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    
    <div class="mb-3">
        <label for="email" class="form-label fw-semibold">Adresse e-mail</label>
        <div class="input-group">
            <span class="input-group-text bg-light border-end-0">
                <i class="fas fa-envelope text-muted"></i>
            </span>
            <input 
                type="email" 
                class="form-control border-start-0 @error('email') is-invalid @enderror" 
                id="email" 
                name="email" 
                value="{{ old('email') }}" 
                required 
                autocomplete="email"
                placeholder="<EMAIL>"
            >
            @error('email')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    
    <div class="mb-3">
        <label for="phone" class="form-label fw-semibold">
            Numéro de téléphone 
            <small class="text-muted">(optionnel, pour SMS OTP)</small>
        </label>
        <div class="input-group">
            <span class="input-group-text bg-light border-end-0">
                <i class="fas fa-phone text-muted"></i>
            </span>
            <input 
                type="tel" 
                class="form-control border-start-0 @error('phone') is-invalid @enderror" 
                id="phone" 
                name="phone" 
                value="{{ old('phone') }}" 
                autocomplete="tel"
                placeholder="+229 01 40 69 15 18"
                pattern="[+]?[0-9\s\-\(\)]+"
            >
            @error('phone')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    
    <div class="mb-3">
        <label for="password" class="form-label fw-semibold">Mot de passe</label>
        <div class="input-group">
            <span class="input-group-text bg-light border-end-0">
                <i class="fas fa-lock text-muted"></i>
            </span>
            <input 
                type="password" 
                class="form-control border-start-0 border-end-0 @error('password') is-invalid @enderror" 
                id="password" 
                name="password" 
                required 
                autocomplete="new-password"
                placeholder="Minimum 8 caractères"
                minlength="8"
                pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,}"
                title="8+ caractères, au moins 1 minuscule, 1 majuscule, 1 chiffre et 1 symbole"
            >
            <button 
                class="btn btn-outline-secondary border-start-0 toggle-password" 
                type="button" 
                data-target="password"
                tabindex="-1"
            >
                <i class="fas fa-eye"></i>
            </button>
            @error('password')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
        <div class="form-text">
            <small class="text-muted">
                Mot de passe fort requis: minimum 8 caractères, lettres (majuscules et minuscules), chiffres et caractères spéciaux.
            </small>
        </div>
    </div>
    
    <div class="mb-4">
        <label for="password_confirmation" class="form-label fw-semibold">Confirmer le mot de passe</label>
        <div class="input-group">
            <span class="input-group-text bg-light border-end-0">
                <i class="fas fa-lock text-muted"></i>
            </span>
            <input 
                type="password" 
                class="form-control border-start-0 border-end-0" 
                id="password_confirmation" 
                name="password_confirmation" 
                required 
                autocomplete="new-password"
                placeholder="Répétez votre mot de passe"
                minlength="8"
                pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,}"
                title="8+ caractères, au moins 1 minuscule, 1 majuscule, 1 chiffre et 1 symbole"
            >
            <button 
                class="btn btn-outline-secondary border-start-0 toggle-password" 
                type="button" 
                data-target="password_confirmation"
                tabindex="-1"
            >
                <i class="fas fa-eye"></i>
            </button>
        </div>
        <div class="mt-2">
            <div id="passwordMatchBar" style="height:6px; border-radius:4px; background-color:#e5e7eb; transition: background-color 0.2s ease;"></div>
        </div>
        <div id="passwordMatch" class="form-text"></div>
    </div>
    
    <div class="mb-4">
        <div class="form-check">
            <input 
                class="form-check-input @error('terms') is-invalid @enderror" 
                type="checkbox" 
                name="terms" 
                id="terms" 
                required
            >
            <label class="form-check-label" for="terms">
                J'accepte les conditions d'utilisation et la politique de confidentialité
            </label>
            @error('terms')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
        <div class="form-text mt-1">
            Lire: 
            <button type="button" id="openTermsLink" class="btn btn-link p-0 align-baseline">conditions d'utilisation</button>
            | 
            <button type="button" id="openPrivacyLink" class="btn btn-link p-0 align-baseline">politique de confidentialité</button>
        </div>
    </div>
    
    <div class="d-grid mb-4">
        <button type="submit" id="registerBtn" class="btn btn-primary btn-lg">
            <i class="fas fa-user-plus me-2"></i>
            Créer mon compte
        </button>
    </div>
    
    <div class="text-center">
        <p class="text-muted mb-2">Déjà un compte ?</p>
        <a href="{{ route('login') }}" class="btn btn-outline-primary">
            <i class="fas fa-sign-in-alt me-2"></i>
            Se connecter
        </a>
    </div>
</form>

<!-- Terms Modal -->
<div class="modal" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">Conditions d'utilisation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Acceptation des conditions</h6>
                <p>En utilisant SignSecure, vous acceptez d'être lié par ces conditions d'utilisation.</p>
                
                <h6>2. Description du service</h6>
                <p>SignSecure est une plateforme de signature électronique sécurisée permettant de signer des documents PDF de manière légale et authentifiée.</p>
                
                <h6>3. Responsabilités de l'utilisateur</h6>
                <p>Vous êtes responsable de la confidentialité de vos identifiants de connexion et de toutes les activités effectuées sous votre compte.</p>
                
                <h6>4. Sécurité des données</h6>
                <p>Nous nous engageons à protéger vos données personnelles et vos documents selon les standards de sécurité les plus élevés.</p>
                
                <h6>5. Valeur légale des signatures</h6>
                <p>Les signatures électroniques créées via SignSecure ont la même valeur légale que les signatures manuscrites conformément à la réglementation en vigueur.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="privacyModalLabel">Politique de confidentialité</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Collecte des données</h6>
                <p>Nous collectons uniquement les données nécessaires au fonctionnement du service : nom, e-mail, numéro de téléphone (optionnel) et signature électronique.</p>
                
                <h6>2. Utilisation des données</h6>
                <p>Vos données sont utilisées exclusivement pour fournir le service de signature électronique et assurer la sécurité de votre compte.</p>
                
                <h6>3. Partage des données</h6>
                <p>Nous ne partageons jamais vos données personnelles avec des tiers sans votre consentement explicite.</p>
                
                <h6>4. Stockage et sécurité</h6>
                <p>Vos données sont stockées de manière sécurisée avec chiffrement et accès restreint.</p>
                
                <h6>5. Vos droits</h6>
                <p>Vous avez le droit d'accéder, modifier ou supprimer vos données personnelles à tout moment.</p>
                
                <h6>6. Contact</h6>
                <p>Pour toute question concernant vos données, contactez-nous à <EMAIL></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push("scripts")
<script src="{{ asset("js/config.js") }}"></script>
<script src="{{ asset("js/api.js") }}"></script>
<script src="{{ asset("js/register.js") }}"></script>
@endpush

