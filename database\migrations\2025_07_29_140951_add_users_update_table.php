<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            
             $table->longText("signature_image")->nullable(); // Base64 de l'image de signature
            $table->string("signature_path")->nullable(); // Chemin de stockage de l'image de signature
          
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};