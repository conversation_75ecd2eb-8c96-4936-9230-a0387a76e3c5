<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class DocumentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        try {
            // Log de débogage pour identifier le problème
            Log::info('DocumentResource::toArray appelé', [
                'resource_type' => get_class($this->resource),
                'resource_id' => $this->resource->id ?? 'NULL',
                'resource_attributes' => method_exists($this->resource, 'getAttributes') ? $this->resource->getAttributes() : 'N/A',
                'original_filename_exists' => isset($this->resource->original_filename),
                'original_filename_value' => $this->resource->original_filename ?? 'NULL'
            ]);

            // Vérifier que le resource est bien un modèle Document
            if (!$this->resource || !method_exists($this->resource, 'getAttributes')) {
                Log::error('DocumentResource: resource invalide', [
                    'resource' => $this->resource,
                    'resource_type' => gettype($this->resource)
                ]);
                throw new \Exception('Resource invalide dans DocumentResource');
            }

            $result = [
                'id' => $this->resource->id ?? 0,
                'original_filename' => $this->resource->original_filename ?? 'Document sans nom',
                'stored_filename' => $this->resource->stored_filename ?? '',
                'file_path' => $this->resource->file_path ?? '',
                'tracking_code' => $this->resource->tracking_code ?? '',
                'file_size' => $this->resource->file_size ?? 0,
                'file_size_human' => $this->resource->file_size ? $this->formatBytes($this->resource->file_size) : '0 B',
                'mime_type' => $this->resource->mime_type ?? 'application/pdf',
                'pages_count' => $this->resource->pages_count ?? 1,
                'status' => $this->resource->status ?? 'pending',
                'is_signed' => $this->resource->is_signed ?? false,
                'uploaded_at' => $this->resource->created_at?->toISOString(),
                'signed_at' => $this->resource->signed_at?->toISOString(),
                'signed_file_path' => $this->resource->signed_file_path ?? null,
            ];

            // Ajouter les relations seulement si elles sont chargées
            if ($this->resource->relationLoaded('user')) {
                try {
                    $result['user'] = new UserResource($this->resource->user);
                } catch (\Exception $e) {
                    Log::warning('Erreur lors du chargement de la relation user', ['error' => $e->getMessage()]);
                    $result['user'] = null;
                }
            }

            if ($this->resource->relationLoaded('signatures')) {
                try {
                    $result['signatures'] = SignatureResource::collection($this->resource->signatures);
                } catch (\Exception $e) {
                    Log::warning('Erreur lors du chargement de la relation signatures', ['error' => $e->getMessage()]);
                    $result['signatures'] = [];
                }
            }

            // Ajouter les URLs seulement si possible
            try {
                $result['download_url'] = $this->when(
                    $this->resource->is_signed && $this->resource->signed_file_path,
                    route('api.documents.download', $this->resource->id)
                );
                $result['preview_url'] = route('api.documents.preview', $this->resource->id);
            } catch (\Exception $e) {
                Log::warning('Erreur lors de la génération des URLs', ['error' => $e->getMessage()]);
                $result['download_url'] = null;
                $result['preview_url'] = null;
            }

            Log::info('DocumentResource::toArray terminé avec succès', [
                'result_keys' => array_keys($result)
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Erreur dans DocumentResource::toArray', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'resource' => $this->resource ?? 'NULL'
            ]);
            
            // Retourner une structure minimale en cas d'erreur
            return [
                'id' => $this->resource->id ?? 0,
                'original_filename' => 'Erreur de chargement',
                'error' => 'Erreur lors du traitement du document'
            ];
        }
    }

    /**
     * Format file size in human readable format
     *
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes <= 0) return '0 B';
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $pow = floor(log($bytes, 1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}