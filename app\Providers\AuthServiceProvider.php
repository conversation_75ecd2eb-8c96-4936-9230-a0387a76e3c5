<?php

namespace App\Providers;

use App\Models\Document;
use App\Models\Signature;
use App\Models\PhysicalRetrace;
use App\Policies\DocumentPolicy;
use App\Policies\SignaturePolicy;
use App\Policies\PhysicalRetracePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Document::class => DocumentPolicy::class,
        Signature::class => SignaturePolicy::class,
        PhysicalRetrace::class => PhysicalRetracePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        //
    }
}