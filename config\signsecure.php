<?php

return [

    /*
    |--------------------------------------------------------------------------
    | SignSecure Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration pour le système de signature électronique SignSecure
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Configuration des OTP
    |--------------------------------------------------------------------------
    */
    'otp' => [
        'expiration_minutes' => env('SIGNSECURE_OTP_EXPIRATION', 10),
        'max_attempts' => env('SIGNSECURE_MAX_OTP_ATTEMPTS', 3),
        'lockout_minutes' => env('SIGNSECURE_OTP_LOCKOUT', 15),
        'code_length' => env('SIGNSECURE_OTP_LENGTH', 6),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des signatures
    |--------------------------------------------------------------------------
    */
    'signature' => [
        'max_age_days' => env('SIGNSECURE_SIGNATURE_MAX_AGE', 365),
        'min_size_bytes' => env('SIGNSECURE_SIGNATURE_MIN_SIZE', 1024),
        'max_size_bytes' => env('SIGNSECURE_SIGNATURE_MAX_SIZE', 1048576), // 1MB
        'allowed_formats' => ['png', 'jpeg', 'jpg'],
        'default_width_mm' => env('SIGNSECURE_SIGNATURE_WIDTH', 50),
        'default_height_mm' => env('SIGNSECURE_SIGNATURE_HEIGHT', 25),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des documents
    |--------------------------------------------------------------------------
    */
    'document' => [
        'max_size_mb' => env('SIGNSECURE_MAX_FILE_SIZE', 10),
        'allowed_mime_types' => ['application/pdf'],
        'storage_disk' => env('SIGNSECURE_STORAGE_DISK', 'private'),
        'tracking_code_length' => env('SIGNSECURE_TRACKING_LENGTH', 16),
        'expiration_days' => env('SIGNSECURE_DOCUMENT_EXPIRATION', 2555), // 7 ans
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de la sécurité
    |--------------------------------------------------------------------------
    */
    'security' => [
        'hash_algorithm' => env('SIGNSECURE_HASH_ALGO', 'sha256'),
        'require_file_integrity' => env('SIGNSECURE_REQUIRE_INTEGRITY', true),
        'log_verifications' => env('SIGNSECURE_LOG_VERIFICATIONS', true),
        'max_verification_attempts' => env('SIGNSECURE_MAX_VERIFICATION_ATTEMPTS', 10),
        'verification_lockout_minutes' => env('SIGNSECURE_VERIFICATION_LOCKOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des notifications
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'email' => [
            'enabled' => env('SIGNSECURE_EMAIL_ENABLED', true),
            'from_address' => env('SIGNSECURE_EMAIL_FROM', '<EMAIL>'),
            'from_name' => env('SIGNSECURE_EMAIL_FROM_NAME', 'SignSecure'),
        ],
        'sms' => [
            'enabled' => env('SIGNSECURE_SMS_ENABLED', false),
            'provider' => env('SIGNSECURE_SMS_PROVIDER', 'twilio'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de la vérification
    |--------------------------------------------------------------------------
    */
    'verification' => [
        'public_url' => env('SIGNSECURE_VERIFICATION_URL', '/verify'),
        'require_captcha' => env('SIGNSECURE_REQUIRE_CAPTCHA', false),
        'captcha_provider' => env('SIGNSECURE_CAPTCHA_PROVIDER', 'recaptcha'),
        'max_verifications_per_hour' => env('SIGNSECURE_MAX_VERIFICATIONS_PER_HOUR', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des métadonnées
    |--------------------------------------------------------------------------
    */
    'metadata' => [
        'extract_pdf_info' => env('SIGNSECURE_EXTRACT_PDF_INFO', true),
        'store_user_agent' => env('SIGNSECURE_STORE_USER_AGENT', true),
        'store_ip_address' => env('SIGNSECURE_STORE_IP_ADDRESS', true),
        'store_geolocation' => env('SIGNSECURE_STORE_GEOLOCATION', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des rapports
    |--------------------------------------------------------------------------
    */
    'reports' => [
        'enable_audit_trail' => env('SIGNSECURE_ENABLE_AUDIT_TRAIL', true),
        'retention_days' => env('SIGNSECURE_RETENTION_DAYS', 2555), // 7 ans
        'export_formats' => ['pdf', 'csv', 'json'],
        'include_signature_images' => env('SIGNSECURE_INCLUDE_SIGNATURES', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de l'API
    |--------------------------------------------------------------------------
    */
    'api' => [
        'url' =>  env('SIGNSECURE_API_URL', 'http://127.0.0.1/api/v1')
        
    ],


];
