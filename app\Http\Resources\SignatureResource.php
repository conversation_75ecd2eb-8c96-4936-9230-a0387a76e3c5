<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SignatureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'position' => [
                'x' => $this->position_x,
                'y' => $this->position_y,
                'page' => $this->page_number,
            ],
            'signature_image' => $this->signature_image, // Base64 de l'image de signature
            'applied_at' => $this->applied_at->toISOString(),
            'user' => new UserResource($this->whenLoaded('user')),
            'document' => $this->when(
                $this->relationLoaded('document'),
                function () {
                    return [
                        'id' => $this->document->id,
                        'filename' => $this->document->original_filename,
                        'tracking_code' => $this->document->tracking_code,
                    ];
                }
            ),
        ];
    }
}
